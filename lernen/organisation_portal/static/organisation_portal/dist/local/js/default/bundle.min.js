var sidebarFull=200,sidebarCollapsed=80,LOGO_COLOR="#43a2ad",OLD_STUDENTS_FEMALE_COLOR="#bfe9ef",PAST_DAYS=1825;function registerSidebarMenu(){registerHomeMenu(),registerChangePassword(),menuLoader.registerSidebarMenu()}function registerHomeMenu(){$("#homeNav").on("click",function(){loadHomePage()})}function registerChangePassword(){$("#changePasswordNav").on("click",function(){loadChangePasswordPage()})}function loadHomePage(){ajaxClient.get("/organisation-portal/home",function(e){$("#main-content").html(e),$("input#stats-date").daterangepicker({autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},onSelect:function(e){$(this).change()}}).on("change",function(){homePage.refreshHomePage()}),homePage.displayDashboardContent(),academicSessionHandler.bindSessionChangeEvent(homePage.refreshHomePage)})}$(document).ready(function(){registerSidebarMenu(),$("#sidebar").addClass("collapsed"),registerChangePassword(),$("#bell-notification-li").css("display","none")});var homePageV2={loadHomePage:function(){ajaxClient.get("/organisation-portal/home/<USER>",function(e){$("#main-content").html(e),$("input#stats-date-range").daterangepicker({autoApply:!1,showDropdowns:!0,startDate:moment().startOf("day"),endDate:moment().startOf("day"),minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]}}).on("change",function(){homePageV2.loadWidgets()})})},loadWidget:function(e,t,a){for(var n in e)console.log("displaying loader for : "+n),homePageV2.displayWidgetLoader(e[n]);ajaxClient.get(t,function(t){for(var n in a(e,t),e)homePageV2.hideWidgetLoader(e[n])},!0)},loadWidgets:function(){homePageV2.loadWidget({studentAttendanceWidgetClass:".student-attendance-stats",studentCountWidgetClass:".student-count-stats"},"/organisation-portal/stats/v2/student-attendance",homePageV2.renderStudentAttendanceAndMetadata),homePageV2.loadWidget({staffCountWidgetClass:".staff-count-stats",staffAttendanceWidgetClass:".staff-attendance-stats"},"/organisation-portal/stats/v2/staff-attendance",homePageV2.renderStaffAttendance),homePageV2.loadWidget({studentAdmissionWidgetClass:".student-admission-stats"},"/organisation-portal/stats/v2/student-admission",homePageV2.renderStudentAdmissionStats),homePageV2.loadWidget({totalFeeCollectionWidgetClass:".fee-collection-stats"},"/organisation-portal/stats/v2/fee-collection",homePageV2.renderFeeCollectionStats)},displayWidgetLoader:function(e){$(e).find(".stats-widget-loader").attr("style","display:block;"),$(e).find(".stats-widget-content").attr("style","display:none;")},hideWidgetLoader:function(e){$(e).find(".stats-widget-loader").attr("style","display:none;"),$(e).find(".stats-widget-content").attr("style","display:block;")},renderStudentAttendanceAndMetadata:function(e,t){var a=e.studentAttendanceWidgetClass,n=e.studentCountWidgetClass;$(n).find(".stats-widget-content").text(t.totalStudent);studentAttendanceCounts=t.studentAttendanceCounts[0];for(var o=0;o<studentAttendanceCounts.attendanceCounts.length;o++)"PRESENT"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount),"LEAVE"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount);const s=$('\n        <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">\n          <div class="carousel-inner"></div>\n          <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">\n            \n            <span aria-hidden="true"\n                  style="display:inline-block;width:2rem;height:2rem;background-repeat:no-repeat;background-size:100% 100%;\n                        background-image: url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="black" viewBox="0 0 8 8"><path d="M5.5 0L4.086 1.414 6.672 4 4.086 6.586 5.5 8l4-4-4-4z"/></svg>\');">\n            </span>\n            <span class="sr-only">Previous</span>\n          </a>\n          <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">\n            <span class="carousel-control-next-icon" aria-hidden="true" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M2.75 0L4.18 1.41 1.59 4 4.18 6.59 2.75 8 -1.25 4z\'/%3E%3C/svg%3E\');"></span>\n            <span class="sr-only">Next</span>\n          </a>\n        </div>\n      '),r=s.find(".carousel-inner");console.log("========================"),t.studentAttendanceCounts.forEach((e,t)=>{var a=e.attendanceType;console.log(a);const n=e.attendanceCounts.find(e=>"PRESENT"===e.attendanceStatus),o=e.attendanceCounts.find(e=>"LEAVE"===e.attendanceStatus);console.log(n),console.log(o);const s=(n.count/n.total*100).toFixed(1),d=$(`\n          <div class="carousel-item ${0===t?"active":""}">\n            <div class="card-body py-4">\n              <div class="media">\n                <div class="media-body">\n                  <h4 class="mb-2 stats-widget-content">${n.count} / ${n.total} (${s}%)</h4>\n                  <p class="mb-2">Student Attendance</p>\n                  <div class="mb-0 mt-3" >\n                    <span class="badge badge-soft-success" >\n                      <i class="mdi mdi-arrow-bottom-right"></i>\n                      ${a}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        `);console.log(d),r.append(d)}),console.log(r),$(a).find(".stats-widget-content").html(s),window.feather&&feather.replace()},renderStaffAttendance:function(e,t){var a=e.staffAttendanceWidgetClass,n=e.staffCountWidgetClass,o=0,s=0,r=0;if(t.staffAttendanceCounts&&t.staffAttendanceCounts.length>0){var d=t.staffAttendanceCounts[0].attendanceCounts,i=d.find(e=>"PRESENT"===e.attendanceStatus);i&&(o=i.count,r=i.total);var l=d.find(e=>"LEAVE"===e.attendanceStatus);l&&(s=l.count)}$(n).find(".stats-widget-content").text(r),$(a).find(".stats-widget-content-staff-present").text(o),$(a).find(".stats-widget-content-staff-leave").text(s)},renderStudentAdmissionStats:function(e,t){console.log(t),console.log("-----------------------");var a=e.studentAdmissionWidgetClass;$(a).find(".stats-widget-content-admission").text(t.totalNewAdmissions),$(a).find(".stats-widget-content-tc").text(t.totalTCIssued)},renderFeeCollectionStats:function(e,t){var a=e.totalFeeCollectionWidgetClass;$(a).find(".stats-widget-content").text(t.totalAmount+"/-")}},homePage={refreshHomePage:function(){var e=academicSessionHandler.getSelectedSessionId(),t=getDate($("input#stats-date").val()).getTime()/1e3;ajaxClient.get("/organisation-portal/home-date-session-change/"+t+"/"+e,function(e){$("#attendance-dashboard-session-content").html(e),homePage.displayDashboardContent()})},displayDashboardContent:function(){var e=readJson("#home-page-stats"),t=[],a=[],n=[],o=[],s={},r=0,d=[],i=0,l=[],c=[],u=[],g=[],h=[],f=[],m=[];console.log("test");var p=[],C=0;for(instituteId in e){var b=e[instituteId];institute=b.institute;var v=institute.branchName,w=institute.instituteUniqueCode;null!=v&&""!=v||(v=institute.instituteName),t.push(v);var P=w+":"+v;n.push(P),a.push(b.total_students);var y=b.attendance_stats,A=0;null!=y&&(A=parseFloat(y.totalPresentAttendance.toFixed(2))),r+=A,d.push(parseFloat(A.toFixed(2)));var S=b.transport_stats,x=0;null!=S&&(x=S.totalTransportAssignedStudentCount),l.push(x),i+=x;var O=b.staff_stats;if(null!=O){C+=O.todayTotalPresentStaff,p.push(O.todayTotalPresentStaff);var L=O.staffGenderWiseCount;if(null!=L)for(var $=Object.keys(L),k=0;k<$.length;k++){var R=$[k];"MALE"===R?c.push(L[R]):"FEMALE"===R&&u.push(L[R])}}var F=b.class_payment_stats;if(null!=F){g.push(parseFloat(F.assignedAmount.toFixed(2))),h.push(parseFloat(F.collectedAmount.toFixed(2))),f.push(parseFloat(F.discountAmount.toFixed(2))),m.push(parseFloat(F.dueAmount.toFixed(2)));var E=F.feeHeadCollectedAmountMap;for(const[e,t]of Object.entries(E))s[e]||(o.push(e),s[e]={}),s[e][P]||(s[e][P]=0),s[e][P]+=t}else g.push(0),h.push(0),f.push(0),m.push(0)}homePage.renderStudentCountChart(t,a),homePage.renderPresentAttendancePieChart(t,d,r),homePage.renderStudentFeesCountChart(t,g,h,f,m),homePage.renderStudentFeeHeadsCountChart(o,n,s),homePage.renderTransportAssignedPieChart(t,l,i),homePage.renderStaffGenderCountChart(t,c,u),homePage.renderStaffTodayAttendanceChart(t,p,C)},renderStudentCountChart:function(e,t){new Chart($("#chartjs-student-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Student Count",backgroundColor:PRIMARY_LOGO_COLOR,borderColor:PRIMARY_LOGO_COLOR,hoverBackgroundColor:PRIMARY_LOGO_COLOR,hoverBorderColor:PRIMARY_LOGO_COLOR,data:t,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]},animation:{onComplete:function(){var e=this.chart,t=e.ctx;t.textAlign="center",t.fillStyle="rgba(0, 0, 0, 1)",t.textBaseline="bottom",this.data.datasets.forEach(function(a,n){e.controller.getDatasetMeta(n).data.forEach(function(e,n){var o=a.data[n];t.fillText(o,e._model.x,e._model.y-5)})})}},events:[]}})},renderPresentAttendancePieChart:function(e,t,a){new Chart($("#chartjs-present-attendance-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderTransportAssignedPieChart:function(e,t,a){new Chart($("#chartjs-active-transport-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderStaffGenderCountChart:function(e,t,a){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20};var n=new Chart($("#chartjs-genderwise-staff-bar-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Male Staff",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:t,barPercentage:.325,categoryPercentage:.5},{label:"Female Staff",backgroundColor:OLD_STUDENTS_FEMALE_COLOR,borderColor:OLD_STUDENTS_FEMALE_COLOR,hoverBackgroundColor:OLD_STUDENTS_FEMALE_COLOR,hoverBorderColor:OLD_STUDENTS_FEMALE_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}});n.canvas.parentNode.style.height="311px",n.canvas.parentNode.style.width="311px"},renderStudentFeesCountChart:function(e,t,a,n,o){new Chart($("#chartjs-institute-fee-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:o,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStudentFeeHeadsCountChart:function(e,t,a){const n=["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],o=t.map((t,o)=>{const s=n[o%n.length];return{label:t.split(":")[1],backgroundColor:s,borderColor:s,hoverBackgroundColor:s,hoverBorderColor:s,data:e.map(e=>a[e][t]||0),barPercentage:.325,categoryPercentage:.5}});new Chart($("#chartjs-institute-fee-head-distribution"),{type:"bar",data:{labels:e,datasets:o},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStaffTodayAttendanceChart:function(e,t,a){new Chart($("#chartjs-staff-present-attendance-pie"),{type:"doughnut",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderColor:"transparent"}]},options:{rotation:1*Math.PI,circumference:1*Math.PI,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},generateBirthdayCertificate:function(e){var t=JSON.parse($(e).find(".student-info").text()),a=t.instituteId,n=t.studentAcademicSessionInfoResponse.academicSession.academicSessionId,o=t.studentId;window.open(baseURL+"/organisation-portal/generate-birthday-certificate/"+a+"/"+n+"/"+o,"_blank")}};function loadChangePasswordPage(){ajaxClient.get("/organisation-portal/change-password",function(e){$("#main-content").html(e)})}function changePassword(){var e=$("#old-password").val(),t=$("#new-password").val(),a={oldPassword:e,newPassword:t};t==$("#confirm-new-password").val()?ajaxClient.post("/organisation-portal/update-password",{changePasswordInfo:JSON.stringify(a)},function(e){$("#change-password\\.status-modal-container").html(e),$("#change-password-status-modal").modal("toggle"),$("#old-password").val(""),$("#new-password").val(""),$("#confirm-new-password").val("")}):showErrorDialogBox("Password don't match!!")}function formatINRCurrency(e){return null==e||null==e||""==e?0:e=e.toLocaleString("en-IN")}function viewStatistics(){loadHomePage(),$("#sidebar").removeClass("collapsed")}function viewStatisticsV2(){homePageV2.loadHomePage(),$("#sidebar").removeClass("collapsed")}var menuLoader={registerSidebarMenu:function(){menuLoader.registerFeesReportsMenu()},registerFeesReportsMenu:function(){$("#orgFeesReportNav").on("click",function(){orgFeesReport.loadHomePage()})}},orgFeesReport={dataCache:{},loadHomePage:function(){ajaxClient.get("/organisation-portal/fees-reports",function(e){$("#main-content").html(e),initDateWithYearRange("-5:+5",!0),initSelect2("All"),orgFeesReport.initDataCache(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),orgFeesReport.bindGenerateReportEvent(),reportUtils.bindSelectClassCheckboxEvent()})},initDataCache:function(){var e=readJson("#all-sessions"),t=readJson("#selected-academic-session-json");orgFeesReport.dataCache.allSessions=e,$(".report-academic-session").val(t.academicSessionId)},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container");reportUtils.getReportHeadersCSV(e);if(!validateMandatoryFields($(e))){var t="";$(e).find(".report-academic-session option:selected").length>0&&(t=$(e).find(".report-academic-session option:selected").val()),t=""===t?0:t;var a=$(e).find("p.report-type").text().trim(),n="";$(e).find(".reports-student-status").length>0&&(n=$(e).find(".reports-student-status").val().join()),$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/organisation-portal/fees-generate-report/"+a+"?academic_session_id="+t+"&studentStatus="+n,"_blank")}})}};