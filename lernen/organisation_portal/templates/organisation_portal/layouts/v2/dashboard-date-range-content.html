{% load json %}
{% load currency_utils %}
{% load displaytime %}
{% load displayPercentage %}
{% load round_to_decimals %}
{% load math_utils %}
{% load string_utils %}
<p style="display:none;" id="home-page-stats">{{institute_details_map|jsonstr}}</p>
<div class="row">
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <div class="student-count-stats">
                {% include 'core/utils/widget_loader.html' %}
                <h3 class="mb-2 stats-widget-content"></h3>
                <p class="mb-2">Enrolled Students</p>
              </div>
              <div class="student-admission-stats">
                  {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %}
                  <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-admission"></span>
                    </span>
                    <span class="text-muted">Admissions</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-tc"></span>
                    </span>
                    <span class="text-muted">TC Issued</span>
                  </div>
              </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <div class="staff-count-stats">
                  {% include 'core/utils/widget_loader.html' %}
                  <h3 class="mb-2 stats-widget-content"></h3>
                  <p class="mb-2">Total Staff</p>
              </div>
              <div class="staff-attendance-stats">
                  {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %}
                  <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-present"></span>
                    </span>
                    <span class="text-muted">Present</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-leave"></span>
                    </span>
                    <span class="text-muted">Leave</span>
                  </div>
              </div>
             
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex student-attendance-stats">
        <div class="card flex-fill">
          {% include 'core/utils/widget_loader.html' %}
          <div class="stats-widget-content"></div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <div class="fee-collection-stats">
                  {% include 'core/utils/widget_loader.html' %}
                  <h3 class="mb-2 stats-widget-content"></h3>
                  <p class="mb-2">Total Fees Collection</p>
              </div>
              <!-- <div class="staff-attendance-stats">
                  {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %}
                  <div class="mb-0 mt-3 stats-widget-content" style="display: block;" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-present"></span>
                    </span>
                    <span class="text-muted">Present</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-leave"></span>
                    </span>
                    <span class="text-muted">Leave</span>
                  </div>
              </div> -->
             
            </div>
          </div>
        </div>
      </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
              <h5 class="card-title mb-0">Student Distribution</h5>
            </div>
            <div class="card-body d-flex">
                <div class="align-self-center w-100">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-student-count-pie"></canvas>
                        </div>
                    </div>
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th>Branches</th>
                                <th class="text-center">Present/Total</th>
                                <th class="text-center">Present(%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for institute_unique_key, institute_stats in institute_details_map.items %}
                            <tr>
                                <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                                <td class="text-center">{% if institute_stats.attendance_stats %}{{institute_stats.attendance_stats.totalPresentAttendance|to_2_decimals_without_traling_zeros}}/{{institute_stats.attendance_stats.totalAttendance|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                                <td class="text-center">{% if institute_stats.attendance_stats %}{{institute_stats.attendance_stats.totalPresentPercentage|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                            </tr>
                            {% endfor %}
                            <tr style="font-weight: bold;">
                                <td>TOTAL</td>
                                <td class="text-center">{% if total_present_today %}{{total_present_today|to_2_decimals_without_traling_zeros}}/{{total_enrolled_student|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                                <td class="text-center">{% if present_percentage %}{{present_percentage|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <h5 class="card-title mb-0">Student Distribution</h5>
            </div>
            <div class="card-body d-flex w-100">
                <div class="align-self-center chart chart-lg">
                    <canvas id="chartjs-student-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
              <h5 class="card-title mb-0">Student Attendance (On Date)</h5>
            </div>
            <div class="card-body d-flex">
                <div class="align-self-center w-100">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-present-attendance-pie"></canvas>
                        </div>
                    </div>
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th>Branches</th>
                                <th class="text-center">Present/Total</th>
                                <th class="text-center">Present(%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for institute_unique_key, institute_stats in institute_details_map.items %}
                            <tr>
                                <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                                <td class="text-center">{% if institute_stats.attendance_stats %}{{institute_stats.attendance_stats.totalPresentAttendance|to_2_decimals_without_traling_zeros}}/{{institute_stats.attendance_stats.totalAttendance|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                                <td class="text-center">{% if institute_stats.attendance_stats %}{{institute_stats.attendance_stats.totalPresentPercentage|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                            </tr>
                            {% endfor %}
                            <tr style="font-weight: bold;">
                                <td>TOTAL</td>
                                <td class="text-center">{% if total_present_today %}{{total_present_today|to_2_decimals_without_traling_zeros}}/{{total_enrolled_student|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                                <td class="text-center">{% if present_percentage %}{{present_percentage|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-12 col-lg-8 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <h5 class="card-title mb-0">Fees Collection</h5>
            </div>
            <div class="card-body d-flex w-100">
                <div class="align-self-center chart chart-lg" {% if not view_fees_stats  %} style="display:none;" {% endif %}>
                    <canvas id="chartjs-institute-fee-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
              <h5 class="card-title mb-0">Employee/Staff Attendance</h5>
            </div>
            <div class="card-body d-flex">
                <div class="align-self-center w-100 text-center">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-staff-present-attendance-pie"></canvas>
                        </div>
                    </div>
                    <table class="table mb-0">
                      <thead>
                          <tr>
                              <th>Branches</th>
                              <th class="text-center">Present/Total</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for institute_unique_key, institute_stats in institute_details_map.items %}
                          <tr>
                              <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                              <td class="text-center">{% if institute_stats.staff_stats %}{{institute_stats.staff_stats.todayTotalPresentStaff|to_2_decimals_without_traling_zeros}}/{{institute_stats.staff_stats.totalOnboardStaff|to_2_decimals_without_traling_zeros}}{% else %}-{% endif %}</td>
                          </tr>
                          {% endfor %}
                      </tbody>
                  </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
  <div class="col-12 col-lg-12 d-flex">
      <div class="card flex-fill w-100">
          <div class="card-header">
              <h5 class="card-title mb-0">Fee Heads Collection</h5>
          </div>
          <div class="card-body d-flex w-100">
              <div class="align-self-center chart chart-lg" {% if not view_fees_stats  %} style="display:none;" {% endif %}>
                  <canvas id="chartjs-institute-fee-head-distribution"></canvas>
              </div>
          </div>
      </div>
  </div>
</div>

<div class="row">
  <div class="col-12 col-lg-4 d-flex">
    <div class="card flex-fill w-100 pl-0 pr-0">
      <div class="card-header">
        <h5 class="card-title mb-0" id="new-admission-graph-header-text">Student Birthdays</h5>
    </div>
      <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-after-enrollment-structure-tab" data-toggle="tab" href="#nav-after-enrollment-structure" role="tab" aria-controls="nav-home" aria-selected="true">Today</a>
          <a class="nav-item nav-link" id="nav-before-registration-structure-tab" data-toggle="tab" href="#before-registration-structure" role="tab" aria-controls="nav-profile" aria-selected="false">Upcoming</a>
        </div>
      </nav>
      <br/>
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-after-enrollment-structure" role="tabpanel" aria-labelledby="nav-after-enrollment-structure-tab">
              <div style="display:block;overflow-y: auto;max-height: 385px;">
              {% for institute_unique_key, institute_stats in institute_details_map.items %}
              {% if institute_stats.today_birthday %}
              <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
              <div class="d-flex">
                <div class="w-100">
                  <div class="mb-0">
                    {% for student in institute_stats.today_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                    </div>
                  </div>
                </div>
              {% endif %}
              {% endfor %}
              </div>
          </div>
          <div class="tab-pane fade" id="before-registration-structure" role="tabpanel" aria-labelledby="nav-before-registration-structure-tab">
            <div style="display:block;overflow-y: auto;max-height: 385px;">
            {% for institute_unique_key, institute_stats in institute_details_map.items %}
            {% if institute_stats.upcoming_birthday %}
            <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
            <div class="d-flex">
              <div class="w-100">
                <div class="mb-0">
                    {% for student in institute_stats.upcoming_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
          </div>
          {% endif %}
          {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>

<div class="col-12 col-lg-4 d-flex">
  <div class="card flex-fill w-100">
      <div class="card-header">
          <h5 class="card-title mb-0" id="new-admission-graph-header-text">Staff Gender Wise Distribution</h5>
      </div>
      <div class="card-body d-flex">
          <div class="w-100">
              <div class="py-3">
                  <div id="tabularview-new-admission-student-distribution" style="display:block;overflow-y: auto;max-height: 450px;"></div>
                  <div class="chart chart-xs" id="chartjs-new-admission">
                    <div id="percentage-change-new-admission" style="float:right;"></div>
                    <canvas id="chartjs-genderwise-staff-bar-distribution"></canvas>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>

  <div class="col-12 col-lg-4 d-flex">
    <div class="card flex-fill w-100">
        <div class="card-header">
          <h5 class="card-title mb-0">Active Transport Students</h5>
        </div>
        <div class="card-body d-flex">
            <div class="align-self-center w-100">
                <div class="py-3">
                    <div class="chart chart-xs">
                      <canvas id="chartjs-active-transport-pie"></canvas>
                    </div>
                </div>
                <table class="table mb-0">
                    <thead>
                        <tr>
                            <th>Branches</th>
                            <th class="text-center">Transport/Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for institute_unique_key, institute_stats in institute_details_map.items %}
                        <tr>
                            <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                            <td class="text-center">{% if institute_stats.transport_stats %}{{institute_stats.transport_stats.totalTransportAssignedStudentCount}}/{{institute_stats.transport_stats.totalEnrolledStudentCount}}{% else %}-{% endif %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
  </div>
</div>
