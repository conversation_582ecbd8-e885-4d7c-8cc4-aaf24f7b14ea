from time import time
from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from core.controller.user.notification_manager import *
from core.controller.utils.tracking_events import *
from core.controller.client.institute_payment_manager import *
from fees.controller.fee_payment_manager import *
from core.controller.utils.utility import *
from core.controller.utils.authorised_actions import *
from .controller.organisation_manager import *
from math import *
import math
import time
import datetime
from datetime import date
from datetime import datetime



COLOR_LIST = ["#2e7078", "#43a2ad", "#7fadb2", "#a5d4d9", "#afdbe0", "#e7f9fb"]

def get_current_time():
	return math.trunc(time.time());

def dashboard_view(request):
	if authorized_organisation_portal(request):
		track_event(request, None, {"channel" : "WEB","trackingEventName" : "ORGANISATION_PORTAL_LOADED"})
		user_login_view = get_user_login_view(request)
		user_type = user_login_view['user']['userType']
		institute_scope = user_login_view['user']['instituteScope']
		institute_rows = []
		count = 0
		rows = 0
		for institute in user_login_view['organisation']['institutes']:
			if institute['instituteId'] in institute_scope:
				institute_unique_code = institute['instituteUniqueCode']
				institute['portal_path'] = get_user_portal_path(user_type, institute_unique_code)
				if(count % 2 == 0):
					institute_rows.append([])
					rows +=1
				institute_row = institute_rows[rows-1]
				institute_row.append(institute)
				count += 1
		tutorial_videos = get_tutorial_video_details('ORGANISATION_PORTAL', institute_unique_code, user_type)
		return render(request, 'organisation_portal/layouts/default-organisation-portal-layout.html', {'dashboard_theme' : get_user_theme(request), 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'institute_rows' : institute_rows, 'user_login_view' : user_login_view, 'tutorial_videos': tutorial_videos })

def home_page_view_v2(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)

		institute_scope = user_login_view['user']['instituteScope']
		institute_unique_code = None
		for institute in user_login_view['organisation']['institutes']:
			if institute['instituteId'] in institute_scope:
				institute_unique_code = institute['instituteUniqueCode']
				break

		selected_date = get_current_time()
		selected_date_time = date.today()
		current_date = selected_date_time.strftime("%d-%b-%Y")
		# organisation_stats = get_organization_stats(user_login_view, user_login_view['organisation']['organisationId'], selected_date, current_session['academicSessionId'])
		
		return render(request, 'organisation_portal/layouts/v2/dashboard-content.html',{'dashboard_theme' : get_user_theme(request), 'user_login_view': user_login_view, 'user': user_login_view['user'], 'fee_module_exists' : False, 'view_fees_stats' : False, 'date' : current_date})


def student_attendance_stats(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		selected_institute_ids = request.GET.get("selected_institute_ids", "")
		date = request.GET.get("date", 0)
		student_attendance_org_stats = get_student_attendance_org_stats(user_login_view, user_login_view['organisation']['organisationId'], "10225,10226,10227,10228", 1689100200)
		return JsonResponse(student_attendance_org_stats)

def staff_attendance_stats(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		selected_institute_ids = request.GET.get("selected_institute_ids", "")
		date = request.GET.get("date", 0)
		staff_attendance_org_stats = get_staff_attendance_org_stats(user_login_view, user_login_view['organisation']['organisationId'], "10225,10226,10227,10228", 1689100200)
		return JsonResponse(staff_attendance_org_stats)

def student_admission_stats(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		selected_institute_ids = request.GET.get("selected_institute_ids", "")
		date = request.GET.get("date", 0)
		student_admission_org_stats = get_student_admission_org_stats(user_login_view, user_login_view['organisation']['organisationId'], "10225,10226,10227,10228", 1689100200, 1689100200)
		return JsonResponse(student_admission_org_stats)

def fee_collection_stats(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		selected_institute_ids = request.GET.get("selected_institute_ids", "")
		date = request.GET.get("date", 0)
		fee_collection_org_stats = get_fee_collection_org_stats(user_login_view, user_login_view['organisation']['organisationId'], "10225,10226,10227,10228", 1689100200, 1689100200)
		return JsonResponse(fee_collection_org_stats)



def home_page_view(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)

		institute_scope = user_login_view['user']['instituteScope']
		institute_unique_code = None
		for institute in user_login_view['organisation']['institutes']:
			if institute['instituteId'] in institute_scope:
				institute_unique_code = institute['instituteUniqueCode']
				break
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)

		selected_date = get_current_time()
		selected_date_time = date.today()
		current_date = selected_date_time.strftime("%d-%b-%Y")
		organisation_stats = get_organization_stats(user_login_view, user_login_view['organisation']['organisationId'], selected_date, current_session['academicSessionId'])
		user_type = user_login_view['user']['userType']
		institute_scope = user_login_view['user']['instituteScope']
		institute_rows = []
		count = 0
		rows = 0
		fee_module_exists = False
		user = user_login_view['user']
		if user['authorizedModules'] is not None:
			for module in user['authorizedModules']:
				if module is not None:
					if module['moduleId'] == 'FEES':
						fee_module_exists = True
						break
		institute_details_map = {}
		view_fees_stats = False
		total_enrolled_student = 0
		total_new_admission = 0
		total_present_today = 0
		total_leave_absent_today = 0
		total_collected_fees = 0
		total_todays_collection = 0
		total_students_in_org = 0
		total_relieved_students = 0
		index = 0
		for institute_stats in organisation_stats:

			institute = institute_stats['institute']
			institute_unique_code = institute['instituteUniqueCode']
			institute['portal_path'] = get_user_portal_path(user_type, institute_unique_code)

			standards = institute_stats['standardList']
			class_payment_stats = institute_stats['sessionFeesStats']
			date_wise_class_payment_stats = institute_stats['dateWiseFeesStats']
			admission_home_stats = institute_stats['studentAdmissionStats']
			attendance_stats = institute_stats['dateWiseAttendanceStats']
			transport_stats = institute_stats['transportStats']
			staff_stats = institute_stats['staffStats']
			today_birthday = institute_stats['todayBirthdayList']
			upcoming_birthday = institute_stats['upcomingBirthdayList']

			if admission_home_stats:
				total_enrolled_student += admission_home_stats['totalStudents']
				total_new_admission += admission_home_stats['newAdmissionStudents']
			if attendance_stats:
				total_present_today += attendance_stats['totalPresentAttendance']
				total_leave_absent_today += (attendance_stats['totalAbsentAttendance'] + attendance_stats['totalLeaveAttendance'])
			if class_payment_stats:
				total_collected_fees += class_payment_stats['collectedAmount']
				total_todays_collection += date_wise_class_payment_stats['collectedAmount']

			total_students = 0
			if standards:
				for standard in standards:
					if standard['studentCount'] is not None:
						total_students += standard['studentCount']
			total_students_in_org += total_students

			total_relieved_students += institute_stats['relievedStudentsCount']

			institute_details_map[institute['instituteUniqueCode']] = {'institute' : get_institute_details(user_login_view, institute_unique_code), 'total_students' : total_students, 'class_payment_stats' : class_payment_stats, 'date_wise_class_payment_stats' : date_wise_class_payment_stats, 'admission_home_stats' : admission_home_stats, 'attendance_stats' : attendance_stats, 'color' : COLOR_LIST[index], 'transport_stats' : transport_stats, 'staff_stats' : staff_stats, 'today_birthday' : today_birthday, 'upcoming_birthday' : upcoming_birthday}

			if(count % 2 == 0):
				institute_rows.append([])
				rows +=1
			institute_row = institute_rows[rows-1]
			institute_row.append(institute)
			count += 1
			view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_ORGANISATION_DASHBOARD_STATS)
			index += 1

		present_percentage = 0
		if total_students_in_org:
			present_percentage = (total_present_today / total_students_in_org) * 100
		return render(request, 'organisation_portal/layouts/dashboard-content.html',{'dashboard_theme' : get_user_theme(request), 'user_login_view': user_login_view, 'user': user_login_view['user'], 'institute_rows' : institute_rows, 'institute_details_map' : institute_details_map, 'fee_module_exists' : fee_module_exists, 'view_fees_stats' : view_fees_stats, 'total_enrolled_student' : total_enrolled_student, 'total_new_admission' : total_new_admission, 'total_present_today' : total_present_today, 'total_leave_absent_today' : total_leave_absent_today, 'total_collected_fees' : total_collected_fees, 'total_todays_collection' : total_todays_collection, 'present_percentage' : present_percentage, 'total_relieved_students' : total_relieved_students, 'date' : current_date, 'academic_years' : academic_years, 'current_session' : current_session})


def home_date_session_change_page_view(request, selected_date, selected_academic_session_id):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		organisation_stats = get_organization_stats(user_login_view, user_login_view['organisation']['organisationId'], selected_date, selected_academic_session_id)
		user_type = user_login_view['user']['userType']
		institute_scope = user_login_view['user']['instituteScope']
		institute_rows = []
		count = 0
		rows = 0
		fee_module_exists = False
		user = user_login_view['user']
		if user['authorizedModules'] is not None:
			for module in user['authorizedModules']:
				if module is not None:
					if module['moduleId'] == 'FEES':
						fee_module_exists = True
						break
		institute_details_map = {}
		view_fees_stats = False
		total_enrolled_student = 0
		total_new_admission = 0
		total_present_today = 0
		total_leave_absent_today = 0
		total_collected_fees = 0
		total_todays_collection = 0
		total_students_in_org = 0
		total_relieved_students = 0
		index = 0
		for institute_stats in organisation_stats:

			institute = institute_stats['institute']
			institute_unique_code = institute['instituteUniqueCode']
			institute['portal_path'] = get_user_portal_path(user_type, institute_unique_code)

			standards = institute_stats['standardList']
			class_payment_stats = institute_stats['sessionFeesStats']
			date_wise_class_payment_stats = institute_stats['dateWiseFeesStats']
			admission_home_stats = institute_stats['studentAdmissionStats']
			attendance_stats = institute_stats['dateWiseAttendanceStats']
			transport_stats = institute_stats['transportStats']
			staff_stats = institute_stats['staffStats']
			today_birthday = institute_stats['todayBirthdayList']
			upcoming_birthday = institute_stats['upcomingBirthdayList']

			if admission_home_stats:
				total_enrolled_student += admission_home_stats['totalStudents']
				total_new_admission += admission_home_stats['newAdmissionStudents']
			if attendance_stats:
				total_present_today += attendance_stats['totalPresentAttendance']
				total_leave_absent_today += (attendance_stats['totalAbsentAttendance'] + attendance_stats['totalLeaveAttendance'])
			if class_payment_stats:
				total_collected_fees += class_payment_stats['collectedAmount']
				total_todays_collection += date_wise_class_payment_stats['collectedAmount']

			total_students = 0
			if standards:
				for standard in standards:
					if standard['studentCount'] is not None:
						total_students += standard['studentCount']
			total_students_in_org += total_students

			total_relieved_students += institute_stats['relievedStudentsCount']

			institute_details_map[institute['instituteUniqueCode']] = {'institute' : get_institute_details(user_login_view, institute_unique_code), 'total_students' : total_students, 'class_payment_stats' : class_payment_stats, 'date_wise_class_payment_stats' : date_wise_class_payment_stats, 'admission_home_stats' : admission_home_stats, 'attendance_stats' : attendance_stats, 'color' : COLOR_LIST[index], 'transport_stats' : transport_stats, 'staff_stats' : staff_stats, 'today_birthday' : today_birthday, 'upcoming_birthday' : upcoming_birthday}

			if(count % 2 == 0):
				institute_rows.append([])
				rows +=1
			institute_row = institute_rows[rows-1]
			institute_row.append(institute)
			count += 1
			view_fees_stats = is_authorised_for_action(user_login_view, institute_unique_code, "FEES", FEES_ORGANISATION_DASHBOARD_STATS)
			index += 1

		present_percentage = 0
		if total_students_in_org:
			present_percentage = (total_present_today / total_students_in_org) * 100
		return render(request, 'organisation_portal/layouts/dashboard-session-content.html',{'dashboard_theme' : get_user_theme(request), 'user_login_view': user_login_view, 'user': user_login_view['user'], 'institute_rows' : institute_rows, 'institute_details_map' : institute_details_map, 'fee_module_exists' : fee_module_exists, 'view_fees_stats' : view_fees_stats, 'total_enrolled_student' : total_enrolled_student, 'total_new_admission' : total_new_admission, 'total_present_today' : total_present_today, 'total_leave_absent_today' : total_leave_absent_today, 'total_collected_fees' : total_collected_fees, 'total_todays_collection' : total_todays_collection, 'present_percentage' : present_percentage, 'total_relieved_students' : total_relieved_students})

def change_password_view(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		user = user_login_view['user']
		return render(request, 'organisation_portal/change_password.html',{'user':user})

@csrf_exempt
def update_password_view(request):
	if authorized_organisation_portal(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		change_password_data = json.loads(request.POST['changePasswordInfo'])
		response_data = change_password(user_login_view,  change_password_data)
		return render(request, 'organisation_portal/status_modal.html',{"data":response_data})

def generate_birthday_certificate_view(request, institute_id, academic_session_id, student_id):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		document = get_birthday_certificate(user_login_view, institute_id, academic_session_id, student_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/pdf")
			response['Content-Disposition'] = 'filename='+document['file_name']
			return response
		return HttpResponse("<strong>Invalid information submitted.</strong>")

def fees_reports_view(request):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		institute_scope = user_login_view['user']['instituteScope']
		institute_unique_code = None
		for institute in user_login_view['organisation']['institutes']:
			if institute['instituteId'] in institute_scope:
				institute_unique_code = institute['instituteUniqueCode']
				break
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'organisation_portal/reports/fees_main_screen.html',{'academic_years':academic_years, 'current_session' : current_session})

def fees_generate_report_view(request, report_type):
	if authorized_organisation_portal(request):
		user_login_view = get_user_login_view(request)
		organisation_id = user_login_view['organisation']['organisationId']
		download_report = request.GET.get("downloadReport", False)
		downloadFormat = request.GET.get("downloadFormat")
		report_request = {"report_type":report_type,"academic_session_id":request.GET.get("academic_session_id",0), 'studentStatus' :  request.GET.get("studentStatus",""), "download_format" : downloadFormat}
		if download_report :
			report = get_fees_report(user_login_view, organisation_id, report_request)
			if(downloadFormat == "PDF"):
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/pdf")
					response['Content-Disposition'] = 'filename='+report['file_name']
					return response
			else:
				if(report['success']):
					response = HttpResponse(report['content'], content_type="application/vnd.ms-excel")
					response['Content-Disposition'] = 'inline; filename='+report["file_name"]
					return response

			return HttpResponse("<strong>"+report['error_reason']+"</strong>")
		else :
			report = get_fees_report_data(user_login_view, organisation_id, report_request)
			url_path = request.build_absolute_uri()
			url_suffix_path = url_path
			excel_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=EXCEL";
			pdf_download_url = url_suffix_path + "&downloadReport=true&downloadFormat=PDF";

			return render(request, 'organisation_portal/reports/org_report_webview.html',{'report':report, 'excel_download_url' : excel_download_url, 'pdf_download_url' : pdf_download_url})
