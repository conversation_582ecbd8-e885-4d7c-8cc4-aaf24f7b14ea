var sidebarFull = 200;
var sidebarCollapsed = 80;
var CLASS_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID = "select-class-notification";
var STUDENT_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID = "select-student-notification";
var BULK_STUDENT_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID = "select-bulk-student-notification";
var BULK_STAFF_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID = "select-bulk-staff-notification";

$(document).ready(function() {
    $('#sidebarCollapseImage').click(function() {
        // showErrorDialogBox("dsd");
        var isCollapsed = 1;
        if ($("#sidebar.active").length == 0) {
            isCollapsed = 0;
        }
        var contentMargin = "";
        if (isCollapsed == 1) {
            var fullMargin = sidebarFull + 20;
            contentMargin = fullMargin + "px";
        } else {
            var collapsedMargin = sidebarCollapsed + 20;
            contentMargin = collapsedMargin + "px";

        }
        $('#sidebar').toggleClass('active');
        $("#company_name_footer").toggleClass('active');
        $('#content').css('margin-left', contentMargin);

    });

    registerSidebarMenu();
    loadHomePage();
    registerChangePassword();
    paymentReminder.readPaymentState();

});

function sidebarHoverOutState(current) {
    $(current).find('img.sidebar_icon').attr("style", "");
    $(current).find('img.sidebar_icon_active').attr("style", "display:none");
}

function sidebarHoverOnState(current) {
    $(current).find('img.sidebar_icon').attr("style", "display:none");
    $(current).find('img.sidebar_icon_active').attr("style", "");
}

function sideBarHoverEventCallback() {
    $(".sidebar-menu-item").hover(
        function() {
            sidebarHoverOnState(this);
        },
        function() {
            if ($(this).parent().hasClass("active")) {
                sidebarHoverOnState(this);
            } else {
                sidebarHoverOutState(this);
            }
        });
}

function activateMenuItem() {
    $('.sidebar-menu-item').click(function(e) {
        $('.sidebar-menu li.active').removeClass('active');
        var $parent = $(this).parent();
        $parent.addClass('active');

        sidebarHoverOutState($('.sidebar-menu').find('li').not('.active').find('.sidebar-menu-item'));
        e.preventDefault();
    });
}

function registerSidebarMenu() {
    sideBarHoverEventCallback();
    activateMenuItem();
    registerHomeMenu();
    registerChangePassword();
    registerSendReminderMenu();
    registerNotificationHistoryMenu();
    registerConfigureCommunicationTemplateMenu();
    auditLogMenu();
}

function registerHomeMenu() {
    $('#homeNav').on('click', function() {
        loadHomePage();
    });
}

function registerChangePassword() {
    $('#changePasswordNav').on('click', function() {
        loadChangePasswordPage();
    });
}

function registerSendReminderMenu() {
    $('#sendNotificationsNav').on('click', function() {
        notifications.loadSendNotificationsPage();
    });
}

function registerNotificationHistoryMenu() {
    $('#notificationHistoryNav').on('click', function() {
        notifications.loadNotificationHistoryHomePage();
    });
}

function auditLogMenu() {
    $('#auditLogNav').on('click', function() {
        auditLog.loadAuditLogHomePage();
    });
}

function registerConfigureCommunicationTemplateMenu() {
    $('#configureCommunicationTemplateNav').on('click', function() {
        notifications.loadConfigureTemplatePage();
    });
}


function loadHomePage() {
    ajaxClient.get("/adminportal/home", function(data) {
        $("#content").html(data);


        $(".hover-press").hover(
            function() {
                $(this).removeClass("shadow");
                $(this).addClass("shadow-sm");
            },
            function() {
              $(this).removeClass("shadow-sm");
              $(this).addClass("shadow");
            });
        homePage.getChartData();
    });
}

var homePage =  {
   getChartData : function () {
      var standards = readJson("#home-page-standards-stats");
      var feeStats = readJson("#home-page-fees-stats");
      var feeChartData = [{"type" : "Total Fees", "amount" : Math.round(feeStats.assignedAmount)}, {"type" : "Collected Fees", "amount" : Math.round(feeStats.collectedAmount)}];
      homePage.loadDonutChart(feeChartData);


      var countArray = [];
      var i = 0;
      var total = 0;
      for( i = 0 ; i < standards.length; i++){
        countArray.push({
          "Class": standards[i].displayName,
          "Count": standards[i].studentCount
        });
        total += standards[i].studentCount;
      }
      $("#total-student-count").text(total);
      homePage.columnChart(countArray);
   },

   loadDonutChart : function (feeChartData) {

          am4core.ready(function() {

        // Themes begin
        am4core.useTheme(am4themes_animated);
        // Themes end

        // Create chart instance
        var chart = am4core.create("chartdiv", am4charts.PieChart);

        // Add data
        chart.data = feeChartData;

        // Set inner radius
        chart.innerRadius = am4core.percent(40);
        chart.radius = am4core.percent(90);
        chart.autoMargins = false;
        chart.height = am4core.percent(100);

        // Add and configure Series
        var pieSeries = chart.series.push(new am4charts.PieSeries());
        pieSeries.dataFields.value = "amount";
        pieSeries.dataFields.category = "type";
        pieSeries.slices.template.stroke = am4core.color("#fff");
        pieSeries.slices.template.strokeWidth = 2;
        pieSeries.slices.template.strokeOpacity = 1;

        // This creates initial animation
        pieSeries.hiddenState.properties.opacity = 1;
        pieSeries.hiddenState.properties.endAngle = -90;
        pieSeries.hiddenState.properties.startAngle = -90;

        pieSeries.labels.template.disabled = true;
        pieSeries.ticks.template.disabled = true;

        $("#chartdiv").attr("style","height:15rem;")
        }); // end am4core.ready()
  },

   columnChart : function (countArray) {
    am4core.ready(function() {

  // Themes begin
  am4core.useTheme(am4themes_animated);
  // Themes end

  // Create chart instance
  var chart = am4core.create("chartdiv1", am4charts.XYChart);

  // Add data
  chart.data = countArray;

  chart.fontSize = 15;
  // Create axes

  var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
  categoryAxis.dataFields.category = "Class";
  categoryAxis.renderer.grid.template.location = 0;
  categoryAxis.renderer.minGridDistance = 30;

  categoryAxis.renderer.labels.template.adapter.add("dy", function(dy, target) {
    if (target.dataItem && target.dataItem.index & 2 == 2) {
      return dy + 25;
    }
    return dy;
  });

  var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());

  // Create series
  var series = chart.series.push(new am4charts.ColumnSeries());
  series.dataFields.valueY = "Count";
  series.dataFields.categoryX = "Class";
  series.name = "Count";
  series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
  series.columns.template.fillOpacity = .8;

  var columnTemplate = series.columns.template;
  columnTemplate.strokeWidth = 2;
  columnTemplate.strokeOpacity = 1;

  }); // end am4core.ready()
  }

};

function loadChangePasswordPage() {
    ajaxClient.get("/adminportal/change-password", function(data) {
        $("#content").html(data);
    });
}

function changePassword(){
  var oldPassword = $("#old-password").val();
  var newPassword = $("#new-password").val();
  var confirmNewPassword = $("#confirm-new-password").val();
  var changePasswordInfo = {"oldPassword" : oldPassword, "newPassword" : newPassword};
  if(newPassword != confirmNewPassword){
    showErrorDialogBox("Password don't match!!");
  } else{
    ajaxClient.post("/adminportal/update-password",{'changePasswordInfo':JSON.stringify(changePasswordInfo)},
        function(data) {
          $("#change-password\\.status-modal-container").html(data);
          $("#change-password-status-modal").modal('toggle');
          loadChangePasswordPage();
    });
  }
}


var notifications = {
  dataCache : {},

  loadSendNotificationsPage : function () {
    ajaxClient.get("/adminportal/notifications/main-screen", function(data) {
        $("#content").html(data);
        academicSessionHandler.bindSessionChangeEvent(notifications.changeSendNotificationSession);
        registerDropDownSelectEvent();
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
        notifications.registerSendNotifictionOptionsTabs();
        notifications.dataCache['sendNotifications'] = {};
        notifications.dataCache['currentScreen'] = "class";
        notifications.dataCache.selectedUserCount = 0;
        notifications.dataCache.templateLoaded = false;
    });
  },

  changeSendNotificationSession : function () {
    var currentScreen = notifications.dataCache['currentScreen'];
    if(currentScreen == "class"){
      notifications.loadClassScreen();
    }else if (currentScreen == "bulk-student"){
      notifications.loadBulkStudentScreen();
    }else if (currentScreen == "staff"){
      notifications.loadBulkStaffScreen();
    }
  },

  registerCountSMSCharacters : function () {
    $("#notification-content").keyup(function( event ) {
        notifications.updateSMSCreditDisplay();
    });
  },

  updateSMSCreditDisplay : function () {
    var message = $("#preview-notification-template-content").text();
    var creditUsed = getSmsCredits(message);
    $("#notification-content-char-count").html("Total characters : "+ message.length + " / Credit(s) Used : " + creditUsed + " (Actual credits may vary depending on the variables values)");
    var confirmButtonContent = "Yes, Send SMS";
    var removeClass = "btn-danger";
    var addClass = "btn-primary";
    if(creditUsed > 1){
       confirmButtonContent = "Yes, Send SMS With " + creditUsed + " Credits";
       removeClass = "btn-primary";
       addClass = "btn-danger";
    }
    $("#notification-send-confirm-button").html(confirmButtonContent);
    $("#notification-send-confirm-button").removeClass(removeClass);
    $("#notification-send-confirm-button").addClass(addClass);

    if(creditUsed == 0){
      $("#notification-send-confirm-button").prop('disabled', true);
      $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
    }
    else if(notifications.dataCache.selectedUserCount * creditUsed > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
      $("#notification-send-confirm-button").prop('disabled', true);
      $("#insufficient-sms-credits-message").attr("style", "color:red; display:block;");
    }else{
      $("#notification-send-confirm-button").prop('disabled', false);
      $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
    }
  },

  registerSendNotifictionOptionsTabs : function() {
    $("#"+CLASS_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        var status = confirm("You will be taken to class level notifications and the unsaved data will be lost on current screen. Do you want to proceed?");
        if(!status){
          return;
        }
        notifications.loadClassScreen();
    });

    $("#"+STUDENT_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        var status = confirm("You will be taken to student level notification and the unsaved data will be lost on current screen. Do you want to proceed?");
        if(!status){
          return;
        }
        notifications.loadStudentScreen();

    });

    $("#"+BULK_STUDENT_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        var status = confirm("You will be taken to bulk student level notifications and the unsaved data will be lost on current screen. Do you want to proceed?");
        if(!status){
          return;
        }
        notifications.loadBulkStudentScreen();
    });
    $("#"+BULK_STAFF_NOTIFICATION_SCREEN_SELECTION_BUTTON_ID).on('click', function () {
        var status = confirm("You will be taken to staff level notifications and the unsaved data will be lost on current screen. Do you want to proceed?");
        if(!status){
          return;
        }
        notifications.loadBulkStaffScreen();
    });
  },

  loadClassScreen: function() {
    notifications.dataCache['userType'] = "STUDENT";
    notifications.dataCache['currentScreen'] = "class";
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/adminportal/notifications/class-screen/"+academicSessionId, function(data) {
        $("#notification-user-list-screen").html(data);
        registerDropDownSelectEvent();
        channelCreditsHandler.initDataCache();
        channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
        notifications.dataCache.selectedUserCount = 0;

    });
  },

  loadBulkStudentScreen: function() {
      notifications.dataCache['userType'] = "STUDENT";
      notifications.dataCache['currentScreen'] = "bulk-student";
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/adminportal/notifications/bulk-student-screen/"+academicSessionId, function(data) {
          $("#notification-user-list-screen").html(data);
          channelCreditsHandler.initDataCache();
          channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
          notifications.dataCache.selectedUserCount = 0;

      });
  },

  loadBulkStaffScreen: function() {
      notifications.dataCache['userType'] = "STAFF";
      notifications.dataCache['currentScreen'] = "staff";
      var academicSessionId =  academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/adminportal/notifications/bulk-staff-screen/"+academicSessionId, function(data) {
          $("#notification-user-list-screen").html(data);
          channelCreditsHandler.initDataCache();
          channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
          notifications.dataCache.selectedUserCount = 0;
      });
  },

  loadClassStudents : function () {
    notifications.dataCache['userType'] = "STUDENT";
    var sessionId = academicSessionHandler.getSelectedSessionId();
    var selectedStandards = getDropdownSelectedAttributes("#send-notification-classes");
    if(selectedStandards.length == 0){
      showErrorDialogBox("Please select atleast one class.");
      return;
    }
    ajaxClient.get("/adminportal/notifications/class-students/"+sessionId+"?standards="+selectedStandards.join(","), function(data) {
        $("#send-notifications-class-student-list").html(data);
        notifications.dataCache['sendNotifications']['selectedStandards'] = selectedStandards;
    });
  },

 selectStudentList : function (selectAllCheckbox) {
      if(selectAllCheckbox.checked){
          $(".student-select-checkbox").prop('checked', true);
          var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
          $("#students-selected-count").html(selectedStudentCount);
          notifications.dataCache.selectedUserCount = selectedStudentCount;
      }
      else{
          $(".student-select-checkbox").prop('checked', false);
          $("#students-selected-count").html(0);
          notifications.dataCache.selectedUserCount = 0;
      }
  },

  selectStaffList : function (selectAllCheckbox) {
       if(selectAllCheckbox.checked){
           $(".staff-select-checkbox").prop('checked', true);
           var selectedStaffCount = $('input.staff-select-checkbox:checkbox:checked').length;
           $("#staff-selected-count").html(selectedStaffCount);
           notifications.dataCache.selectedUserCount = selectedStaffCount;

       }
       else{
           $(".staff-select-checkbox").prop('checked', false);
           $("#staff-selected-count").html(0);
           notifications.dataCache.selectedUserCount = 0;

       }
   },

 studentSelectCheckbox : function(studentSelectCheckbox) {
        var selectedStudentCount = $('input.student-select-checkbox:checkbox:checked').length;
        $("#students-selected-count").html(selectedStudentCount);
        notifications.dataCache.selectedUserCount = selectedStudentCount;
  },

  staffSelectCheckbox : function(staffSelectCheckbox) {
         var staffStudentCount = $('input.staff-select-checkbox:checkbox:checked').length;
         $("#staff-selected-count").html(staffStudentCount);
         notifications.dataCache.selectedUserCount = selectedStaffCount;
  },

  sendNotificationsConfirmModal : function (userType) {
    var selectedUserCount = 0;

    if(userType == "STUDENT"){
      selectedUserCount = $('input.student-select-checkbox:checkbox:checked').length;
      notifications.dataCache.selectedUserCount = selectedUserCount;
    }else if(userType == "STAFF"){
      selectedUserCount = $('input.staff-select-checkbox:checkbox:checked').length;
      notifications.dataCache.selectedUserCount = selectedUserCount;
    }else{
      showErrorDialogBox("Invalid user type for sending notifications.");
      return;
    }
    if(selectedUserCount == 0){
      showErrorDialogBox("Please select atleast one contact for sending notifications.");
      return;
    }

    if(notifications.dataCache.templateLoaded){
        notifications.updateConfirmNotificationModalDisplay();
        $("#bulk-notification-confirmation-modal").modal({backdrop: 'static', keyboard: false});
        $(".selected-user-count").text(selectedUserCount);
    }else{
      ajaxClient.get("/adminportal/notification-templates", function(data){
          $("#bulk-notification-template-modal-container").html(data);
          notifications.updateConfirmNotificationModalDisplay();
          $("#bulk-notification-confirmation-modal").modal({backdrop: 'static', keyboard: false});
          var audioTemplates = readJson("#audio-templates-json");
          var smsTemplates = readJson("#sms-templates-json");
          notifications.bindAudioTemplateClickAction(audioTemplates);
          notifications.bindSMSTemplateClickAction(smsTemplates);
          notifications.registerCountSMSCharacters();
          $(".selected-user-count").text(selectedUserCount);
          notifications.dataCache.templateLoaded = true;
     });
    }

  },

  bindAudioTemplateClickAction : function (audioTemplates) {
    $('#audio-template-select').change(function(){
        var templateId = $(this).val();
        if(templateId == ""){
          $("#audio-template-selection-display").attr("style", "display:none");
          $("#send-bulk-voice-calls-button").prop('disabled', true);
          return;
        }

        $("#audio-template-selection-display").attr("style", "display:block");

        for(var i = 0; i < audioTemplates.length; i++){
          var audioTemplate = audioTemplates[i];
          if(templateId == audioTemplate.templateId){
            $("#template-name").text(audioTemplate.templateName);
            $("#template-duration").text(round(audioTemplate.metadata.audio_metadata["duration"], 0));
            $("#template-credits").text(audioTemplate.credits);

            var totalCredits = audioTemplate.credits * notifications.dataCache.selectedUserCount;
            $("#total-user-credits").text(totalCredits);
            notifications.dataCache.selectedAudioCredits = audioTemplate.credits;

            if(totalCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
                $("#send-bulk-voice-calls-button").prop('disabled', true);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:block");
            }else{
                $("#send-bulk-voice-calls-button").prop('disabled', false);
                $("#insufficient-voice-call-credits-message").attr("style", "color:red; display:none");
            }

            break;
          }
        }
    });
  },

  bindSMSTemplateClickAction : function (smsTemplates) {
    $('#sms-template-select').change(function(){
        var templateId = $(this).val();
        if(templateId == ""){
          $("#sms-template-selection-display").attr("style", "display:none");
          $("#notification-send-confirm-button").prop('disabled', true);
          return;
        }

        $("#sms-template-selection-display").attr("style", "display:block");

        for(var i = 0; i < smsTemplates.length; i++){
          var smsTemplate = smsTemplates[i];
          if(templateId == smsTemplate.templateId){
            $("#selected-sms-template-header").text("Template Message (" + smsTemplate.templateName + ")");
            $("#notification-template-content").text(smsTemplate.templateValue);

            if(smsTemplate.templateVariableDetails != null && smsTemplate.templateVariableDetails.templateVariableList != null){
                var count = 1;
                var tableValue = "";
                for(var j = 0 ; j < smsTemplate.templateVariableDetails.templateVariableList.length; j++){
                    var templateVariable = smsTemplate.templateVariableDetails.templateVariableList[j];
                    var varContent = "";
                    var varValue = "";
                    if(templateVariable.defaultValue != null){
                      varValue = templateVariable.defaultValue;
                    }
                    var classValue = "";
                    if(templateVariable.templateVariableType != "CUSTOM"){
                      classValue = "template-var-row system-var";
                      varContent = "System Derived";
                    }else{
                      classValue = "template-var-row custom-var";
                      varContent = "<input type=\"text\" class=\"variable-value\" value=\""+ varValue +"\"/>";
                    }

                    tableValue += "<tr class=\""+classValue+"\" id='"+templateVariable.varName+"'> <td class='template-var-sr-no'>"+count+"</td><td class='template-var-name'>"+templateVariable.varName+"</td><td class='template-var-type'>"+templateVariable.templateVariableTypeDisplayName+"</td><td class='template-var-value'>"+varContent+"</td></tr>"
                    count += 1;
                }
            }
            $("#template-var-content").html(tableValue);
            break;
          }
        }
        notifications.bindSMSTemplateVarInput();
        notifications.updateSMSTemplatePreview();

    });
  },

  bindSMSTemplateVarInput : function () {
    $(".variable-value").on('keyup', function (e) {
      notifications.updateSMSTemplatePreview();
    });
  },

  updateSMSTemplatePreview : function () {
      var smsTemplateValue = $("#notification-template-content").text();
      $(".template-var-row").each(function() {
        var varName = $(this).find(".template-var-name").first().text();
        var varValue = varName;
        if($(this).hasClass("custom-var")){
          varValue = $(this).find(".variable-value").val();
          if(varValue != null && varValue.trim() != ""){
            smsTemplateValue = smsTemplateValue.replace("${"+varName+"}", varValue);
          }
        }
      });
      $("#preview-notification-template-content").text(smsTemplateValue);
      notifications.updateSMSCreditDisplay();
  },

  getSMSTemplateVariables : function () {
      var templateVars = {};
      $(".template-var-row").each(function() {
        var varName = $(this).find(".template-var-name").first().text();
        var varValue = null;
        if($(this).hasClass("custom-var")){
          varValue = $(this).find(".variable-value").val();
        }
        templateVars[varName] = varValue;
      });
      return templateVars;
  },

  updateConfirmNotificationModalDisplay : function () {

      notifications.updateSMSCreditDisplay();

      $("#sms-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count);
      $("#audio-voice-call-credits-display").text(channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count);

      $("#audio-template-selection-display").attr("style", "display:none");
      $('#audio-template-select').val("");
      $("#send-bulk-voice-calls-button").prop('disabled', true);
      $("#insufficient-voice-call-credits-message").attr("style", "display:none");

      $("#sms-template-selection-display").attr("style", "display:none");
      $('#sms-template-select').val("");
      $("#notification-send-confirm-button").prop('disabled', true);
      $("#insufficient-sms-credits-message").attr("style", "color:red; display:none;");
  },

  resetStudentCredentialsConfirmModal : function (userType) {
    var selectedUserCount = 0;

    if(userType == "STUDENT"){
      selectedUserCount = $('input.student-select-checkbox:checkbox:checked').length;
    }else if(userType == "STAFF"){
      selectedUserCount = $('input.staff-select-checkbox:checkbox:checked').length;
    }else{
      showErrorDialogBox("Invalid user type for credentials reset.");
      return;
    }
    if(selectedUserCount == 0){
      showErrorDialogBox("Please select atleast one user for credentials reset.");
      return;
    }
    var currentFormattedTime = getFormattedDateWithTime(parseInt(new Date().getTime()/1000));
    $("#bulk-credentials-reset-batch-name").val("CRED-RESET-SMS-"+currentFormattedTime);
    $("#bulk-credentials-reset-confirmation-modal-text").text("You have selected "+selectedUserCount+ " user(s) to reset credentials. New password will be generated for these selected users and SMS will be sent to their primary contact number. Do you want to proceed?");
    $("#bulk-credentials-reset-confirmation-modal").modal({backdrop: 'static', keyboard: false});

  },

  sendSMSNotifications : function () {
    var batchName = $("#notification-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent notifications.")
      return;
    }
    var templateId = $('#sms-template-select').find(':selected').val().trim();
    if(templateId == ""){
      showErrorDialogBox("Please select sms template to send notifications.");
      return;
    }

    var message = $("#preview-notification-template-content").text();
    if(message == null || message == ""){
      showErrorDialogBox("Please select sms template to send notifications.")
      return;
    }
    var userType = notifications.dataCache['userType'];

    var userIds = null;
    if(userType == "STUDENT"){
      userIds = notifications.getSelectedBulkStudentIds();
    }else if(userType == "STAFF"){
      userIds = notifications.getSelectedBulkStaffIds();
    }else{
      showErrorDialogBox("Invalid user type for sending notifications.");
      return;
    }

    var creditUsed = getSmsCredits(message);

    if(notifications.dataCache.selectedUserCount * creditUsed > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + notifications.dataCache.selectedUserCount + " SMS. Please recharge.");
      return;
    }

    $("#bulk-notification-confirmation-modal").modal('toggle');

    var customVariables = notifications.getSMSTemplateVariables();

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var data = {'batchName':batchName, 'academicSessionId' : academicSessionId, 'deliveryMode':'SMS', 'userType' : userType, 'userIds': userIds, 'smsTemplateId': templateId, 'customVariables' : customVariables};
    ajaxClient.post("/adminportal/send-notifications",{'sendNotificationsPayload':JSON.stringify(data)}, function(data){
        $("#notification-status-modal-container").html(data);
        $("#bulk-notification-status-modal").modal('toggle');
        channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
   });
  },

  sendAudioVoiceCall : function () {

    var batchName = $("#voice-notification-fee-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the sent notifications.")
      return;
    }
    var includeFine = false;
    if($("#include-fee-fine").is(":checked")){
      includeFine = true;
    }
    var templateId = $('#audio-template-select').find(':selected').val().trim();
    if(templateId == ""){
      showErrorDialogBox("Please select audio clip for voice call");
      return;
    }

    var userType = notifications.dataCache['userType'];

    var userIds = null;
    if(userType == "STUDENT"){
      userIds = notifications.getSelectedBulkStudentIds();
    }else if(userType == "STAFF"){
      userIds = notifications.getSelectedBulkStaffIds();
    }else{
      showErrorDialogBox("Invalid user type for sending notifications.");
      return;
    }

    if(notifications.dataCache.selectedUserCount *  notifications.dataCache.selectedAudioCredits > channelCreditsHandler.dataCache.channelCreditsMap.AUDIO_VOICE_CALL_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + notifications.dataCache.selectedUserCount + " voice calls. Please recharge.");
      return;
    }

    $("#bulk-notification-confirmation-modal").modal('toggle');

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var data = {'batchName':batchName, 'academicSessionId' : academicSessionId, 'deliveryMode':'CALL', 'userType' : userType, 'userIds': userIds, 'voiceCallPayloadType' : 'AUDIO', 'voiceTemplateId': templateId};
    ajaxClient.post("/adminportal/send-voice-call-notifications",{'sendNotificationsPayload':JSON.stringify(data)}, function(data){
        $("#notification-status-modal-container").html(data);
        $("#bulk-notification-status-modal").modal('toggle');
        channelCreditsHandler.loadChannelCredits("/adminportal/get-channel-credits")
   });
  },

  resetCredentials : function () {
    var batchName = $("#bulk-credentials-reset-batch-name").val();
    if(batchName == null || batchName == ""){
      showErrorDialogBox("Please give batch name to identify the credentials reset request.")
      return;
    }

    var userType = notifications.dataCache['userType'];

    var userIds = null;
    if(userType == "STUDENT"){
      userIds = notifications.getSelectedBulkStudentIds();
    }else if(userType == "STAFF"){
      userIds = notifications.getSelectedBulkStaffIds();
    }else{
      showErrorDialogBox("Invalid user type for credentials reset.");
      return;
    }

    if(notifications.dataCache.selectedUserCount > channelCreditsHandler.dataCache.channelCreditsMap.SMS_COUNTER.count){
      showErrorDialogBox("You do not have sufficient credits to send " + notifications.dataCache.selectedUserCount + " SMS. Please recharge.");
      return;
    }

    $("#bulk-credentials-reset-confirmation-modal").modal('toggle');

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var data = {'batchName':batchName, 'academicSessionId' : academicSessionId, 'deliveryMode':'SMS', 'userType' : userType, 'userIds': userIds};
    ajaxClient.post("/adminportal/reset-credentials",{'resetCredentialsPayload':JSON.stringify(data)}, function(data){
        $("#notification-status-modal-container").html(data);
        $("#bulk-notification-status-modal").modal('toggle');
   });
  },

  getSelectedBulkStudentIds :function () {
    var studentIds = [];
    $("input.student-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var studentId = $(this).parent().find('p.bulk-notification-student-id').first().text().trim();
        studentIds.push(studentId);
    });
    return studentIds;
  },

  getSelectedBulkStaffIds :function () {
    var staffIds = [];
    $("input.staff-select-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var staffId = $(this).parent().find('p.bulk-notification-staff-id').first().text().trim();
        staffIds.push(staffId);
    });
    return staffIds;
  },


  loadNotificationHistoryHomePage : function () {
    ajaxClient.get("/adminportal/notification-history-homepage", function(data) {
        $("#content").html(data);
        academicSessionHandler.bindSessionChangeEvent(notifications.changeSession);
        notifications.registerLoadNotifictionHistoryOptionsTabs();
        notifications.loadNotificationHistory(true, "SMS", "BATCH");
    });
  },

  changeSession : function () {
    notifications.loadNotificationHistory(true, "SMS", "BATCH");
  },

  loadNotificationHistory : function (freshSearch, deliveryMode, notificationHistoryTab) {
    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;

    var sessionId = academicSessionHandler.getSelectedSessionId();
    var userType = notifications.getNotificationHistoryUserType();

    notifications.dataCache['notificationHistoryTab'] = notificationHistoryTab;
    notifications.dataCache['deliveryModeTab'] = deliveryMode;

    ajaxClient.get("/adminportal/notification-history/"+sessionId+"/"+userType+"/"+deliveryMode+"/"+notificationHistoryTab+"/"+offset+"/"+itemsPerPage, function(data) {
        var screenHeight = 0;
        if(deliveryMode == "SMS"){
          $("#smsNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }else if(deliveryMode == "CALL"){
          $("#voiceNotificationHistoryResult").html(data);
          screenHeight = $("#sidebar").height() - $("#channel-tabContent").position().top - 50;
        }
        $('.notification-history-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
        var response = JSON.parse($("#pagination-info").text().trim());
        $('#items-per-page').val(response.itemsPerPage);
        $('.page-item').removeClass('active');
        var pageNumber = (response.offset/response.itemsPerPage) + 1;
        $('#page-number-'+page_number).addClass('active');
        //Since entire nav section is reloaded, so it is switched back to individual tab is earlier user selected that
        if(notificationHistoryTab == 'INDIVIDUAL'){
          $("#nav-individual-history-tab").addClass('active');
          $("#nav-batch-history-tab").removeClass('active');
          $("#individual-history-content").addClass('show active');
          $("#batch-history-content").removeClass('show active');
        }
        notifications.bindIndividualNotificationsContentView();
        notifications.initPagination();
    });
  },

  loadBatchNotificationsDetails : function (batchId) {
    var userType = notifications.getNotificationHistoryUserType();
    ajaxClient.get("/adminportal/batch-notification-details/"+userType+"/"+batchId, function(data) {
        $("#batch-notification-detail-modal-container").html(data);
        $("#view-batch-notification-modal").modal('toggle');
    });
  },

  bindIndividualNotificationsContentView : function () {
    $(".view-individual-notification").click(function () {
        var notificationContent = $(this).parent().find("p.notification-content").text().trim();
        $("#individual-notification-detail-modal").find(".modal-body").html("<span style=\"white-space: pre-line;\">" + notificationContent + "</span>");
        $("#individual-notification-detail-modal").modal('toggle');
    });
  },

  getNotificationHistoryUserType : function () {
      var userType = notifications.dataCache['historyUserType'];
      if(userType == null || userType == ""){
        return "STUDENT";
      }
      return userType;
  },

  getNotificationHistoryTab : function () {
      var notificationHistoryTab = notifications.dataCache['notificationHistoryTab'];
      if(notificationHistoryTab == null || notificationHistoryTab == ""){
        return "BATCH";
      }
      return notificationHistoryTab;
  },

  getDeliveryModeTab : function () {
      var deliveryModeTab = notifications.dataCache['deliveryModeTab'];
      if(deliveryModeTab == null || deliveryModeTab == ""){
        return "SMS";
      }
      return deliveryModeTab;
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var notificationHistoryTab = notifications.getNotificationHistoryTab();
        var deliveryModeTab = notifications.getDeliveryModeTab();
        notifications.loadNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notifications.getNotificationHistoryTab();
        var deliveryModeTab = notifications.getDeliveryModeTab();
        notifications.loadNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notifications.getNotificationHistoryTab();
        var deliveryModeTab = notifications.getDeliveryModeTab();
        notifications.loadNotificationHistory(false, deliveryModeTab, notificationHistoryTab);
      },
      function () {
        var notificationHistoryTab = notifications.getNotificationHistoryTab();
        var deliveryModeTab = notifications.getDeliveryModeTab();
        notifications.loadNotificationHistory(true, deliveryModeTab, notificationHistoryTab);
      }
    );
  },

  registerLoadNotifictionHistoryOptionsTabs : function() {
    $(".notification-history-options").on('click', function () {
      var elementId = $(this).attr("id");
      var userType = "STUDENT";
      if(elementId == "select-staff-notification-history"){
        userType = "STAFF";
      }
      notifications.dataCache['historyUserType'] = userType;
      var deliveryModeTab = notifications.getDeliveryModeTab();
      notifications.loadNotificationHistory(true, deliveryModeTab, "BATCH");
    });
  },

  loadConfigureTemplatePage : function () {
    ajaxClient.get("/adminportal/notifications/configure-template-home", function(data) {
        $("#content").html(data);
        notifications.bindTemplatePageClicks();
        registerUploadFileCallback();
        var smsTemplates = readJson("#sms-templates");
        notifications.dataCache['smsTemplates'] = smsTemplates;
    });
  },

  bindTemplatePageClicks : function () {
    $(".template-delivery-mode-options").on('click', function () {
      var elementId = $(this).attr("id");
      var deliveryMode = "";
      if(elementId == "select-voice-template"){
        deliveryMode = "CALL";
      }else if(elementId == "select-sms-template"){
        deliveryMode = "SMS";
      }else{
        showErrorDialogBox("template not supported yet");
      }
      notifications.dataCache['templateSelectTab'] = deliveryMode;

      if(deliveryMode == "CALL"){
        notifications.loadAudioTemplatePage();
      }else if(deliveryMode == "SMS"){
        notifications.loadSMSTemplatePage();
      }
    });
  },

  loadSMSTemplatePage : function () {
    ajaxClient.get("/adminportal/notifications/sms-template-screen", function(data) {
        $("#channel-template-screen").html(data);
        var smsTemplates = readJson("#sms-templates");
        notifications.dataCache['smsTemplates'] = smsTemplates;
    });
  },

  loadAudioTemplatePage : function () {
    ajaxClient.get("/adminportal/notifications/audio-template-screen", function(data) {
        $("#channel-template-screen").html(data);
        registerUploadFileCallback();
    });
  },

  viewSMSTemplateContent : function (templateId) {
    var smsTemplates = notifications.dataCache['smsTemplates'];
    var content = "";
    for(var i = 0; i < smsTemplates.length; i++){
      var smsTemplate = smsTemplates[i];
      if(smsTemplate.templateId === templateId){
          content = smsTemplate.templateValue;
          break;
      }
    }

    $("#sms-template-content").text(content);
    $("#sms-template-content-view-modal").modal({backdrop: 'static', keyboard: false});
  },

  playAudioTemplate : function (templateId) {
    // https://stackoverflow.com/questions/44896667/how-to-send-ajax-post-request-and-play-the-audio-in-the-response
      $("#audio-template-player-source").attr('src', getBaseURL() +"/adminportal/notifications/audio-template-download/"+templateId);
      $("#audio-player-modal").modal({backdrop: 'static', keyboard: false});
      var audio = $('#audio-template-player').get(0);

      $("#audio-progress-loader").attr("style", "display:block");
      $("#audio-template-player-container").attr("style", "display:none");

      audio.load();
      audio.play();
      audio.onloadeddata = function() {
        $("#audio-progress-loader").attr("style", "display:none");
        $("#audio-template-player-container").attr("style", "display:block");
      };
  },

  stopAudio : function () {
      $("#audio-template-player-source").attr('src', "");
      var audio = $('#audio-template-player').get(0);
      audio.pause();
      audio.currentTime = 0;
  },

  uploadAudioTemplate : function () {
    var templateType = $("#upload-audio-template-type option:selected").val().trim();
    if(templateType == ""){
      showErrorDialogBox("Template type field is mandatory please fill it then proceed.");
      return;
    }
    var file ;
    var ONE_KB = 1024;
    var FILE_SIZE_LIMIT = 1024 * 3;
    if (($("#upload-audio-template-file"))[0].files.length > 0) {
        file = ($("#upload-audio-template-file"))[0].files[0];
        if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
          showErrorDialogBox("Size Of document cannot be greater than 3MB");
          return;
        }
    } else {
        showErrorDialogBox("No file selected. Please choose a audio file to upload");
        return;
    }
    var templateName = $("#upload-audio-template-name").val();
    if(templateName == "") {
      showErrorDialogBox("Template name field is mandatory please fill it then proceed");
      return;
    }

    var formData = new FormData();
    formData.append('audioTemplate', file);
    formData.append('templateType', templateType);
    formData.append('templateName', templateName);
    $("#upload-new-audio-template-modal").modal("toggle");

    ajaxClient.uploadFile("/adminportal/notifications/audio-template-upload", formData, function(data){
        $("#notification-template-status-modal-container").html(data);
        $("#audio-template-upload-status-modal").modal({backdrop: 'static', keyboard: false});
        notifications.loadAudioTemplatePage();
    });
  }

};


var auditLog = {

  loadAuditLogHomePage : function () {
    ajaxClient.get("/adminportal/audit-log/home", function(data) {
      $("#content").html(data);
      auditLog.registerUserSearchCallback();
      auditLog.getLogs(true);
      //
      //
      // academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession);
      // searchStudents(true);
      // registerStudentSearchCallback();
      // // registerViewStudentDetailsCallBack();
      // bindStudentListPageEvents();
      // initDate(365*100);
    });
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        auditLog.getLogs(false);
      },
      function () {
        auditLog.getLogs(false);
      },
      function () {
        auditLog.getLogs(false);
      },
      function () {
        auditLog.getLogs(true);
      }
    );
  },

  getLogs : function (freshSearch) {
    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;
    var search_text = $("#search-user-input").val().trim();
    ajaxClient.get("/adminportal/audit-log/search?search_text="+search_text+"&offset="+offset +"&itemsPerPage="+itemsPerPage, function(data) {
        $("#search-user-result").html(data);
        pagination.updatePaginationDetails("search-user-result","audit-log-fixed-height-list-wrapper");
        auditLog.initPagination();
        auditLog.bindLogEvents();
    });
  },

  bindLogEvents : function () {
      $('.view-log-details').click(function() {
        var logId = $(this).attr("id");
        ajaxClient.get("/adminportal/audit-log/details/"+logId, function(data) {
            $("#audit-log-details-wrapper").html(data);
            $("#audit-log-details-modal").modal('toggle');
        });
      });
  },

  registerUserSearchCallback: function () {
      $('#search-users').on('click', function () {
           auditLog.getLogs(true);
      });
      $("#search-user-input").on('keyup', function (e) {
        if (e.keyCode == 13) {
            auditLog.getLogs(true);
        }
      });
  }

};
