var sidebarFull=200,sidebarCollapsed=80,LOGO_COLOR="#43a2ad",LIGHT_GREY_COLOR="#F3F3F3";function registerInstituteStatisticsMenu(){$("#instituteStatisticsNav").on("click",function(){instituteStatistics.loadHomePage(),$(this).parent().addClass("active")})}$(document).ready(function(){paymentReminder.readPaymentState(),registerInstituteStatisticsMenu()}),$(document).ready(function(){$(".staff-attendance-data-div .nav-tabs a").on("click",function(t){t.preventDefault(),$(this).tab("show")})});var instituteStatistics={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(instituteStatistics.loadHomePageForSession),instituteStatistics.displayDashboardContent(),$('[data-toggle="tooltip"]').tooltip()},loadHomePage:function(){ajaxClient.get("/adminportal/home",function(t){$("#main-content").html(t),bindPaymentTransactionClickEvents(),instituteStatistics.initHomePage()})},loadHomePageForSession:function(){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/adminportal/session-home/"+t,function(t){$("#admin-dashboard-session-content").html(t),bindPaymentTransactionClickEvents(),instituteStatistics.displayDashboardContent(),$('[data-toggle="tooltip"]').tooltip()})},displayStudentsTransportList:function(){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/adminportal/student-transport-info/"+t,function(t){$("#student-tranport-details").html(t),$("#student-detail-popup").modal("toggle"),instituteStatistics.loadStudentList(t)})},displayStaffAttendanceList:function(t){$("#staff-detail-popup").modal("toggle"),instituteStatistics.loadStaffList(t)},loadStaffList:function(t){var e=readJson("#staff-stats").staffAttendanceGenderDetailsMap[t];$("#male-staff-data").empty(),$("#female-staff-data").empty(),e.MALE&&e.MALE.length>0&&e.MALE.forEach(function(t,e){$("#male-staff-data").append(`\n              <tr>\n                  <th scope="row">${e+1}</th>\n                  <td>${t.staffInstituteId}</td>\n                  <td>${t.name}</td>\n                  <td>${t.staffCategory.staffCategoryName}</td>\n              </tr>\n          `)}),e.FEMALE&&e.FEMALE.length>0&&e.FEMALE.forEach(function(t,e){$("#female-staff-data").append(`\n              <tr>\n                  <th scope="row">${e+1}</th>\n                  <td>${t.staffInstituteId}</td>\n                  <td>${t.name}</td>\n                  <td>${t.staffCategory.staffCategoryName}</td>\n              </tr>\n          `)})},refreshHomePage:function(){instituteStatistics.loadHomePageForSession()},displayDashboardContent:function(){for(var t=readJson("#home-page-standards-stats"),e=readJson("#staff-stats"),a=readJson("#transport-stats"),n=[],s=[],o=0;o<t.length;o++)n.push(t[o].displayName),s.push(t[o].studentCount),t[o].studentCount;instituteStatistics.renderStudentCountChart(n,s),instituteStatistics.loadStudentFeesCountChart(),instituteStatistics.renderStaffCountChart(e),instituteStatistics.renderActiveTransport(a)},renderActiveTransport:function(t){var e=[],a=0,n=0;void 0!==t.totalTransportAssignedStudentCount&&(e.push(t.totalTransportAssignedStudentCount),a=t.totalTransportAssignedStudentCount),void 0!==t.totalTransportUnassignedStudentCount&&e.push(t.totalTransportUnassignedStudentCount),void 0!==t.totalEnrolledStudentCount&&(n=t.totalEnrolledStudentCount);var s="";if(0==n)s='<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>0%</span><span class="text-muted"> of total students</span>';else{var o=getPercentage(a,n);s='<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(o)+'%</span><span class="text-muted"> of total students</span>'}$("#percentage-active-transport").html(s),instituteStatistics.renderTransportChart(["Assigned","Unassigned"],e)},renderTransportChart:function(t,e){Chart.Legend.prototype.afterFit=function(){this.height=this.height+40};var a=new Chart($("#chartjs-active-transport-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:[LOGO_COLOR,LIGHT_GREY_COLOR],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:75,legend:{display:!0,position:"top",align:"center",labels:{boxWidth:12,padding:10}}}});a.canvas.parentNode.style.height="250px",a.canvas.parentNode.style.width="311px"},renderStaffCountChart:function(t){var e=[],a=[];void 0!==t.staffGenderWiseCount.MALE?(e.push(t.todayGenderWisePresentStaff.MALE),a.push(t.staffGenderWiseCount.MALE-t.todayGenderWisePresentStaff.MALE)):(e.push(0),a.push(0)),void 0!==t.staffGenderWiseCount.FEMALE?(e.push(t.todayGenderWisePresentStaff.FEMALE),a.push(t.staffGenderWiseCount.FEMALE-t.todayGenderWisePresentStaff.FEMALE)):(e.push(0),a.push(0)),instituteStatistics.renderStaffsCountChart(["Male","Female"],e,a)},renderStaffsCountChart:function(t,e,a){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20};var n=new Chart($("#chartjs-staff-count-bar-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Present Staff",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5},{label:"Not Present Staff",backgroundColor:LIGHT_GREY_COLOR,borderColor:LIGHT_GREY_COLOR,hoverBackgroundColor:LIGHT_GREY_COLOR,hoverBorderColor:LIGHT_GREY_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}});n.canvas.parentNode.style.height="311px",n.canvas.parentNode.style.width="311px"},loadStudentFeesCountChart:function(){var t=readJson("#date-wise-home-page-fees-stats");if(null!=t&&null!=t.classFeePaymentAggregatedDatas){for(var e=[],a=[],n=[],s=[],o=[],r=0;r<t.classFeePaymentAggregatedDatas.length;r++){var i=t.classFeePaymentAggregatedDatas[r];e.push(i.standardName),a.push(i.assignedAmount),n.push(i.collectedAmount),s.push(i.discountAmount),o.push(i.dueAmount)}instituteStatistics.renderStudentFeesCountChart(e,a,n,s,o)}},renderStudentFeesCountChart:function(t,e,a,n,s){new Chart($("#chartjs-class-fee-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:s,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStudentCountChart:function(t,e){new Chart($("#chartjs-student-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Student Count",backgroundColor:PRIMARY_LOGO_COLOR,borderColor:PRIMARY_LOGO_COLOR,hoverBackgroundColor:PRIMARY_LOGO_COLOR,hoverBorderColor:PRIMARY_LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},generateBirthdayCertificate:function(t){var e=JSON.parse($(t).find(".student-info").text()),a=e.studentAcademicSessionInfoResponse.academicSession.academicSessionId,n=e.studentId;window.open(baseURL+"/adminportal/generate-birthday-certificate/"+a+"/"+n,"_blank")}};function getPercentage(t,e){return parseFloat((t/e*100).toFixed(1))}function bindPaymentTransactionClickEvents(){$(".invoice-payment-transaction").on("click",function(){generateFeePaymentInvoice($(this).attr("id").trim())})}function generateFeePaymentInvoice(t){window.open(baseURL+"/fees/invoice/"+t+"/pdf-summary","_blank")}