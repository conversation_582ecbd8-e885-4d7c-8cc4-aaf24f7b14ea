var sidebarFull = 200;
var sidebarCollapsed = 80;
var LOGO_COLOR = "#43a2ad";
var LIGHT_GREY_COLOR = "#F3F3F3";

$(document).ready(function() {
    paymentReminder.readPaymentState();
    registerInstituteStatisticsMenu();
});


$(document).ready(function () {
  $('.staff-attendance-data-div .nav-tabs a').on('click', function (e) {
      e.preventDefault();
      $(this).tab('show');
  });
});


function registerInstituteStatisticsMenu () {
  $('#instituteStatisticsNav').on('click', function() {
      instituteStatistics.loadHomePage();
      $(this).parent().addClass("active");
  });
}

var instituteStatistics =  {

  initHomePage : function () {
      academicSessionHandler.bindSessionChangeEvent(instituteStatistics.loadHomePageForSession);
      instituteStatistics.displayDashboardContent();
      $('[data-toggle="tooltip"]').tooltip();
  },

  loadHomePage : function () {
    ajaxClient.get("/adminportal/home", function(data) {
        $("#main-content").html(data);
        bindPaymentTransactionClickEvents();
        instituteStatistics.initHomePage();
    });
  },

  loadHomePageForSession : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/adminportal/session-home/"+academicSessionId, function(data) {
        $("#admin-dashboard-session-content").html(data);
        bindPaymentTransactionClickEvents();
        instituteStatistics.displayDashboardContent();
        $('[data-toggle="tooltip"]').tooltip();
    });
  },

  displayStudentsTransportList : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/adminportal/student-transport-info/"+academicSessionId, function(data) {
        $("#student-tranport-details").html(data);
        $("#student-detail-popup").modal('toggle');
      instituteStatistics.loadStudentList(data);
      });
  },

  displayStaffAttendanceList : function (category) {
    $("#staff-detail-popup").modal('toggle');
    instituteStatistics.loadStaffList(category);
  },

  loadStaffList : function(category){
    var staff_data = readJson("#staff-stats");
    var staff_updated_data = staff_data.staffAttendanceGenderDetailsMap[category];
    // Clear previous data
    $('#male-staff-data').empty();
    $('#female-staff-data').empty();
    if (staff_updated_data.MALE && staff_updated_data.MALE.length > 0) {
      staff_updated_data.MALE.forEach(function (staff, index) {
          $('#male-staff-data').append(`
              <tr>
                  <th scope="row">${index + 1}</th>
                  <td>${staff.staffInstituteId}</td>
                  <td>${staff.name}</td>
                  <td>${staff.staffCategory.staffCategoryName}</td>
              </tr>
          `);
      });
  }

  // Render female staff data
  if (staff_updated_data.FEMALE && staff_updated_data.FEMALE.length > 0) {
    staff_updated_data.FEMALE.forEach(function (staff, index) {
          $('#female-staff-data').append(`
              <tr>
                  <th scope="row">${index + 1}</th>
                  <td>${staff.staffInstituteId}</td>
                  <td>${staff.name}</td>
                  <td>${staff.staffCategory.staffCategoryName}</td>
              </tr>
          `);
      });
  }
},


  refreshHomePage : function () {
      instituteStatistics.loadHomePageForSession();
  },

 displayDashboardContent : function () {
   var standards = readJson("#home-page-standards-stats");
   var staff_stats = readJson("#staff-stats");
   var transport_stats = readJson("#transport-stats");
   var labelArr = [];
   var dataArr = []
   var total = 0;
   for( var i = 0 ; i < standards.length; i++){
     labelArr.push(standards[i].displayName);
     dataArr.push(standards[i].studentCount);
     total += standards[i].studentCount;
   }
   instituteStatistics.renderStudentCountChart(labelArr, dataArr);

   instituteStatistics.loadStudentFeesCountChart();

   instituteStatistics.renderStaffCountChart(staff_stats);

   instituteStatistics.renderActiveTransport(transport_stats);
 },

 renderActiveTransport : function (transport_stats) {

  var labelArr = ["Assigned", "Unassigned"];
  var transportStudents = [];
  var totalTransportAssignedStudentCount = 0;
  var totalEnrolledStudentCount = 0;
  if(transport_stats.totalTransportAssignedStudentCount !== undefined) {
  transportStudents.push(transport_stats.totalTransportAssignedStudentCount);
  totalTransportAssignedStudentCount = transport_stats.totalTransportAssignedStudentCount;
  }
  if(transport_stats.totalTransportUnassignedStudentCount !== undefined) {
    transportStudents.push(transport_stats.totalTransportUnassignedStudentCount);
  }
  if(transport_stats.totalEnrolledStudentCount !== undefined) {
    totalEnrolledStudentCount = transport_stats.totalEnrolledStudentCount;
  }
  var html = "";
     if(totalEnrolledStudentCount == 0) {
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>0%</span><span class=\"text-muted\"> of total students</span>";
      } else {
       var percentageChange = getPercentage(totalTransportAssignedStudentCount, totalEnrolledStudentCount);
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%</span><span class=\"text-muted\"> of total students</span>";
      }
     $("#percentage-active-transport").html(html);
  instituteStatistics.renderTransportChart(labelArr, transportStudents);
 },

 renderTransportChart : function (labelArr, transportStudents) {
  Chart.Legend.prototype.afterFit = function() {
    this.height = this.height + 40;
  };
  var rteChart = new Chart($("#chartjs-active-transport-pie"), {
    type: "pie",
    data: {
      labels: labelArr,
      datasets: [{
        data: transportStudents,
        backgroundColor: [
          LOGO_COLOR,
          LIGHT_GREY_COLOR
        ],
        borderWidth: 1,
        borderColor: window.theme.white
      }]
    },
    options: {
      responsive: !window.MSInputMethodContext,
      maintainAspectRatio: false,
      cutoutPercentage: 75,
      legend: {
        display: true,
        position: 'top',
        align: 'center',
        labels:{
          boxWidth: 12,
          padding: 10,
        }
      },
    }
  });
  rteChart.canvas.parentNode.style.height = '250px';
  rteChart.canvas.parentNode.style.width = '311px';
},

 renderStaffCountChart : function (staff_stats) {

  var labelArr = ["Male", "Female"];
  var staffCount = [];
  var presentStaffCount = [];
  var remainingStaffCount = [];
  if(staff_stats.staffGenderWiseCount.MALE !== undefined) {
    presentStaffCount.push(staff_stats.todayGenderWisePresentStaff.MALE);
    remainingStaffCount.push(staff_stats.staffGenderWiseCount.MALE - staff_stats.todayGenderWisePresentStaff.MALE);
  } else {
    presentStaffCount.push(0);
    remainingStaffCount.push(0);
  }

  if(staff_stats.staffGenderWiseCount.FEMALE !== undefined) {
    presentStaffCount.push(staff_stats.todayGenderWisePresentStaff.FEMALE);
    remainingStaffCount.push(staff_stats.staffGenderWiseCount.FEMALE - staff_stats.todayGenderWisePresentStaff.FEMALE);
  } else {
    presentStaffCount.push(0);
    remainingStaffCount.push(0);
  }

  instituteStatistics.renderStaffsCountChart(labelArr, presentStaffCount, remainingStaffCount);
 },

 renderStaffsCountChart : function (labelArr, presentStaffCount, remainingStaffCount) {
  Chart.Legend.prototype.afterFit = function() {
    this.height = this.height + 20;
  };

  var newAdmissionChart = new Chart($("#chartjs-staff-count-bar-distribution"), {
    type: "bar",
    data: {
      labels: labelArr,
      datasets: [{
          label: "Present Staff",
          backgroundColor: LOGO_COLOR,
          borderColor: LOGO_COLOR,
          hoverBackgroundColor: LOGO_COLOR,
          hoverBorderColor: LOGO_COLOR,
          data: presentStaffCount,
          barPercentage: .325,
          categoryPercentage: .5
        },
        {
          label: "Not Present Staff",
          backgroundColor: LIGHT_GREY_COLOR,
          borderColor: LIGHT_GREY_COLOR,
          hoverBackgroundColor: LIGHT_GREY_COLOR,
          hoverBorderColor: LIGHT_GREY_COLOR,
          data: remainingStaffCount,
          barPercentage: .325,
          categoryPercentage: .5
        },
      ]
    },
    options: {
      maintainAspectRatio: false,
      cornerRadius: 15,
      legend: {
        display: false
      },
      scales: {
        yAxes: [{
          ticks: {
               beginAtZero: true
          },
          gridLines: {
            display: false
          },
          stacked: false,
          stacked: true,
        }],
        xAxes: [{
          stacked: false,
          gridLines: {
            color: "transparent"
          },
          stacked: true,
        }]
      }
    }
  });

    // Bar chart
  //   var newAdmissionChart = new Chart($("#chartjs-staff-count-bar-distribution"), {
  //     type: "bar",
  //     data: {
  //       labels: labelArr,
  //       datasets: [
  //         {
  //         label: "Total Staff Count",
  //         backgroundColor: LIGHT_GREY_COLOR,
  //         borderColor: LIGHT_GREY_COLOR,
  //         hoverBackgroundColor: LIGHT_GREY_COLOR,
  //         hoverBorderColor: LIGHT_GREY_COLOR,
  //         data: presentStaffCount,
  //         barPercentage: .325,
  //         categoryPercentage: .5
  //       },
  //       {
  //         label: "Present Staff Count",
  //         backgroundColor: LOGO_COLOR,
  //         borderColor: LOGO_COLOR,
  //         hoverBackgroundColor: LOGO_COLOR,
  //         hoverBorderColor: LOGO_COLOR,
  //         data: remainingStaffCount,
  //         barPercentage: .325,
  //         categoryPercentage: .5
  //       }
  // ]},
  // options: {
  //   responsive: !window.MSInputMethodContext,
  //   maintainAspectRatio: false,
  //   legend: {
  //     display: false,
  //   },
  //   scales: {
  //     yAxes: [{
  //       ticks: {
  //           beginAtZero: true
  //       },
  //       gridLines: {
  //         display: false
  //       },
  //       stacked: false,
  //       stacked: true,
  //     }],
  //     xAxes: [{
  //       stacked: false,
  //       gridLines: {
  //         color: "transparent"
  //       },
  //       stacked: true,
  //     }]
  //   },
  //   animation: {
  //     onComplete: function () {
  //       var chartInstance = this.chart,
  //         ctx = chartInstance.ctx;
  //         ctx.textAlign = 'center';
  //         ctx.fillStyle = "rgba(0, 0, 0, 1)";
  //         ctx.textBaseline = 'bottom';

  //         this.data.datasets.forEach(function (dataset, i) {
  //           var meta = chartInstance.controller.getDatasetMeta(i);
  //           meta.data.forEach(function (bar, index) {
  //             var data = dataset.data[index];
  //             ctx.fillText(data, bar._model.x, bar._model.y - 5);

  //           });
  //         });
  //       }
  //     },
  //     events: [],
  // }
  // });
  newAdmissionChart.canvas.parentNode.style.height = '311px';
  newAdmissionChart.canvas.parentNode.style.width = '311px';
},

 loadStudentFeesCountChart : function () {

   var classFeeStats = readJson("#date-wise-home-page-fees-stats");
   if(classFeeStats == null || classFeeStats.classFeePaymentAggregatedDatas == null){
     return;
   }
   var labelArr = [];
   var totalFeeDataArr = []
   var collectedFeeDataArr = []
   var discountFeeDataArr = []
   var dueFeeDataArr = []
   var dataArr = []
   var total = 0;
   for( var i = 0 ; i < classFeeStats.classFeePaymentAggregatedDatas.length; i++){
     var classPayload = classFeeStats.classFeePaymentAggregatedDatas[i];
     labelArr.push(classPayload.standardName);
     totalFeeDataArr.push(classPayload.assignedAmount);
     collectedFeeDataArr.push(classPayload.collectedAmount);
     discountFeeDataArr.push(classPayload.discountAmount);
     dueFeeDataArr.push(classPayload.dueAmount);
   }

   instituteStatistics.renderStudentFeesCountChart(labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr);

 },

 renderStudentFeesCountChart : function (labelArr, totalFeeDataArr, collectedFeeDataArr, discountFeeDataArr, dueFeeDataArr ) {
   // Bar chart
   new Chart($("#chartjs-class-fee-distribution"), {
     type: "bar",
     data: {
       labels: labelArr,
       datasets: [{
          label: "Collected Fees",
          backgroundColor: window.theme.success,
          borderColor: window.theme.success,
          hoverBackgroundColor: window.theme.success,
          hoverBorderColor: window.theme.success,
          data: collectedFeeDataArr,
          barPercentage: .325,
          categoryPercentage: .5
        },{
         label: "Discounted Fees",
         backgroundColor: window.theme.warning,
         borderColor: window.theme.warning,
         hoverBackgroundColor: window.theme.warning,
         hoverBorderColor: window.theme.warning,
         data: discountFeeDataArr,
         barPercentage: .325,
         categoryPercentage: .5
       },
       {
        label: "Due Fees",
        backgroundColor: window.theme.danger,
        borderColor: window.theme.danger,
        hoverBackgroundColor: window.theme.danger,
        hoverBorderColor: window.theme.danger,
        data: dueFeeDataArr,
        barPercentage: .325,
        categoryPercentage: .5
      }
       ]
     },
     options: {
       maintainAspectRatio: false,
       cornerRadius: 15,
       legend: {
         display: false
       },
       scales: {
         yAxes: [{
           ticks: {
                beginAtZero: true
           },
           gridLines: {
             display: false
           },
           stacked: false,
           stacked: true,
         }],
         xAxes: [{
           stacked: false,
           gridLines: {
             color: "transparent"
           },
           stacked: true,
         }]
       }
     }
   });
 },

 renderStudentCountChart : function (labelArr, dataArr) {
   // Bar chart
   var studentCountChart = new Chart($("#chartjs-student-distribution"), {
     type: "bar",
     data: {
       labels: labelArr,
       datasets: [{
         label: "Student Count",
         backgroundColor: PRIMARY_LOGO_COLOR,
         borderColor: PRIMARY_LOGO_COLOR,
         hoverBackgroundColor: PRIMARY_LOGO_COLOR,
         hoverBorderColor: PRIMARY_LOGO_COLOR,
         data: dataArr,
         barPercentage: .325,
         categoryPercentage: .5
       }]
     },
     options: {
       maintainAspectRatio: false,
       cornerRadius: 15,
       legend: {
         display: false
       },
       scales: {
         yAxes: [{
           ticks: {
                beginAtZero: true
           },
           gridLines: {
             display: false
           },
           stacked: false,
           stacked: true,
         }],
         xAxes: [{
           stacked: false,
           gridLines: {
             color: "transparent"
           },
           stacked: true,
         }]
       }
     }
   });
 },

 generateBirthdayCertificate : function (ref) {
   var student = JSON.parse($(ref).find(".student-info").text());
   var sessionId = student.studentAcademicSessionInfoResponse.academicSession.academicSessionId;
   var studentId = student.studentId;
   window.open(baseURL + "/adminportal/generate-birthday-certificate/"+sessionId+"/"+studentId, '_blank');
 },

};

function getPercentage(number, total){
  return parseFloat(((number / total) * 100).toFixed(1));
}

function bindPaymentTransactionClickEvents() {
  $(".invoice-payment-transaction").on('click', function () {
      var transactionId = $(this).attr("id").trim();
      generateFeePaymentInvoice(transactionId);
  });
}

function generateFeePaymentInvoice(transactionId) {
    window.open(baseURL + "/fees/invoice/" + transactionId+"/pdf-summary", '_blank');
}