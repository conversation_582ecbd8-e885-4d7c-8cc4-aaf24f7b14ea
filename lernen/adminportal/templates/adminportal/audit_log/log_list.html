<div id="audit-log-fixed-height-list-wrapper">
    {% load displaytime %}
    {% load json %}
    <div class="table-responsive">
      <table class="table table-hover">
      <caption>Audit Logs</caption>
      <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col" >User Id</th>
            <th scope="col">Name</th>
            <th scope="col">Module</th>
            <th scope="col">Action</th>
            <th scope="col">Date</th>
            <th scope="col">Log</th>
            <th scope="col" colspan="1">Details</th>
        </tr>
      </thead>
      <tbody>
        {% for audit_log in audit_logs %}
            <tr>
                <th scope="row">{{ forloop.counter }}</th>
                <td>{{audit_log.user.userName}}</td>
                <td>{{audit_log.user.firstName|title}}</td>
                <td>{{audit_log.audiLogActionData.module|title}}</td>
                <td>{{audit_log.audiLogActionData.auditLogActionDisplay}}</td>
                <td>{{audit_log.logTime|print_date}}</td>
                <td>{{audit_log.miniLogStatement}}</td>
                <td>
                  <button id={{audit_log.logId}} type="button" class="btn btn-outline-primary btn-sm view-log-details" data-toggle="modal">View Details</button>
                </td>
            </tr>
        {% endfor %}
      </tbody>
    </table>
    </div>
</div>
<div id="fixed-pagination-content" style="padding-top: 5px; ">{% include 'core/utils/pagination.html'%}</div>
