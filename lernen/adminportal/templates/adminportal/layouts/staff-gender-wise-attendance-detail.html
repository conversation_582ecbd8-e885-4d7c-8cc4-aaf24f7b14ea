<div class="staff-attendance-data-div">
    <nav>
        <div class="nav nav-pills" id="nav-tab" role="tablist">
            <a class="nav-item nav-link active" id="nav-male-data-tab" data-toggle="tab" href="#nav-male-data" role="tab" aria-controls="nav-male-data" aria-selected="true">Male</a>
            <a class="nav-item nav-link" id="nav-female-data-tab" data-toggle="tab" href="#nav-female-data" role="tab" aria-controls="nav-female-data" aria-selected="false">Female</a>
        </div>
    </nav>
   <br>
    <div class="tab-content">
        <div class="tab-pane fade show active" id="nav-male-data" role="tabpanel" aria-labelledby="nav-male-data-tab">
            <table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
                <thead>
                    <tr>
                        <th scope="col">S.No.</th>
                        <th scope="col">Staff Institute Id</th>
                        <th scope="col">Staff Name</th>
                        <th scope="col">Category</th>
                    </tr>
                </thead>
                <tbody id="male-staff-data">
                    <!-- Male staff data will be rendered here -->
                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="nav-female-data" role="tabpanel" aria-labelledby="nav-female-data-tab">
            <table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
                <thead>
                    <tr>
                        <th scope="col">S.No.</th>
                        <th scope="col">Staff Institute Id</th>
                        <th scope="col">Staff Name</th>
                        <th scope="col">Category</th>
                    </tr>
                </thead>
                <tbody id="female-staff-data">
                    <!-- Female staff data will be rendered here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
