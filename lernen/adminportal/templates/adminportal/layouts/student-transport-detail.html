<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table">
    <thead>
        <tr>
            <th scope="col">S.No.</th>
            <th scope="col">Admission Number</th>
            <th scope="col">Student Name</th>
            <th scope="col">Father Name</th>
            <th scope="col">Class</th>
            <th scope="col">Stoppage Name</th>
            <th scope="col">Pick Up Route</th>
            <th scope="col">Drop Route</th>
        </tr>
    </thead>
    <tbody>
        {% if student_transport_info_list %}
        {% for transport in student_transport_info_list %}
        <tr>
            <th scope="row">{{ forloop.counter }}</th>
            <td>{{ transport.student.admissionNumber }}</td>
            <td>{{ transport.student.name }}</td>
            <td>{% if transport.student.fathersName %}{{ transport.student.fathersName }} {% endif %}</td>
            <td>{{ transport.student.studentSessionData.standardNameWithSection }}</td>
            <td>{{ transport.studentTransportData.transportArea.area }}</td>
            <td>{% if transport.studentTransportData and  transport.studentTransportData.pickupTransportServiceRouteMetadata %} {{ transport.studentTransportData.pickupTransportServiceRouteMetadata.serviceRouteName }} {% endif %}</td>
            <td>{% if transport.studentTransportData and  transport.studentTransportData.dropTransportServiceRouteMetadata %} {{ transport.studentTransportData.dropTransportServiceRouteMetadata.serviceRouteName }}  {% endif %}</td>
        </tr>
        {% endfor %}
        {% endif %}
    </tbody>
</table>

