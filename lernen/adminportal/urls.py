from django.contrib import admin
from django.urls import path
from django.urls import include
from adminportal.views import *

urlpatterns = [
    path('home', home_page_view, name='home_page_view'),
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('change-password', change_password_view, name='change_password_view'),
    path('update-password', update_password_view, name='update_password_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),
    path('mark-list-opened',  mark_list_opened_view, name='mark_list_opened_view'),
    path('get-bell-notification-list/<offset>/<limit>',  get_bell_notification_list, name='get_bell_notification_list'),
    path('mark-as-read',  mark_as_read_view, name='mark_as_read_view'),
    path('student-transport-info/<academic_session_id>', student_transport_info_view, name='student_transport_info_view'),
    path('generate-birthday-certificate/<academic_session_id>/<student_id>', generate_birthday_certificate_view, name='generate_birthday_certificate_view'),

]
