/*
    DEMO STYLE
*/
@import "https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700";


/* Error border colour - "border","1px solid #ff8795"
Error message color - color:#e65f76 */

body {
    font-family: '<PERSON><PERSON> Slab';
    background: #ffffff;
}

input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 30px white inset;
}
p {
    font-family: '<PERSON><PERSON> Slab';
    font-size: 1.1em;
    font-weight: 300;
    line-height: 1.7em;
    color: #999;
}

a, a:hover, a:focus {
    color: inherit;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
}

.badge .close{
		margin-left: .25rem;
		color: inherit;
		font-size: 100%;
		text-shadow: 0 1px 0 rgba(#000, .5);
}
/*
.navbar {
    padding: 15px 10px;
    background: #fff;
    border: none;
    border-radius: 0;
    margin-bottom: 40px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
} */

/* .navbar-btn {
    box-shadow: none;
    outline: none !important;
    border: none;
} */

.line {
    width: 100%;
    height: 1px;
    border-bottom: 1px dashed #ddd;
    margin: 40px 0;
}

i, span {
    display: inline-block;
}


/* ---------------------------------------------------
    SIDEBAR STYLE
----------------------------------------------------- */
.wrapper {
    display: flex;
    align-items: stretch;
}

#sidebar {
    min-width: 200px;
    max-width: 200px;
    background: #2d3136;
    color: #fff;
    transition: all 0.3s;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    overflow-x: hidden;
    height: 100%;
}

#sidebar.active {
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}

#sidebar.active .sidebar-header h3, #sidebar.active .CTAs {
    display: none;
}

#sidebar.active .sidebar-header strong {
    display: block;
}

#sidebar.active .sidebar-header h5 {
    display: block;
}

#sidebar ul li a img {
    display: inline-block;
    padding-right:  18px;
    font-size: 1.8em;
    padding-bottom: 5px;
}

#sidebar.active ul li a img {
    /* margin-left: :  20px; */
    display: block;
    padding-left: 20px;
    padding-bottom: 8px
}

#sidebar ul li a {
    text-align: left;
    color: #ececed;
}

#sidebar.active ul li a {
    padding: 20px 10px;
    text-align: center;
    font-size: 0.1em;
}

#sidebar.active ul li a i {
    margin-right:  0;
    display: block;
    font-size: 1.8em;
    margin-bottom: 5px;
}

#sidebar.active ul ul a {
    padding: 10px !important;
}

#sidebar.active a[aria-expanded="false"]::before, #sidebar.active a[aria-expanded="true"]::before {
    top: auto;
    bottom: 5px;
    right: 50%;
    -webkit-transform: translateX(50%);
    -ms-transform: translateX(50%);
    transform: translateX(50%);
}

#sidebar .sidebar-header {
    padding: 0px;
    background: #1a1a1a;
}

#sidebar .sidebar-header strong {
    display: none;
    font-size: 1.8em;
}

#sidebar .sidebar-header h5 {
    display: none;
}

#sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #ffffff;
}

#sidebar ul li a {
    padding: 12px;
    font-size: 0.9em;
    display: block;
    color: #e4e5e5
}

#sidebar.active ul li a {
    padding: 10px;
    font-size: 0.9em;
    display: block;
    color: #e4e5e5
}

#sidebar ul li a:hover {
    /* color: #7386D5; */
    /* color: #7386D5; */
    background: #34383e;
    color: #2984c7;
    border-left: 3px solid #2984c7;
}

#sidebar ul li a i {
    margin-right: 10px;
}

#sidebar ul li.active > a, a[aria-expanded="true"] {
    color: #2984c7;   /*a6d785*/
    background: #34383e;
    border-left: 3px solid #2984c7;
}


a[data-toggle="collapse"] {
    position: relative;
}

a[aria-expanded="false"]::before, a[aria-expanded="true"]::before {
    content: '\e259';
    display: block;
    position: absolute;
    right: 20px;
    /* font-family: 'Glyphicons Halflings'; */
    font-size: 0.6em;
}
a[aria-expanded="true"]::before {
    content: '\e260';
}


ul ul a {
    font-size: 0.9em !important;
    padding-left: 30px !important;
    background: #1a1a1a;
}

ul.CTAs {
    padding: 20px;
}

ul.CTAs a {
    text-align: center;
    font-size: 0.9em !important;
    display: block;
    border-radius: 5px;
    margin-bottom: 5px;
}

a.download {
    background: #fff;
    color: #7386D5;
}

a.article, a.article:hover {
    background: #6d7fcc !important;
    color: #fff !important;
}



/* ---------------------------------------------------
    CONTENT STYLE
----------------------------------------------------- */
#content {
    padding: 20px;
    min-height: 50vh;
    transition: all 0.3s;
    margin-left: 200px;
    margin-top: 40px;
    width: 100%;
}

#company_name_footer{
    position: fixed;
    bottom: 0;
}

#company_name_footer.active{
    display: none;
}

/* ---------------------------------------------------
    MEDIAQUERIES
----------------------------------------------------- */
@media (max-width: 768px) {
    #sidebar {
        min-width: 80px;
        max-width: 80px;
        text-align: center;
        margin-left: -80px !important ;
    }
    a[aria-expanded="false"]::before, a[aria-expanded="true"]::before {
        top: auto;
        bottom: 5px;
        right: 50%;
        -webkit-transform: translateX(50%);
        -ms-transform: translateX(50%);
        transform: translateX(50%);
    }
    #sidebar.active {
        margin-left: 0 !important;
    }

    #sidebar .sidebar-header h3, #sidebar .CTAs {
        display: none;
    }

    #sidebar .sidebar-header strong {
        display: block;
    }

    #sidebar ul li a {
        padding: 20px 10px;
    }

    #sidebar ul li a span {
        font-size: 0.85em;
    }
    #sidebar ul li a i {
        margin-right:  0;
        display: block;
    }

    #sidebar ul ul a {
        padding: 10px !important;
    }

    #sidebar ul li a i {
        font-size: 1.3em;
    }
    #sidebar {
        margin-left: 0;
    }
    #sidebarCollapse span {
        display: none;
    }

    #content {
        padding: 20px;
        min-height: 100vh;
        transition: all 0.3s;
        margin-left: 20px;
        margin-top: 70px;
    }

}
