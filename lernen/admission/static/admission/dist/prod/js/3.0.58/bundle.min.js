var regexEmail=/^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,regexPhoneNumber=/^\d{10}$/,regexAadharNumber=/^\d{12}$/,regexAge=/^(0?[1-9]|[1-9][0-9]|[1][0-1][0-9]|120)$/;function loadAdmitStudentMenu(){ajaxClient.get("/admission/add-student-view",function(t){$("#main-content").html(t),admission.bindSessionChangeEvent(),bindAdmissionClassSectionEvent(),bindRemoveErrorDisplayEvent(),initDate(10950),previousTabSwtichingEvent(),nextTabSwtichingEvent(),enforceConstraints(),permanentAddressUpdate("permanent-same-as-present","student-permanent-address","student-city","student-state","student-post-office","student-police-station","student-zipcode","student-present-address","student-present-city","student-present-state","student-present-post-office","student-present-police-station","student-present-zipcode"),$(document).ready(function(){$('[data-toggle="tooltip"]').tooltip()})})}var admission={dataCache:{},bindSessionChangeEvent:function(){$("#add-student-academic-session").change(function(){var t=$(this).find(":selected").val().trim();ajaxClient.get("/admission/standards/"+t,function(t){$(".student-standard-json-wrapper").html(t),fillStudentSections($("#add-student-class").find(":selected").val().trim()),admission.updateInstituteHouses()})})},updateInstituteHouses:function(){var t=$("#add-student-academic-session").find(":selected").val().trim();ajaxClient.get("/admission/institute-houses/"+t,function(t){$("#institute-houses-info-button").html(t),$(document).ready(function(){$('[data-toggle="tooltip"]').tooltip()})})}};function bindAdmissionClassSectionEvent(){$(".student-class").change(function(){fillStudentSections($(this).find(":selected").val().trim())})}function loadCreateStudentConfirmModal(){if(validateMandatoryFields($("#add\\.basic-info-content")))showErrorDialogBox("Invalid student information. Please try again.");else{var t=$("#add-student-academic-session").find(":selected").val().trim(),e=$("#add-student-class").find(":selected").val().trim();ajaxClient.get("/admission/default-fee-assignment-structure/"+t+"/"+e,function(t){$("#create-student-confirm-modal-container").html(t),$("#create-student-confirm-modal").modal({backdrop:"static",keyboard:!1})})}}function createStudent(){if($("#create-student-confirm-modal").modal("toggle"),validateMandatoryFields($("#add\\.basic-info-content")))showErrorDialogBox("Invalid student information. Please try again.");else{var t=$("#add-student-academic-session").find(":selected").val().trim(),e=$("#add-student-class").find(":selected").val().trim(),a=$("#add-student-section").find(":selected").val().trim();""==a&&(a=null);var n=getStudentBasicInfo();if(void 0!==n){console.log(n);0;0;0;var s=[];$("#applicable-registration-fees").find("input.applicable-fee-structure-assign-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).attr("id");s.push(t)}});var i={studentPayload:{admissionAcademicSession:t,standardId:e,sectionId:a,studentBasicInfo:n,studentFamilyInfo:null,studentGuardianInfoList:[],studentPreviousSchoolInfo:null,studentMedicalInfo:null,newAdmission:$("#student-new-admission").is(":checked")},feeStructureIds:s},o=new FormData;0,o.append("registerStudentPayload",JSON.stringify(i)),ajaxClient.uploadFile("/admission/add-new-student",o,function(t){$("#admission-status-modal-container").html(t);var e=JSON.parse($("#student-data-response").text().trim());e.success&&(fillStudentInformation(e.student),$("#update-student-family-info").trigger("click"),$(".add-student-screen").attr("style","display:none"),$("#add\\.update-student-screen").attr("style","display:block"),$("#screen-name").text("ADMIT-STUDENT")),enforceConstraints(),$("#admission-status-modal").modal("toggle")})}}}function getStudentBasicInfo(){var t=$("#student-registration-number").val().trim(),e=getDate($("#student-admission-date").val()),a=getDate($("#student-registration-date").val()),n=$("#student-name").val().trim(),s=$("#student-gender").val().trim();""==s&&(s=null);var i=getDate($("#student-date-of-birth").val()),o=$("#student-birth-place").val().trim(),r=$("#student-category").val().trim();""==r&&(r=null);var d=$("#student-religion").val().trim(),l=$("#student-caste").val().trim(),u=$("#student-rte").is(":checked"),c=$("#student-mother-tongue").val().trim(),p=$("#student-area-type").val().trim();""==p&&(p=null);var m=$("#student-specially-abled").is(":checked"),f=$("#student-bpl").is(":checked"),g=$("#student-present-address").val().trim(),h=$("#student-present-city").val().trim(),v=null==$("#student-present-state").find(".country-states").find(":selected").val()?"":$("#student-present-state").find(".country-states").find(":selected").val().trim(),b=$("#student-present-post-office").val().trim(),S=$("#student-present-police-station").val().trim(),y=$("#student-present-zipcode").val().trim(),C=$("#student-permanent-address").val().trim(),w=$("#student-city").val().trim(),I=null==$("#student-state").find(".country-states").find(":selected").val()?"":$("#student-state").find(".country-states").find(":selected").val().trim(),D=$("#student-post-office").val().trim(),E=$("#student-police-station").val().trim(),x=$("#student-zipcode").val().trim(),N=null;null!=a&&(N=a.getTime()/1e3);var T=null;null!=e&&(T=e.getTime()/1e3);var k=null;null!=i&&(k=i.getTime()/1e3);var R=!1,_=null,P=$("#student-aadhar-number").val().trim();""==P||regexAadharNumber.test(P)||(R=!0,_="student-aadhar-number",string="Invalid Aadhar Number");var A=$("#student-nationality").val().trim(),L=$("#student-primary-contact-number").val().trim();""==L||regexPhoneNumber.test(L)||(R=!0,_="student-primary-contact-number",string="Primary Contact Number should be of 10 digits.");var O=$("#student-primary-email").val().trim();""==O||regexEmail.test(O)||(R=!0,_="student-primary-email",string="Type correct format of Primary Email");var B=$("#student-whatsapp-number").val().trim();""==B||regexPhoneNumber.test(B)||(R=!0,_="student-whatsapp-number",string="Whatsapp Number should be of 10 digits.");var F=$("#student-is-sponsored").is(":checked"),M=$("#student-hosteller").is(":checked");if(R)return null!=_&&$("#"+_).css("border","1px solid #ff8795"),void showErrorDialogBox(string);var q=$("#add-student-admission-in-class").val().trim(),j=$("#add-student-name-as-per-aadhar").val().trim(),H=$("#add-child-category-criteria").val().trim();""==H&&(H=null);var G=$("#add-specially-abled-type").val().trim(),U=$("#add-pen-number").val().trim(),W=$("#add-apaar-id-number").val().trim();return{registrationRequestNumber:t+"-"+Math.random().toString(36),registrationNumber:t,registrationDate:N,admissionDate:T,name:n,gender:s,dateOfBirth:k,birthPlace:o,aadharNumber:P,userCategory:r,religion:d,caste:l,rte:u,motherTongue:c,areaType:p,speciallyAbled:m,bpl:f,presentAddress:g,presentCity:h,presentState:v,presentPostOffice:b,presentPoliceStation:S,presentZipcode:y,permanentAddress:C,permanentCity:w,permanentState:I,permanentPostOffice:D,permanentPoliceStation:E,permanentZipcode:x,nationality:A,primaryContactNumber:L,primaryEmail:O,isSponsored:F,hosteller:M,whatsappNumber:B,instituteHouseId:null,admissionInClass:q,speciallyAbledType:G,studentNameAsPerAadhar:j,childCategoryCriteria:H,penNumber:U,apaarIdNo:W}}function getStudentBasicInfoUpdate(){var t=$("#update\\.student-registration-number").val().trim(),e=$("#update\\.student-admission-number").val().trim(),a=$("#update\\.pen-number").val().trim(),n=$("#update\\.apaar-id-number").val().trim(),s=getDate($("#update\\.student-admission-date").val()),i=getDate($("#update\\.student-registration-date").val()),o=$("#update\\.student-name").val().trim(),r=$("#update\\.student-gender").val().trim();""==r&&(r=null);var d=getDate($("#update\\.student-date-of-birth").val()),l=$("#update\\.student-birth-place").val().trim(),u=$("#update\\.student-category").val().trim();""==u&&(u=null);var c=$("#update\\.student-religion").val().trim(),p=$("#update\\.student-caste").val().trim(),m=$("#update\\.student-rte").is(":checked"),f=$("#update\\.student-mother-tongue").val().trim(),g=$("#update\\.student-area-type").val().trim();""==g&&(g=null);var h=$("#update\\.student-specially-abled").is(":checked"),v=$("#update\\.student-bpl").is(":checked"),b=$("#update\\.student-present-address").val().trim(),S=$("#update\\.student-present-city").val().trim(),y=null==$("#update\\.student-present-state").find(".country-states").find(":selected").val()?"":$("#update\\.student-present-state").find(".country-states").find(":selected").val().trim(),C=$("#update\\.student-present-post-office").val().trim(),w=$("#update\\.student-present-police-station").val().trim(),I=$("#update\\.student-present-zipcode").val().trim(),D=$("#update\\.student-permanent-address").val().trim(),E=$("#update\\.student-city").val().trim(),x=null==$("#update\\.student-state").find(".country-states").find(":selected").val()?"":$("#update\\.student-state").find(".country-states").find(":selected").val().trim(),N=$("#update\\.student-post-office").val().trim(),T=$("#update\\.student-police-station").val().trim(),k=$("#update\\.student-zipcode").val().trim(),R=null;null!=i&&(R=i.getTime()/1e3);var _=null;null!=s&&(_=s.getTime()/1e3);var P=null;null!=d&&(P=d.getTime()/1e3);var A=!1,L=null,O=$("#update\\.student-aadhar-number").val().trim();""==O||regexAadharNumber.test(O)||(A=!0,L="student-aadhar-number",string="Invalid Aadhar Number");var B=$("#update\\.student-nationality").val().trim(),F=$("#update\\.student-primary-contact-number").val().trim();""==F||regexPhoneNumber.test(F)||(A=!0,L="student-primary-contact-number",string="Primary Contact Number should be of 10 digits.");var M=$("#update\\.student-primary-email").val().trim();""==M||regexEmail.test(M)||(A=!0,L="student-primary-email",string="Type correct format of Primary Email");var q=$("#update\\.student-whatsapp-number").val().trim();""==q||regexPhoneNumber.test(q)||(A=!0,L="student-whatsapp-number",string="Whatsapp Number should be of 10 digits.");var j=$("#update\\.student-is-sponsored").is(":checked"),H=$("#update\\.student-hosteller").is(":checked");if(A)return null!=L&&$("#"+L).css("border","1px solid #ff8795"),void showErrorDialogBox(string);var G=$("#update\\.student-house").val();null!=G&&""!==G||(G=null);var U=$("#update\\.student-admission-in-class").val().trim(),W=$("#update\\.student-name-as-per-aadhar").val().trim(),Y=$("#update\\.child-category-criteria").val();return Y=""==Y?null:Y.trim(),{registrationNumber:t,admissionNumber:e,penNumber:a,apaarIdNo:n,registrationDate:R,admissionDate:_,name:o,gender:r,dateOfBirth:P,birthPlace:l,aadharNumber:O,userCategory:u,religion:c,caste:p,rte:m,motherTongue:f,areaType:g,speciallyAbled:h,bpl:v,presentAddress:b,presentCity:S,presentState:y,presentPostOffice:C,presentPoliceStation:w,presentZipcode:I,permanentAddress:D,permanentCity:E,permanentState:x,permanentPostOffice:N,permanentPoliceStation:T,permanentZipcode:k,nationality:B,primaryContactNumber:F,primaryEmail:M,isSponsored:j,hosteller:H,whatsappNumber:q,instituteHouseId:G,admissionInClass:U,speciallyAbledType:$("#update\\.specially-abled-type").val().trim(),studentNameAsPerAadhar:W,childCategoryCriteria:Y}}function getStudentFamilyInfo(){var t=$("#student-mother-name").val().trim(),e=$("#student-father-name").val().trim(),a=$("#student-mother-qualification").val().trim(),n=$("#student-father-qualification").val().trim(),s=!1,i=null,o=$("#mother-contact-number").val().trim();""==o||regexPhoneNumber.test(o)||(s=!0,i="mother-contact-number",string="Mother Contact Number should be of 10 digits.");var r=$("#father-contact-number").val().trim();""==r||regexPhoneNumber.test(r)||(s=!0,i="father-contact-number",string="Father Contact Number should be of 10 digits.");var d=$("#mother-occupation").val().trim(),l=$("#father-occupation").val().trim(),u=$("#mother-aadhar-number").val().trim();""==u||regexAadharNumber.test(u)||(s=!0,i="mother-aadhar-number",string="Invalid Mother Aadhar Number");var c=$("#father-aadhar-number").val().trim();""==c||regexAadharNumber.test(c)||(s=!0,i="father-aadhar-number",string="Invalid Father Aadhar Number");var p=$("#mother-pan-card-details").val().trim(),m=$("#father-pan-card-details").val().trim(),f=$("#approx-family-income").val().trim();f<0&&(s=!0,i="approx-family-income",string="Please put the proper value for the income");var g=$("#mother-annual-income").val().trim();g<0&&(s=!0,i="mother-annual-income",string="Please put the proper value for the income");var h=$("#father-annual-income").val().trim();return h<0&&(s=!0,i="father-annual-income",string="Please put the proper value for the income"),s?(null!=i&&$("#"+i).css("border","1px solid #ff8795"),void showErrorDialogBox(string)):{mothersName:t,fathersName:e,mothersQualification:a,fathersQualification:n,mothersContactNumber:o,fathersContactNumber:r,mothersOccupation:d,mothersAnnualIncome:g,fathersOccupation:l,fathersAnnualIncome:h,mothersAadharNumber:u,fathersAadharNumber:c,mothersPanCardDetails:p,fathersPanCardDetails:m,approxFamilyIncome:f}}function getStudentGuardianInfo(){var t=!1,e=null,a=$("#guardian-name").val().trim(),n=$("#guardian-relation").val().trim(),s=0==$("#guardian-age").val().length?null:$("#guardian-age").val().trim();null==s||regexAge.test(s)||(t=!0,e="guardian-age",string="Invalid Age");var i=$("#guardian-gender").val().trim(),o=$("#guardian-occupation").val().trim(),r=$("#guardian-email").val().trim();""==r||regexEmail.test(r)||(t=!0,e="guardian-email",string="Type correct format of guardian Email");var d=$("#guardian-contact-number").val().trim();""==d||regexPhoneNumber.test(d)||(t=!0,e="guardian-contact-number",string="Guardian contact Number should be of 10 digits.");var l=$("#guardian-address").val().trim(),u=$("#guardian-city").val().trim(),c=null==$("#guardian-state").find(".country-states").find(":selected").val()?"":$("#guardian-state").find(".country-states").find(":selected").val().trim(),p=$("#guardian-zipcode").val().trim();return t?(null!=e&&$("#"+e).css("border","1px solid #ff8795"),void showErrorDialogBox(string)):""!=i?{guardianName:a,relation:n,age:s,gender:i,occupation:o,email:r,contactNumber:d,occupation:o,address:l,city:u,state:c,zipcode:p}:{guardianName:a,relation:n,age:s,occupation:o,email:r,contactNumber:d,occupation:o,address:l,city:u,state:c,zipcode:p}}function getStudentPreviousSchoolInfo(){var t=!1,e=null,a=$("#student-is-admission-tc-based").is(":checked"),n=$("#previous-school-tc-number").val().trim(),s=$("#previous-school-name").val().trim(),i=$("#previous-school-medium").val().trim(),o=$("#class-passed").val().trim(),r=0==$("#year-of-passing").val().length?null:$("#year-of-passing").val().trim();r<0&&(t=!0,e="year-of-passing",string="Invalid Year of Passing");var d=$("#result").val().trim(),l=$("#percentage").val().trim();return t?(null!=e&&$("#"+e).css("border","1px solid #ff8795"),void showErrorDialogBox(string)):{isAdmissionTcBased:a,tcNumber:n,schoolName:s,medium:i,classPassed:o,yearOfPassing:r,result:d,percentage:l}}function getStudentMedicalInfo(){var t=$("#blood-group").val().trim(),e=$("#blood-pressure").val().trim(),a=$("#pulse").val().trim(),n=$("#height").val().trim(),s=0==$("#weight").val().length?null:$("#weight").val().trim(),i=getDate($("#date-of-physical-examination").val()),o=null;null!=i&&(o=i.getTime()/1e3);return""==t?{bloodPressure:e,pulse:a,height:n,weight:s,dateOfPhysicalExamination:i}:{bloodGroup:t,bloodPressure:e,pulse:a,height:n,weight:s,dateOfPhysicalExamination:o}}function updateStudent(t){updateStudentWithScreen(t,"STUDENT-LIST")}function updateStudentWithScreen(t,e){var a=validateMandatoryFields($("#update-basic-info-content"));if(a|=validateMandatoryFields($("#update-family-info-content")),a|=validateMandatoryFields($("#update-guardian-info-content")),a|=validateMandatoryFields($("#update-previous-school-info-content")),a|=validateMandatoryFields($("#update-medical-info-content")))showErrorDialogBox("Please fill mandatory fields.");else{var n=$("#update-admission-student-id").text().trim(),s=$("#update-student-class").find(":selected").val().trim(),i=$("#update-student-section").find(":selected").val().trim();""==i&&(i=null);var o=$("#update-student-academic-session").find(":selected").val().trim(),r=[],d=getStudentBasicInfoUpdate();if(void 0!==d){console.log(d);var l=getStudentFamilyInfo();if(void 0!==l){var u=getStudentGuardianInfo();if(void 0!==u){var c=getStudentPreviousSchoolInfo();if(void 0!==c){var p=getStudentMedicalInfo();if(void 0!==p){r.push(u);var m=getStudentDocumentInformation(),f={studentId:n,standardId:s,sectionId:i,studentBasicInfo:d,studentFamilyInfo:l,studentGuardianInfoList:r,studentPreviousSchoolInfo:c,studentMedicalInfo:p,newAdmission:$("#update\\.student-new-admission").is(":checked"),collectedStudentDocumentType:m},g=new FormData;0,g.append("updateStudentPayload",JSON.stringify(f)),console.log(e),ajaxClient.uploadFile("/admission/update-student/"+o,g,function(e){$("#admission-status-modal-container").html(e),JSON.parse($("#student-data-response").text()).success?t?(returnToMainScreen(),$("#student-document-warning-popup").modal("toggle")):switchToNextTab():$("#admission-status-modal").modal("toggle")})}}}}}}}function getStudentDocumentInformation(){var t=[];return $("input.student-document-collected-checkbox").each(function(){if($(this).is(":checked")){var e=$(this).parent().parent().parent().attr("id");t.push(e)}}),t}var sidebarFull=200,sidebarCollapsed=80,graphLibraryLoaded=!1,ALL_ENROLLED_STUDENT_SCREEN_ID="all-enrolled-student-screen",NSO_STUDENTS_LIST_SCREEN_ID="nso-student-list-screen",NEW_ADMISSION_MALE_COLOR="#93c47d",NEW_ADMISSION_FEMALE_COLOR="#cbe6c0",NEW_ADMISSION_TRANSGENDER_COLOR="#E69138",LOGO_COLOR="#43a2ad",OLD_STUDENTS_FEMALE_COLOR="#bfe9ef",LIGHT_GREY_COLOR="#F3F3F3",STUDENT_DOCUMENT_TYPE=["TRANSFER_CERTIFICATE","AADHAR_CARD","STUDENT_PROFILE_IMAGE","FATHER_PROFILE_IMAGE","MOTHER_PROFILE_IMAGE","RATION_CARD","PREVIOUS_SCHOOL_STUDY_CERTIFICATE","BIRTH_CERTIFICATE","STUDENT_PAN_CARD","BIRTH_CERTIFICATE_AND_AFFIDAVIT"];$(document).ready(function(){menuLoader.registerSidebarMenu();var t=new URLSearchParams(window.location.search);if(actionType=t.get(ACTION_TYPE),null!=actionType&&null!=actionType&&""!=actionType){"admit-student"===actionType&&($("#admitStudentNav").parent().addClass("active"),loadAdmitStudentMenu());var e=window.location.href;url=removeURLParameter(e,ACTION_TYPE);let t={id:"100"};window.history.replaceState(t,e,url)}paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerAdmitStudentMenu(),menuLoader.registerPendingEnrolmentsMenu(),menuLoader.registerAllStudentMenu(),menuLoader.registerStudentDetailsMenu(),menuLoader.registerNSOStudentDetailsMenu(),menuLoader.registerRelievedStudentDetailsMenu(),menuLoader.registerSiblingDetailsNav(),menuLoader.registerStudentRegistrationDetailsMenu(),menuLoader.registerReportsDetailsMenu(),menuLoader.registerIdentityCardsMenu(),menuLoader.registerAdmissionEnquiryMenu()},registerHomeMenu:function(){$("#statisticsNav").on("click",function(){statistics.loadHomePage()})},registerAdmitStudentMenu:function(){$("#admitStudentNav").on("click",function(){loadAdmitStudentMenu()})},registerPendingEnrolmentsMenu:function(){$("#pendingEnrolmentsNav").on("click",function(){loadPendingEnrolmentsPage()})},registerStudentDetailsMenu:function(){$("#studentDetailsNav").on("click",function(){loadStudentDetailsPage()})},registerAllStudentMenu:function(){$("#relieveStudentNav").on("click",function(){loadAllStudentsPage()})},registerRelievedStudentDetailsMenu:function(){$("#relievedStudentDetailsNav").on("click",function(){loadRelievedStudentDetailsPage()})},registerNSOStudentDetailsMenu:function(){$("#nsoStudentDetailsNav").on("click",function(){loadNSOStudentDetailsPage()})},registerStudentRegistrationDetailsMenu:function(){$("#registrationStudentsDetailsNav").on("click",function(){studentRegistration.loadHomePage()})},registerSiblingDetailsNav:function(){$("#siblingDetailsNav").on("click",function(){siblingDetails.loadHomePage()})},registerReportsDetailsMenu:function(){$("#reportsNav").on("click",function(){admissionReports.loadMainScreen()})},registerIdentityCardsMenu:function(){$("#identityCardsNav").on("click",function(){identityCards.loadMainScreen()})},registerAdmissionEnquiryMenu:function(){$("#admissionEnquiryDetailsNav").on("click",function(){admissionEnquiry.loadMainScreen()})}},statistics={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(statistics.loadHomePageForSession),statistics.displayDashboardContent(),statistics.onClickAdmissionStatsViewSwitchButton()},loadHomePage:function(){ajaxClient.get("/admission/home",function(t){$("#main-content").html(t),statistics.initHomePage()})},loadStudentList:function(t){var e=readJson("#admission-home-stats");if(null!=e){var a,n='<table id="datatables-reponsive" class="table table-bordered table-striped datatables-reponsive-table"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Sr No.</th><th scope="col">Admission Date</th><th scope="col">Admission Number</th><th scope="col">Student Name</th><th scope="col">Father Name</th><th scope="col">Class</th><th scope="col">Gender</th><th scope="col">DOB</th></tr></thead><tbody>';"NEW_ADMISSION"==t?a=e.newAdmissionStudentList:"RTE_STUDENT"==t&&(a=e.rteStudentList);for(var s=1,i=0;i<a.length;i++){var o=a[i].studentBasicInfo.admissionDate,r="";null!=o&&o>0&&(r=getFormattedDate(o));var d=a[i].studentBasicInfo.admissionNumber,l=a[i].studentBasicInfo.name,u="";null!=a[i].studentFamilyInfo.fathersName&&(u=a[i].studentFamilyInfo.fathersName);var c=a[i].studentAcademicSessionInfoResponse.standard.displayNameWithSection,p=a[i].studentBasicInfo.gender,m="";if(null!=a[i].studentBasicInfo.dateOfBirth){var f=a[i].studentBasicInfo.dateOfBirth;m="";null!=f&&f>0&&(m=getFormattedDate(f))}n+='<tr><th scope="row">'+s+++"</th><td>"+r+"</td><td>"+d+"</td><td>"+l+"</td> <td>"+u+"</td> <td>"+c+"</td> <td>"+p+"</td> <td>"+m+"</td></tr>"}n+="</tbody></table>",$("#admission-student-data-display").html(n),$("#datatables-reponsive").DataTable({searching:!0,bPaginate:!0,columnDefs:[{orderable:!1,targets:"no-sort"}],order:[[0,"asc"]]}),$("#display-admission-student-data-modal").modal({backdrop:"static",keyboard:!1})}},loadHomePageForSession:function(){var t=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/admission/session-home/"+t,function(t){$("#admission-dashboard-session-content").html(t),$("#admission-stats-view-type").val()===TABULAR?($("#current-card-view-type").text(TABULAR),statistics.displayTabularDashboardContent()):(statistics.displayDashboardContent(),$("#current-card-view-type").text(GRAPHICAL)),statistics.onClickAdmissionStatsViewSwitchButton()})},refreshHomePage:function(){statistics.loadHomePageForSession()},onClickAdmissionStatsViewSwitchButton:function(){$("#admission-stats-view-type").on("change",function(){$("#admission-stats-view-type").val()===TABULAR?($("#current-card-view-type").text(TABULAR),statistics.displayTabularDashboardContent()):(statistics.displayDashboardContent(),$("#current-card-view-type").text(GRAPHICAL))})},displayTabularDashboardContent:function(){var t=readJson("#admission-home-stats"),e=readJson("#home-page-standards-stats");admission_home_stats=statistics.initAdmissionHomeStats(t,e),statistics.renderTopBoxesData(admission_home_stats),statistics.renderStudentGenderCountTable(admission_home_stats),statistics.renderRTECountTable(admission_home_stats),statistics.renderNewAdmissionCountTable(admission_home_stats),statistics.renderCategoryCountTable(admission_home_stats)},initAdmissionHomeStats:function(t,e){for(var a=[],n=0;n<e.length;n++)a.push(e[n].displayName);var s=t.totalStudents,i=t.oldStudents,o=t.newRegistrationStudents,r=t.newAdmissionStudents,d=t.totalLastYearStudents,l=t.oldLastYearStudents,u=t.newRegistrationLastYearStudents,c=t.newAdmissionLastYearStudents,p=t.classWiseOldStudentGenderCountMap,m=t.classWiseNewAdmissionGenderCountMap,f=t.classWiseNewAdmissionCountMap,g=t.classWiseLastYearNewAdmissionCountMap,h=t.classWiseRteCountMap,v=t.classWiseStudentCountMap,b=t.classWiseCategoryCountMap;let $=new Map,S=new Map,y=new Map,C=new Map,w=new Map,I=new Map,D=new Map;for(n=0;n<a.length;n++){var E=a[n];isObjectEmpty(p)||$.set(E,p[E]),isObjectEmpty(m)||S.set(E,m[E]),isObjectEmpty(f)||y.set(E,f[E]),isObjectEmpty(g)||C.set(E,g[E]),isObjectEmpty(h)||w.set(E,h[E]),isObjectEmpty(v)||I.set(E,v[E]),isObjectEmpty(b)||D.set(E,b[E])}return t={totalStudents:s,oldStudents:i,newRegistrationStudents:o,newAdmissionStudents:r,totalLastYearStudents:d,oldLastYearStudents:l,newRegistrationLastYearStudents:u,newAdmissionLastYearStudents:c,classWiseOldStudentGenderCountMap:$,classWiseNewAdmissionGenderCountMap:S,classWiseNewAdmissionCountMap:y,classWiseLastYearNewAdmissionCountMap:C,classWiseRteCountMap:w,classWiseStudentCountMap:I,classWiseCategoryCountMap:D}},renderCategoryCountTable:function(t){$("#categorywise-graph-header-text").text("Categorywise Student Distribution (Tabular View)"),$("#chartjs-categorywise-student-bar-distribution").attr("style","display:none;"),$("#tabularview-categorywise-student-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;");var e=t.classWiseCategoryCountMap,a=(t.totalStudents,e.size),n=[],s=[],i=[],o=[],r=[],d=[],l=[],u=[],c=[],p=[];for(let[t,a]of e.entries()){n.push(t);var m=0;$.each(a,function(t,e){"BC"===t?s.push(e):"GENERAL"===t?i.push(e):"MIN"===t?o.push(e):"OBC"===t?r.push(e):"OTHER"===t?d.push(e):"SBC"===t?l.push(e):"SC"===t?u.push(e):"ST"===t&&c.push(e),m+=e}),p.push(m)}for(var f='<table  class="table table-bordered table-striped text-center"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Class</th><th scope="col">BC</th><th scope="col">General</th><th scope="col">MIN</th><th scope="col">OBC</th><th scope="col">OTHER</th><th scope="col">SBC</th><th scope="col">SC</th><th scope="col">ST</th><th scope="col">Total</th></tr></thead><tbody>',g=0,h=0,v=0,b=0,S=0,y=0,C=0,w=0,I=0,D=0;D<a;D++)f+='<tr><td scope="row">'+n[D]+'</td><td scope="row">'+s[D]+'</td><td scope="row">'+i[D]+'</td><td scope="row">'+o[D]+'</td><td scope="row">'+r[D]+'</td><td scope="row">'+d[D]+'</td><td scope="row">'+l[D]+'</td><td scope="row">'+u[D]+'</td><td scope="row">'+c[D]+'</td><td scope="row">'+p[D]+"</td></tr>",g+=s[D],h+=i[D],v+=o[D],b+=r[D],S+=d[D],y+=l[D],C+=u[D],w+=c[D],I+=p[D];f+='<tr><td scope="row" style="font-weight:bold">Grand Total</td><td scope="row" style="font-weight:bold">'+g+'</td><td scope="row" style="font-weight:bold">'+h+'</td><td scope="row" style="font-weight:bold">'+v+'</td><td scope="row" style="font-weight:bold">'+b+'</td><td scope="row" style="font-weight:bold">'+S+'</td><td scope="row" style="font-weight:bold">'+y+'</td><td scope="row" style="font-weight:bold">'+C+'</td><td scope="row" style="font-weight:bold">'+w+'</td><td scope="row" style="font-weight:bold">'+I+"</td></tr>",f+="</tbody></table>",$("#tabularview-categorywise-student-distribution").html(f)},renderNewAdmissionCountTable:function(t){$("#new-admission-graph-header-text").text("New Admissions (Tabular View)"),$("#chartjs-new-admission-student-bar-distribution").attr("style","display:none;"),$("#chartjs-new-admission").attr("style","display:none;"),$("#percentage-change-new-admission").attr("style","display:none;float:right;"),$("#tabularview-new-admission-student-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;");var e=t.classWiseNewAdmissionCountMap,a=t.classWiseLastYearNewAdmissionCountMap,n=e.size,s=[],i=[];for(let[t,a]of e.entries())s.push(t),i.push(a);var o=[];if(a.size<=0)for(var r=0;r<n;r++)o.push(0);else for(let[t,e]of a.entries())o.push(e);var d='<table  class="table table-bordered table-striped text-center"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Class</th><th scope="col">New Admissions</th><th scope="col">%</th></tr></thead><tbody>',l=0,u=0;for(r=0;r<n;r++){var c="";if(0==o[r])c='<span class="badge badge-soft-secondary"><i class="mdi mdi-arrow-bottom-right"></i>-</span>';else{var p=getPercentageChange(o[r],i[r]);c=p>=0?'<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(p)+'<i class="bi bi-arrow-up"></i></span>':'<span class="badge badge-soft-danger"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(p)+'<i class="bi bi-arrow-down"></i></span>'}d+='<tr><td scope="row">'+s[r]+'</td><td scope="row">'+i[r]+'</td><td scope="row">'+c+"</td></tr>",l+=parseFloat(i[r]),u+=parseFloat(c)}d+='<tr><td scope="row" style="font-weight:bold">Grand Total</td><td scope="row" style="font-weight:bold">'+l+'</td><td scope="row" style="font-weight:bold">'+(isNaN(u)?"-":u)+"</td></tr>",d+="</tbody></table>",$("#tabularview-new-admission-student-distribution").html(d)},renderRTECountTable:function(t){$("#rte-graph-header-text").text("RTE (Tabular View)"),$("#chartjs-rte-student-pie").attr("style","display:none;"),$("#percentage-rte-students").attr("style","display:none;"),$("#tabularview-rte-student-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;");var e=t.classWiseRteCountMap,a=t.classWiseStudentCountMap,n=e.size,s=[],i=[];for(let[t,e]of a.entries())s.push(t),i.push(e);var o=[];for(let[t,a]of e.entries())o.push(a);for(var r='<table  class="table table-bordered table-striped text-center"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col">Class</th><th scope="col">RTE Students</th><th scope="col">%</th></tr></thead><tbody>',d=0,l=0,u=0;u<n;u++){if(0==i[u])percentageChangeHTML='<tr><td scope="row">'+s[u]+'</td><td scope="row">'+o[u]+'</td><td scope="row">-</td></tr>';else{var c=getPercentage(o[u],i[u]);r+='<tr><td scope="row">'+s[u]+'</td><td scope="row">'+o[u]+'</td><td scope="row">'+c+"</td></tr>"}d+=parseFloat(o[u]),l+=parseFloat(c)}r+='<tr><td scope="row" style="font-weight:bold">Grand Total</td><td scope="row" style="font-weight:bold">'+d+'</td><td scope="row" style="font-weight:bold">'+l+"</td></tr>",r+="</tbody></table>",$("#tabularview-rte-student-distribution").html(r)},renderStudentGenderCountTable:function(t){$("#genderwise-graph-header-text").text("Genderwise Student Distribution (Tabular View)"),$("#chartjs-genderwise-student-bar-distribution").attr("style","display:none;"),$("#tabularview-genderwise-student-distribution").attr("style","display:block;overflow-y: auto;max-height: 450px;");var e=t.classWiseNewAdmissionGenderCountMap,a=t.classWiseOldStudentGenderCountMap,n=e.size,s=[],i=[],o=[],r=[];for(let[t,a]of e.entries()){s.push(t);var d=0;$.each(a,function(t,e){"MALE"===t?(i.push(e),d+=e):"FEMALE"===t&&(o.push(e),d+=e)}),r.push(d)}var l=[],u=[],c=[];for(let[t,e]of a.entries()){d=0;$.each(e,function(t,e){"MALE"===t?(l.push(e),d+=e):"FEMALE"===t&&(u.push(e),d+=e)}),c.push(d)}for(var p='<table  class="table table-bordered table-striped text-center"><thead style="background-color:#f2f2f2;position:sticky;top:0;z-index-1"><tr><th scope="col" rowspan="2">Class</th><th scope="col" colspan="3">New Students</th><th scope="col" colspan="3">Old Students</th><th scope="col" colspan="3">Total Students</th></tr><tr><th scope="col">M</th><th scope="col">F</th><th scope="col">Total</th><th scope="col">M</th><th scope="col">F</th><th scope="col">Total</th><th scope="col">M</th><th scope="col">F</th><th scope="col">Total</th></tr></thead><tbody>',m=0,f=0,g=0,h=0,v=0,b=0,S=0,y=0,C=0,w=0;w<n;w++)p+='<tr><td scope="row">'+s[w]+'</td><td scope="row">'+i[w]+'</td><td scope="row">'+o[w]+'</td><td scope="row">'+r[w]+'</td><td scope="row">'+l[w]+'</td><td scope="row">'+u[w]+'</td><td scope="row">'+c[w]+'</td><td scope="row">'+(i[w]+l[w])+'</td><td scope="row">'+(o[w]+u[w])+'</td><td scope="row">'+(r[w]+c[w])+"</td></tr>",m+=i[w],f+=o[w],g+=r[w],h+=l[w],v+=u[w],b+=c[w],S+=i[w]+l[w],y+=o[w]+u[w],C+=r[w]+c[w];p+='<tr><td scope="row" style="font-weight:bold">Grand Total</td><td scope="row" style="font-weight:bold">'+m+'</td><td scope="row" style="font-weight:bold">'+f+'</td><td scope="row" style="font-weight:bold">'+g+'</td><td scope="row" style="font-weight:bold">'+h+'</td><td scope="row" style="font-weight:bold">'+v+'</td><td scope="row" style="font-weight:bold">'+b+'</td><td scope="row" style="font-weight:bold">'+S+'</td><td scope="row" style="font-weight:bold">'+y+'</td><td scope="row" style="font-weight:bold">'+C+"</td></tr>",p+="</tbody></table>",$("#tabularview-genderwise-student-distribution").html(p)},displayDashboardContent:function(){var t=readJson("#admission-home-stats"),e=readJson("#home-page-standards-stats");admission_home_stats=statistics.initAdmissionHomeStats(t,e),statistics.renderTopBoxesData(admission_home_stats),statistics.renderGenderCountChartData(admission_home_stats),statistics.renderRTECountChartData(admission_home_stats),statistics.renderNewAdmissionCountChartData(admission_home_stats),statistics.renderCategoryCountChartData(admission_home_stats)},renderCategoryCountChartData:function(t){$("#categorywise-graph-header-text").text("Categorywise Student Distribution (Graphical View)"),$("#chartjs-categorywise-student-bar-distribution").attr("style","display:block;"),$("#tabularview-categorywise-student-distribution").attr("style","display:none;");var e=t.classWiseCategoryCountMap,a=t.totalStudents,n=0,s=0,i=0,o=0,r=0,d=0,l=0,u=0;for(let[t,a]of e.entries())$.each(a,function(t,e){"BC"===t?n+=e:"GENERAL"===t?s+=e:"MIN"===t?i+=e:"OBC"===t?o+=e:"OTHER"===t?r+=e:"SBC"===t?d+=e:"SC"===t?l+=e:"ST"===t&&(u+=e)});var c=[n,s,i,o,r,d,l,u],p=[a-n,a-s,a-i,a-o,a-r,a-d,a-l,a-u];statistics.renderStudentCategoryCountChart(["BC","GENERAL","MIN","OBC","OTHER","SBC","SC","ST"],c,p)},renderNewAdmissionCountChartData:function(t){$("#new-admission-graph-header-text").text("New Admissions (Graphical View)"),$("#chartjs-new-admission-student-bar-distribution").attr("style","display:block;"),$("#chartjs-new-admission").attr("style","display:block;"),$("#percentage-change-new-admission").attr("style","display:block;float:right;"),$("#tabularview-new-admission-student-distribution").attr("style","display:none;");var e=t.classWiseNewAdmissionCountMap,a=t.classWiseLastYearNewAdmissionCountMap,n=0;for(let[t,a]of e.entries())n+=a;var s=0;if(a.size<=0);else for(let[t,e]of a.entries())s+=e;var i=[];i.push(s),i.push(n);var o="";if(0==s)o='<span class="badge badge-soft-secondary"><i class="mdi mdi-arrow-bottom-right"></i>-</span>';else{var r=getPercentageChange(s,n);o=r>=0?'<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(r)+'%<i class="bi bi-arrow-up"></i></span><span class="text-muted"> from last year</span>':'<span class="badge badge-soft-danger"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(r)+'%<i class="bi bi-arrow-down"></i></span><span class="text-muted"> from last year</span>'}$("#percentage-change-new-admission").html(o),statistics.renderNewAdmissionStudentChart(["Previous Session","This Session"],i)},renderRTECountChartData:function(t){$("#rte-graph-header-text").text("RTE (Graphical View)"),$("#chartjs-rte-student-pie").attr("style","display:block;"),$("#percentage-rte-students").attr("style","display:block;"),$("#tabularview-rte-student-distribution").attr("style","display:none;");var e=t.classWiseRteCountMap,a=t.classWiseStudentCountMap,n=[];for(let[t,e]of a.entries())n.push(e);var s=[];for(let[t,a]of e.entries())s.push(a);for(var i=[],o=0;o<n.length;o++)i.push(n[o]-s[o]);var r=0,d=0;for(o=0;o<s.length;o++)r+=s[o];for(o=0;o<i.length;o++)d+=i[o];var l="";if(r+d==0)l='<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>0%</span><span class="text-muted"> of total students</span>';else{var u=getPercentage(r,r+d);l='<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(u)+'%</span><span class="text-muted"> of total students</span>'}$("#percentage-rte-students").html(l);var c=[r,d];statistics.renderRTEStudentsPieChart(["RTE","Non RTE"],c)},renderGenderCountChartData:function(t){$("#genderwise-graph-header-text").text("Genderwise Student Distribution (Graphical View)"),$("#chartjs-genderwise-student-bar-distribution").attr("style","display:block;"),$("#tabularview-genderwise-student-distribution").attr("style","display:none;");var e=t.classWiseNewAdmissionGenderCountMap,a=t.classWiseOldStudentGenderCountMap,n=[],s=[],i=[],o=[],r=[];for(let[t,a]of e.entries())n.push(t),$.each(a,function(t,e){"MALE"===t?s.push(e):"FEMALE"===t&&i.push(e)});for(let[t,e]of a.entries())$.each(e,function(t,e){"MALE"===t?o.push(e):"FEMALE"===t&&r.push(e)});statistics.renderStudentGenderCountChart(n,s,i,o,r)},renderTopBoxesData:function(t){var e=t.totalStudents,a=t.oldStudents,n=t.newRegistrationStudents,s=t.newAdmissionStudents;$("#total-student-count").text(e),console.log("newRegistrationStudents = "+n),$("#total-new-registration-count").text(n),$("#total-new-admissions-count").text(s),$("#total-old-students-count").text(a);var i=t.totalLastYearStudents,o=(t.oldLastYearStudents,t.newRegistrationLastYearStudents),r=t.newAdmissionLastYearStudents;if(0==i)$("#total-student-count-badge-value").html('<span class="badge badge-soft-secondary"><i class="mdi mdi-arrow-bottom-right"></i>-</span><span class="text-muted"> from last year</span>');else{var d=getPercentageChange(i,e);d>=0?$("#total-student-count-badge-value").html('<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(d)+'%<i class="bi bi-arrow-up"></i></span><span class="text-muted"> from last year</span>'):$("#total-student-count-badge-value").html('<span class="badge badge-soft-danger"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(d)+'%<i class="bi bi-arrow-down"></i></span><span class="text-muted"> from last year</span>')}if(console.log("newRegistrationLastYearStudents : "+o),0==o)$("#total-new-registration-count-badge-value").html('<span class="badge badge-soft-secondary"><i class="mdi mdi-arrow-bottom-right"></i>-</span><span class="text-muted"> from last year</span>');else{var l=getPercentageChange(o,n);l>=0?$("#total-new-registration-count-badge-value").html('<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(l)+'%<i class="bi bi-arrow-up"></i></span><span class="text-muted"> from last year</span>'):$("#total-new-registration-count-badge-value").html('<span class="badge badge-soft-danger"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(l)+'%<i class="bi bi-arrow-down"></i></span><span class="text-muted"> from last year</span>')}if(0==r)$("#total-new-admissions-count-badge-value").html('<span class="badge badge-soft-secondary"><i class="mdi mdi-arrow-bottom-right"></i>-</span><span class="text-muted"> from last year</span>');else{var u=getPercentageChange(r,s);u>=0?$("#total-new-admissions-count-badge-value").html('<span class="badge badge-soft-success"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(u)+'%<i class="bi bi-arrow-up"></i></span><span class="text-muted"> from last year</span>'):$("#total-new-admissions-count-badge-value").html('<span class="badge badge-soft-danger"><i class="mdi mdi-arrow-bottom-right"></i>'+Math.abs(u)+'%<i class="bi bi-arrow-down"></i></span><span class="text-muted"> from last year</span>')}},renderNewAdmissionStudentChart:function(t,e){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20};var a=new Chart($("#chartjs-new-admission-student-bar-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"New Admission",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}});a.canvas.parentNode.style.height="311px",a.canvas.parentNode.style.width="311px"},renderStudentCategoryCountChart:function(t,e,a){new Chart($("#chartjs-categorywise-student-bar-distribution"),{type:"horizontalBar",data:{labels:t,datasets:[{label:"Category Student",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:e,barPercentage:.325,categoryPercentage:.5},{label:"Other Students",backgroundColor:LIGHT_GREY_COLOR,borderColor:LIGHT_GREY_COLOR,hoverBackgroundColor:LIGHT_GREY_COLOR,hoverBorderColor:LIGHT_GREY_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,legend:{display:!1},tooltips:{enabled:!0,mode:"single",displayColors:!1,callbacks:{label:function(t,e){return" : "+e.datasets[0].data[t.index]}}},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStudentGenderCountChart:function(t,e,a,n,s){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20},new Chart($("#chartjs-genderwise-student-bar-distribution"),{type:"bar",data:{labels:t,datasets:[{label:"Old Student Male",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Old Student Female",backgroundColor:OLD_STUDENTS_FEMALE_COLOR,borderColor:OLD_STUDENTS_FEMALE_COLOR,hoverBackgroundColor:OLD_STUDENTS_FEMALE_COLOR,hoverBorderColor:OLD_STUDENTS_FEMALE_COLOR,data:s,barPercentage:.325,categoryPercentage:.5},{label:"New Admission Male",backgroundColor:NEW_ADMISSION_MALE_COLOR,borderColor:NEW_ADMISSION_MALE_COLOR,hoverBackgroundColor:NEW_ADMISSION_MALE_COLOR,hoverBorderColor:NEW_ADMISSION_MALE_COLOR,data:e,barPercentage:.325,categoryPercentage:.5},{label:"New Admission Female",backgroundColor:NEW_ADMISSION_FEMALE_COLOR,borderColor:NEW_ADMISSION_FEMALE_COLOR,hoverBackgroundColor:NEW_ADMISSION_FEMALE_COLOR,hoverBorderColor:NEW_ADMISSION_FEMALE_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!0,cornerRadius:15,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderRTEStudentsPieChart:function(t,e){Chart.Legend.prototype.afterFit=function(){this.height=this.height+40};var a=new Chart($("#chartjs-rte-student-pie"),{type:"pie",data:{labels:t,datasets:[{data:e,backgroundColor:[LOGO_COLOR,LIGHT_GREY_COLOR],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:75,legend:{display:!0,position:"top",align:"center",labels:{boxWidth:12,padding:10}}}});a.canvas.parentNode.style.height="250px",a.canvas.parentNode.style.width="311px"}};function previousTabSwtichingEvent(){$(".previous-switch-tab").on("click",function(){var t=$(".switch-tab.active").prev();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")})}function nextTabSwtichingEvent(){$(".next-switch-tab").on("click",function(){switchToNextTab()})}function switchToNextTab(){var t=$(".switch-tab.active").next();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")}function swtichingTostudentUpdateFormFirstTab(){var t=$(".switch-tab").first();$(".switch-tab").removeClass("active"),$(".switch-tab").removeClass("disabled"),t.trigger("click"),t.removeClass("disabled").addClass("active")}function closeModal(){clearMandatoryFieldsErrorDisplay()}function returnToMainScreen(){"ADMIT-STUDENT"==$("#screen-name").text().trim()?loadAdmitStudentMenu():(searchStudents(!0),$(".student-details-screen").attr("style","display:none"),$("#student-details").attr("style","display:block"),swtichingTostudentUpdateFormFirstTab())}function fillStudentSections(t){for(var e=$("p.student-standard-json").first().text().trim(),a=JSON.parse(e),n={},s=0;s<a.length;s++){n[(o=a[s]).standardId]=o}if(t in n){var i='<option value=""></option>',o=n[t];for(s=0;s<o.standardSectionList.length;s++)i+='<option value="'+o.standardSectionList[s].sectionId+'">'+o.standardSectionList[s].sectionName+"</option>";$(".student-section").html(i)}}function fillStudentAcademicSession(t){var e='<option value="'+t.academicSessionId+'">'+t.displayName+"</option>";$("#update-student-academic-session").html(e)}function fillStudentInformation(t){fillStudentAcademicSession(t.studentAcademicSessionInfoResponse.academicSession),$("#update-admission-student-id").text(t.studentId),$("#update\\.student-registration-number").val(t.studentBasicInfo.registrationNumber),$("#update\\.student-admission-number").val(t.studentBasicInfo.admissionNumber),$("#update\\.pen-number").val(t.studentBasicInfo.penNumber),$("#update\\.apaar-id-number").val(t.studentBasicInfo.apaarIdNo),$("#update-student-class").val(t.studentAcademicSessionInfoResponse.standard.standardId),fillStudentSections(t.studentAcademicSessionInfoResponse.standard.standardId),null!=t.studentAcademicSessionInfoResponse.standard.standardSectionList&&t.studentAcademicSessionInfoResponse.standard.standardSectionList.length>0&&$("#update-student-section").val(t.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionId),$("#update\\.student-name").val(t.studentBasicInfo.name),$("#update\\.student-gender").val(t.studentBasicInfo.gender),setFormattedDate(t.studentBasicInfo.dateOfBirth,"#update\\.student-date-of-birth"),$("#update\\.student-birth-place").val(t.studentBasicInfo.birthPlace),$("#update\\.student-category").val(t.studentBasicInfo.userCategory),$("#update\\.student-religion").val(t.studentBasicInfo.religion),$("#update\\.student-caste").val(t.studentBasicInfo.caste),$("#update\\.student-rte").prop("checked",t.studentBasicInfo.rte),$("#update\\.student-mother-tongue").val(t.studentBasicInfo.motherTongue),$("#update\\.student-area-type").val(t.studentBasicInfo.areaType),$("#update\\.student-specially-abled").prop("checked",t.studentBasicInfo.speciallyAbled),$("#update\\.student-bpl").prop("checked",t.studentBasicInfo.bpl),$("#update\\.student-present-address").val(t.studentBasicInfo.presentAddress),$("#update\\.student-present-city").val(t.studentBasicInfo.presentCity),$("#update\\.student-present-state").find(".country-states").val(t.studentBasicInfo.presentState),$("#update\\.student-present-post-office").val(t.studentBasicInfo.presentPostOffice),$("#update\\.student-present-police-station").val(t.studentBasicInfo.presentPoliceStation),$("#update\\.student-present-zipcode").val(t.studentBasicInfo.presentZipcode),$("#update\\.student-permanent-address").val(t.studentBasicInfo.permanentAddress),$("#update\\.student-city").val(t.studentBasicInfo.permanentCity),$("#update\\.student-state").find(".country-states").val(t.studentBasicInfo.permanentState),$("#update\\.student-post-office").val(t.studentBasicInfo.permanentPostOffice),$("#update\\.student-police-station").val(t.studentBasicInfo.permanentPoliceStation),$("#update\\.student-zipcode").val(t.studentBasicInfo.permanentZipcode),$("#update\\.student-aadhar-number").val(t.studentBasicInfo.aadharNumber),setFormattedDate(t.studentBasicInfo.registrationDate,"#update\\.student-registration-date"),setFormattedDate(t.studentBasicInfo.admissionDate,"#update\\.student-admission-date"),$("#update\\.student-primary-contact-number").val(t.studentBasicInfo.primaryContactNumber),$("#update\\.student-primary-email").val(t.studentBasicInfo.primaryEmail),$("#update\\.student-whatsapp-number").val(t.studentBasicInfo.whatsappNumber),$("#update\\.student-is-sponsored").prop("checked",t.studentBasicInfo.sponsored),$("#update\\.student-new-admission").prop("checked",t.newAdmission),$("#update\\.student-hosteller").prop("checked",t.studentBasicInfo.hosteller),$("#update\\.student-nationality").val(t.studentBasicInfo.nationality),$("#update\\.student-house").val(null==t.studentBasicInfo.instituteHouse?"":t.studentBasicInfo.instituteHouse.houseId),$("#update\\.student-admission-in-class").val(t.studentBasicInfo.admissionInClass),$("#update\\.student-name-as-per-aadhar").val(t.studentBasicInfo.studentNameAsPerAadhar);""!==t.studentBasicInfo.childCategoryCriteria&&null!==t.studentBasicInfo.childCategoryCriteria&&void 0!==t.studentBasicInfo.childCategoryCriteria&&t.studentBasicInfo.childCategoryCriteria,$("#update\\.child-category-criteria").val(),$("#update\\.specially-abled-type").val(t.studentBasicInfo.speciallyAbledType),$("#student-mother-name").val(t.studentFamilyInfo.mothersName),$("#student-father-name").val(t.studentFamilyInfo.fathersName),$("#student-mother-qualification").val(t.studentFamilyInfo.mothersQualification),$("#student-father-qualification").val(t.studentFamilyInfo.fathersQualification),$("#mother-contact-number").val(t.studentFamilyInfo.mothersContactNumber),$("#father-contact-number").val(t.studentFamilyInfo.fathersContactNumber),$("#mother-occupation").val(t.studentFamilyInfo.mothersOccupation),$("#mother-annual-income").val(t.studentFamilyInfo.mothersAnnualIncome),$("#father-occupation").val(t.studentFamilyInfo.fathersOccupation),$("#father-annual-income").val(t.studentFamilyInfo.fathersAnnualIncome),$("#mother-aadhar-number").val(t.studentFamilyInfo.mothersAadharNumber),$("#father-aadhar-number").val(t.studentFamilyInfo.fathersAadharNumber),$("#mother-pan-card-details").val(t.studentFamilyInfo.mothersPanCardDetails),$("#father-pan-card-details").val(t.studentFamilyInfo.fathersPanCardDetails),$("#approx-family-income").val(t.studentFamilyInfo.approxFamilyIncome),null!=t.studentGuardianInfoList&&t.studentGuardianInfoList.length>0&&($("#guardian-name").val(t.studentGuardianInfoList[0].guardianName),$("#guardian-relation").val(t.studentGuardianInfoList[0].relation),$("#guardian-age").val(t.studentGuardianInfoList[0].age),$("#guardian-gender").val(t.studentGuardianInfoList[0].gender),$("#guardian-occupation").val(t.studentGuardianInfoList[0].occupation),$("#guardian-email").val(t.studentGuardianInfoList[0].email),$("#guardian-contact-number").val(t.studentGuardianInfoList[0].contactNumber),$("#guardian-address").val(t.studentGuardianInfoList[0].address),$("#guardian-city").val(t.studentGuardianInfoList[0].city),$("#guardian-state").find(".country-states").val(t.studentGuardianInfoList[0].state),$("#guardian-zipcode").val(t.studentGuardianInfoList[0].zipcode)),$("#previous-school-name").val(t.studentPreviousSchoolInfo.schoolName),$("#previous-school-medium").val(t.studentPreviousSchoolInfo.medium),$("#class-passed").val(t.studentPreviousSchoolInfo.classPassed),$("#year-of-passing").val(t.studentPreviousSchoolInfo.yearOfPassing),$("#result").val(t.studentPreviousSchoolInfo.result),$("#percentage").val(t.studentPreviousSchoolInfo.percentage),$("#student-is-admission-tc-based").prop("checked",t.studentPreviousSchoolInfo.admissionTcBased),$("#previous-school-tc-number").val(t.studentPreviousSchoolInfo.tcNumber),$("#blood-group").val(t.studentMedicalInfo.bloodGroup),$("#blood-pressure").val(t.studentMedicalInfo.bloodPressure),$("#pulse").val(t.studentMedicalInfo.pulse),$("#height").val(t.studentMedicalInfo.height),$("#weight").val(t.studentMedicalInfo.weight),setFormattedDate(t.studentMedicalInfo.dateOfPhysicalExamination,"#date-of-physical-examination");var e={};if(null!=t.studentDocuments&&t.studentDocuments.length>0)for(var a=0;a<t.studentDocuments.length;a++){var n=t.studentDocuments[a];e[n.documentType]=n}fillStudentDocumentsInformation(e)}function fillStudentDocumentsInformation(t){for(var e=$("#student-document-information-table"),a="",n=0;n<STUDENT_DOCUMENT_TYPE.length;n++){var s=STUDENT_DOCUMENT_TYPE[n],i=t[s];a+='<tr class="student-document-row" id='+s+"><td>"+s+"</td>";var o="No",r="",d="";null!=i&&null!=i&&(!0,r="checked",d=i.documentNotUploaded?"":"disabled",o=i.documentNotUploaded?"No":"Yes"),a+='<td><div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input student-document-collected-checkbox" id="student-document-checkbox-'+n+'"'+r+" "+d+'><label class="custom-control-label" for="student-document-checkbox-'+n+'"></label></div></td><td>'+o+"</td>",a+="</tr>"}$(e).html(a)}function studentDocumentWarningPopup(){$("#student-document-warning-popup").modal("toggle")}var admissionReports={dataCache:{},loadMainScreen:function(){ajaxClient.get("/admission/reports",function(t){$("#main-content").html(t),initSelect2(),initDateInput(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),reportUtils.bindSelectClassCheckboxEvent(),admissionReports.bindGenerateReportEvent(),admissionReports.loadOnChangeEventsFilter(),admissionReports.checkboxEvents(),admissionReports.dataCache.filterationCriteria=new Object})},loadOnChangeEventsFilter:function(){$("select.filter-gender").on("change",function(){admissionReports.dataCache.filterationCriteria.gender=$(this).val()}),$("select.filter-religion").on("change",function(){admissionReports.dataCache.filterationCriteria.religion=$(this).val()}),$("select.filter-category").on("change",function(){admissionReports.dataCache.filterationCriteria.category=$(this).val()}),$("select.filter-area-type").on("change",function(){admissionReports.dataCache.filterationCriteria.areaType=$(this).val()}),$("select.filter-specially-abled").on("change",function(){admissionReports.dataCache.filterationCriteria.speciallyAbled=$(this).val()}),$("select.filter-rte").on("change",function(){admissionReports.dataCache.filterationCriteria.rte=$(this).val()}),$("select.filter-bpl").on("change",function(){admissionReports.dataCache.filterationCriteria.bpl=$(this).val()}),$("select.filter-states").on("change",function(){admissionReports.dataCache.filterationCriteria.states=$(this).val()}),$("select.reports-student-class").on("change",function(){admissionReports.dataCache.filterationCriteria.standards=$(this).val()}),$("select.filter-house").on("change",function(){admissionReports.dataCache.filterationCriteria.instituteHouse=$(this).val()})},bindGenerateReportEvent:function(){$(".report-academic-session").on("change",function(){var t=this,e=$(t).val();ajaxClient.get("/admission/report-session-change/"+e,function(e){$(t).parent().parent().parent().find(".class-attendance-type-div").html(e),initSelect2(),admissionReports.loadOnChangeEventsFilter()})}),$(".generate-report").on("click",function(){var t="";null!=admissionReports.dataCache.filterationCriteria.gender&&null!=admissionReports.dataCache.filterationCriteria.gender&&(t=admissionReports.dataCache.filterationCriteria.gender.join(","));var e="";null!=admissionReports.dataCache.filterationCriteria.religion&&null!=admissionReports.dataCache.filterationCriteria.religion&&(e=admissionReports.dataCache.filterationCriteria.religion.join(","));var a="";null!=admissionReports.dataCache.filterationCriteria.category&&null!=admissionReports.dataCache.filterationCriteria.category&&(a=admissionReports.dataCache.filterationCriteria.category.join(","));var n="";null!=admissionReports.dataCache.filterationCriteria.areaType&&null!=admissionReports.dataCache.filterationCriteria.areaType&&(n=admissionReports.dataCache.filterationCriteria.areaType.join(","));var s="";null!=admissionReports.dataCache.filterationCriteria.speciallyAbled&&null!=admissionReports.dataCache.filterationCriteria.speciallyAbled&&(s=admissionReports.dataCache.filterationCriteria.speciallyAbled);var i="";null!=admissionReports.dataCache.filterationCriteria.rte&&null!=admissionReports.dataCache.filterationCriteria.rte&&(i=admissionReports.dataCache.filterationCriteria.rte);var o="";null!=admissionReports.dataCache.filterationCriteria.bpl&&null!=admissionReports.dataCache.filterationCriteria.bpl&&(o=admissionReports.dataCache.filterationCriteria.bpl);var r="";null!=admissionReports.dataCache.filterationCriteria.states&&null!=admissionReports.dataCache.filterationCriteria.states&&(r=admissionReports.dataCache.filterationCriteria.states.join(","));var d="";null!=admissionReports.dataCache.filterationCriteria.instituteHouse&&null!=admissionReports.dataCache.filterationCriteria.instituteHouse&&(d=admissionReports.dataCache.filterationCriteria.instituteHouse.join(",")),$(this).closest("div.modal").modal("toggle");var l=$(this).closest("div.report-field-container"),u=$(l).find("p.report-type").text().trim(),c=reportUtils.getReportHeadersCSV(l);admissionReports.dataCache.filterationCriteria=new Object;var p=$(l).find(".download-format").val();p=void 0===p?"":p.trim();var m=!1;if($(l).find("input.new-admission").is(":checked")&&(m=!0),"PDF"==p&&c.split(",").length>20)showErrorDialogBox("Please select less than 20 columns for the Student Details PDF report and try again.");else{var f=0;0!=$(l).find(".report-academic-session option:selected").length&&(f=$(l).find(".report-academic-session option:selected").val());var g="";$(l).find(".reports-student-class").length>0&&(g=$(l).find(".reports-student-class").val().join());var h="";$(l).find(".student-status").length>0&&(h=$(l).find(".student-status").val().join());var v="";$(l).find(".final-student-status").length>0&&(v=$(l).find(".final-student-status").val().join());var b="";$(l).find(".document-types").length>0&&(b=$(l).find(".document-types").val().join());var S="";0!=$(l).find(".document-status option:selected").length&&(S=$(l).find(".document-status option:selected").val()),window.open(baseURL+"/admission/generate-report/"+f+"?requiredStandards="+g+"&studentStatus="+h+"&finalStudentStatus="+v+"&documentTypes="+b+"&documentStatus="+S+"&reportType="+u+"&requiredHeaders="+c+"&rte="+i+"&category="+a+"&gender="+t+"&religion="+e+"&area_type="+n+"&specially_abled="+s+"&bpl="+o+"&state="+r+"&instituteHouseId="+d+"&newAdmission="+m,"_blank")}})},resetPopup:function(){admissionReports.resetCheckBoxes()},resetCheckBoxes:function(){$(".parent").prop("checked",!1),$(".child").prop("checked",!1),$(".basic").prop("checked",!0),$(".basic-report-column").prop("checked",!0)},checkboxEvents:function(){$(".child").on("change",function(){$(this).parent().parent().parent().find($(".parent")).prop("checked",!1)}),$(".parent").on("change",function(){$(this).prop("checked")?$(this).parent().parent().find($(".child")).prop("checked",!0):$(this).prop("checked")||$(this).parent().parent().find($(".child")).each(function(){$(this).is(":disabled")||$(this).prop("checked",!1)})})}},transferCertificate={removeTCStudentCourses:function(t){$(t).parent().remove()},addTCStudentCourses:function(){$("#tc-student-courses-div").append('<div class="input-group mb-3 col-md-4"><input type="text" class="form-control tc-student-courses" placeholder="Subject..."><div class="input-group-prepend" style="cursor:pointer;" onclick="transferCertificate.removeTCStudentCourses(this);"><span class="input-group-text" aria-hidden="true">x</span></div></div>')},getTCSubject:function(){var t=[];return $(".tc-student-courses").each(function(){t.push($(this).val())}),t.join(",")},isValidData:function(t){return null!=t&&null!=t&&""!=t},getTcStudentSessionDetails:function(){var t=[];return $(".tc-previous-session-detail-summary-list").each(function(){var e=$(this).find("#tc-previous-admission-class").val(),a="",n=getDate($(this).find("#tc-previous-admission-date").val());transferCertificate.isValidData(n)&&(a=n.getTime()/1e3);var s=$(this).find("#tc-admission-promotion-class").val(),i="",o=getDate($(this).find("#tc-admission-promotion-date").val());transferCertificate.isValidData(o)&&(i=o.getTime()/1e3);var r="",d=getDate($(this).find("#tc-date-of-passing-from-school").val());transferCertificate.isValidData(d)&&(r=d.getTime()/1e3);var l=$(this).find("#tc-no-of-meeting").val(),u=$(this).find("#tc-no-of-present").val(),c=$(this).find("#tc-rank-in-class").val(),p=$(this).find("#tc-result-division-final-exam").val(),m=$(this).find("#tc-medium").val(),f=$(this).find("#tc-subjects-taken").val(),g=$(this).find("#tc-conduct-and-work-in-session").val(),h=$(this).find("#tc-duration-in-session").val();t.push({previousAdmissionClass:e,previousAdmissionDate:a,admissionPromotionClass:s,admissionPromotionDate:i,dateOfPassingFromSchool:r,noOfMeeting:l,noOfPresent:u,rankInClass:c,resultDivisionFinalExam:p,medium:m,subjectsTaken:f,conductAndWorkInSession:g,duration:h})}),t},generateStaticTC:function(t){window.open(baseURL+"/admission/generate-pdf-tc/"+t,"_blank"),returnToMainScreen()},addTCStudentPrevSessionDetails:function(){$("#tc-previous-session-detail-summary-list-div").append('<div class="col-md-12 mb-3 tc-previous-session-detail-summary-list"><div class="row"><div class="mb-3 col-md-2"><label>Previous Admission Class</label><input type="text" class="form-control" placeholder="Previous AdmissionClass..." id="tc-previous-admission-class"></div><div class="mb-3 col-md-2"><label>Previous Admission Date</label><input type="text" class="form-control select-date" placeholder="Ad./Promo. Date..." id="tc-previous-admission-date"></div><div class="mb-3 col-md-2"><label>Promotion Class</label><input type="text" class="form-control" placeholder="Promotion Class..." id="tc-admission-promotion-class"></div><div class="mb-3 col-md-3"><label>Promotion Date</label><input type="text" class="form-control select-date" placeholder="Promotion Date..." id="tc-admission-promotion-date"></div><div class="mb-3 col-md-3"><label>Date of Passing from School</label><input type="text" class="form-control select-date" placeholder="Date of Passing from School..." id="tc-date-of-passing-from-school"></div></div><div class="row"><div class="mb-3 col-md-2"><label>No. of Meeting</label><input type="text" class="form-control" placeholder="No. of Meeting..." id="tc-no-of-meeting"></div><div class="mb-3 col-md-2"><label>No of Present</label><input type="text" class="form-control" placeholder="No. of Present..." id="tc-no-of-present"></div><div class="mb-3 col-md-3"><label>Result and Division in Final Exam</label><input type="text" class="form-control" placeholder="Result and Division in Final Exam..." id="tc-result-division-final-exam"></div><div class="mb-3 col-md-2"><label>Subjects taken</label><input type="text" class="form-control" placeholder="Subjects taken..." id="tc-subjects-taken"></div><div class="mb-3 col-md-2"><label>Rank in Class</label><input type="text" class="form-control" placeholder="Rank in Class..." id="tc-rank-in-class"></div></div><div class="row"><div class="mb-3 col-md-2"><label>Medium</label><input type="text" class="form-control" placeholder="Medium..." id="tc-medium"></div><div class="mb-3 col-md-4"><label>Duration: Admission date to the end of this session</label><input type="text" class="form-control" placeholder="Admission date to the end of this session..." id="tc-duration-in-session"></div><div class="mb-3 col-md-3"><label>Conduct and Work in session</label><input type="text" class="form-control" placeholder="Conduct and Work in Session..." id="tc-conduct-and-work-in-session"></div></div><div class="col-md-12" style="cursor:pointer;" onclick="transferCertificate.removeTCStudentCourses(this);"><p style="color: #FF0000; text-align: right;"><u>Delete Session Detail</u></p></div></div>'),initDateWithYearRange("-20:+5",!1)},generateTC:function(t){var e=$("#tc-book-no").val().trim(),a=$("#tc-serial-no").val().trim(),n=$("#tc-generation-is-tc-number-fixed").text().trim(),s=0,i=getDate($("#tc-generation-date").val());null!=i&&null!=i&&""!=i&&(s=i.getTime()/1e3);var o=$("#tc-affiliation-no").val().trim(),r=$("#tc-dise-code").val().trim(),d=$("#tc-school-code").val().trim(),l=$("#tc-pri-edu-affli-no").val().trim(),u=$("#tc-apaar-id-no").val().trim(),c=$("#tc-rte-affli-no").val().trim(),p=$("#tc-admission-no").val().trim(),m=$("#tc-student-name").val().trim(),f=0,g=getDate($("#tc-admission-date").val());null!=g&&null!=g&&""!=g&&(f=g.getTime()/1e3);var h=$("#tc-admission-class").val().trim(),v=$("#tc-father-name").val().trim(),b=$("#tc-mother-name").val().trim(),S=$("#tc-proof-of-dob-at-the-time-of-admission").val().trim(),y=$("#tc-living-time-in-currect-city").val().trim(),C=0,w=getDate($("#tc-dob").val());null!=w&&null!=w&&""!=w&&(C=w.getTime()/1e3);var I=$("#tc-category").val().trim(),D=$("#tc-nationality").val().trim(),E=$("#tc-last-session-class").val().trim(),x=$("#tc-last-session-class-name-in-words").val().trim(),N=$("#tc-last-session-class-name-in-figures").val().trim(),T=$("#tc-last-exam").val().trim(),k=$("#tc-last-exam-failed").val().trim(),R=transferCertificate.getTCSubject(),_=$("#tc-getting-promotion").val().trim(),P=$("#tc-promoting-class").val().trim(),A=$("#tc-promoting-class-name-in-words").val().trim(),L=$("#tc-promoting-class-name-in-figures").val().trim(),O=$("#tc-last-fees-paid").val().trim(),B=$("#tc-discount").val().trim(),F=$("#tc-total-working-days").val().trim(),M=$("#tc-total-attended-days").val().trim(),q=$("#tc-ncc-cadet").val().trim(),j=$("#tc-cocurricular-activities").val().trim(),H=$("#tc-code-of-conduct").val(),G=0,U=getDate($("#tc-relieve-date").val());if(null!=U&&null!=U&&""!=U&&(G=U.getTime()/1e3)>s)showErrorDialogBox("Relieved Date cannot be greater than TC Generation Date.");else{var W=$("#tc-relieve-reason").val().trim(),Y=$("#tc-remarks").val().trim(),J=0,V=getDate($("#tc-date-of-application-of-certificate").val());null!=V&&null!=V&&""!=V&&(J=V.getTime()/1e3);var z=0,Z=getDate($("#tc-date-pupils-name-stuck-off-the-rolls").val());null!=Z&&null!=Z&&""!=Z&&(z=Z.getTime()/1e3);var Q="?book_number="+e+"&tc_number="+a+"&is_tc_number_fixed="+n+"&tc_generation_date="+s+"&affiliation_number="+o+"&dise_code="+r+"&school_code="+d+"&primary_education_affiliation_number="+l+"&apaar_id_number="+u+"&rte_affiliation_number="+c+"&admission_number="+p+"&student_name="+m+"&admission_date="+f+"&admission_class="+h+"&father_guardian_name="+v+"&mother_name="+b+"&dob="+C+"&category="+I+"&nationality="+D+"&last_active_session_class="+E+"&last_exam_taken_with_result="+T+"&number_of_time_exam_failed="+k+"&scholastic_courses_last_active_session_str="+R+"&promotion_to_higher_class="+_+"&promoting_class_name="+P+"&last_fees_paid="+O+"&discount_with_nature="+B+"&total_working_days="+F+"&total_attended_days="+M+"&ncc_cadet_boy_scout_girl_guide_with_details="+q+"&co_curricular_activities="+j+"&code_of_conduct="+H+"&relieve_date="+G+"&relieve_reason="+W+"&remarks="+Y+"&proof_of_dob_at_the_time_of_admission="+S+"&last_active_session_class_in_figures="+N+"&last_active_session_class_in_words="+x+"&promoting_class_name_in_figures="+L+"&promoting_class_name_in_words="+A+"&date_of_application_of_certificate="+J+"&date_pupils_name_stuck_off_the_rolls="+z+"&prev_session_details_list="+JSON.stringify(transferCertificate.getTcStudentSessionDetails())+"&living_time_in_currect_city="+y;console.log(Q),window.open(baseURL+"/admission/pdf-tc/"+t+Q,"_blank"),returnToMainScreen()}},clearTCVariables:function(){var t=$("#tc-counter-enable").text();$("#tc-affiliation-no").val(""),"false"===t&&$("#tc-serial-no").val(""),$("#tc-book-no").val(""),$("#tc-generation-date").val(""),$("#tc-affiliation-no").val(""),$("#tc-dise-code").val(""),$("#tc-school-code").val(""),$("#tc-pri-edu-affli-no").val(""),$("#tc-apaar-id-no").val(""),$("#tc-living-time-in-currect-city").val(""),$("#tc-rte-affli-no").val(""),$("#tc-admission-no").val(""),$("#tc-student-name").val(""),$("#tc-admission-date").val(""),$("#tc-admission-class").val(""),$("#tc-father-name").val(""),$("#tc-mother-name").val(""),$("#tc-proof-of-dob-at-the-time-of-admission").val(""),$("#tc-dob").val(""),$("#tc-category").val(""),$("#tc-nationality").val(""),$("#tc-last-session-class").val(""),$("#tc-last-exam").val(""),$("#tc-last-exam-failed").val(""),$(".tc-student-courses").val(""),$("#tc-getting-promotion").val(""),$("#tc-promoting-class").val(""),$("#tc-last-fees-paid").val(""),$("#tc-discount").val(""),$("#tc-total-working-days").val(""),$("#tc-total-attended-days").val(""),$("#tc-ncc-cadet").val(""),$("#tc-cocurricular-activities").val(""),$("#tc-code-of-conduct").val(""),$("#tc-relieve-date").val(""),$("#tc-relieve-reason").val(""),$("#tc-remarks").val("")},resetTCVariables:function(t){ajaxClient.get("/admission/student-tc-field-details/"+t,function(e){$("#tc-outer-div").html(e),$("#tc-generation-student-id").text(t),initDateWithYearRange("-20:+5",!1),initPastDateById("tc-generation-date",2e3),initPastDateById("tc-relieve-date",2e3)})}},certificate={loadStudentPaymentDetails:function(t){var e=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/admission/generate-study-certificate/"+e+"/"+t,"_blank")}};function advanceSearch(){$("#advance-search-option-div").toggle()}function getPercentageChange(t,e){return parseFloat((100*(e-t)/t).toFixed(1))}function getPercentage(t,e){return parseFloat((t/e*100).toFixed(1))}function isObjectEmpty(t){return 0===Object.keys(t).length&&t.constructor===Object}var admissionEnquiry={dataCache:{},loadMainScreen:function(t){ajaxClient.get("/admission/enquiry-home",function(t){$("#main-content").html(t),admissionEnquiry.viewEnquiryDetails(!0,"SUBMITTED"),enquiryPagination.dataCache.status="SUBMITTED",academicSessionHandler.bindSessionChangeEvent(admissionEnquiry.loadEnquiryDetailsForSession),admissionEnquiry.bindEnquiryClickEvents(),admissionEnquiry.dataCache={},initDate(1825)})},loadEnquiryDetailsForSession:function(){admissionEnquiry.viewEnquiryDetails(!0,"SUBMITTED")},backButton:function(t,e,a){$(".student-details-screen").css("display","block");var n=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/admission/enquiry-details/session/"+n+"/"+t+"/"+e+"/0",function(e){$("#enquiryDetailsResult").html(e);var a=$("#sidebar").height()-$("#enquiryDetailsResult").position().top-300;$("#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper").attr("style","height:"+a+"px;  overflow-y: scroll; cursor: pointer;"),admissionEnquiry.highlightActiveTab(t),admissionEnquiry.bindEnquiryClickEvents(),admissionEnquiry.dataCache={},enquiryPagination.initPagination(),console.log("."+t.toLowerCase()+"-datatables-reponsive"),$("."+t.toLowerCase()+"-datatables-reponsive").DataTable({searching:!0,columnDefs:[{orderable:!1,targets:"no-sort"}]})})},viewEnquiryDetails:function(t,e){enquiryPagination.dataCache.status=e;var a=getDate($("#latest-followup-date").val());a=null!=a?a.getTime()/1e3:0,"ACCEPTED"!=e&&(a=0);var n=$(".page-item.active").find(".page-number").text().trim();t&&(n=1);$("#items-per-page").val();n||(n=1);var s=0*(n-1),i=academicSessionHandler.getSelectedSessionId();$("#enquiryDetailsResult").html(""),ajaxClient.get("/admission/enquiry-details/session/"+i+"/"+e+"/"+s+"/0?filter_date="+a,function(t){$("#enquiryDetailsResult").html(t);var n=$("#sidebar").height()-$("#enquiryDetailsResult").position().top-300;$("#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper").attr("style","height:"+n+"px;  overflow-y: scroll; cursor: pointer;"),admissionEnquiry.highlightActiveTab(e),admissionEnquiry.bindEnquiryClickEvents(),admissionEnquiry.dataCache={},enquiryPagination.initPagination(),initDateWithYearRange("-2:+2",!1),0!=a&&$("#latest-followup-date").val(getFormattedDate(a)),console.log("."+e.toLowerCase()+"-datatables-reponsive"),$("."+e.toLowerCase()+"-datatables-reponsive").DataTable({searching:!0,columnDefs:[{orderable:!1,targets:"no-sort"}]})})},highlightActiveTab:function(t){switch($("#nav-submitted-enquiry-tab, #nav-accepted-enquiry-tab, #nav-rejected-enquiry-tab, #nav-closed-enquiry-tab").removeClass("active"),$("#submitted-enquiry-content, #accepted-enquiry-content, #rejected-enquiry-content, #closed-enquiry-content").removeClass("active"),t){case"SUBMITTED":$("#nav-submitted-enquiry-tab").addClass("active"),$("#submitted-enquiry-content").addClass("active");break;case"ACCEPTED":$("#nav-accepted-enquiry-tab").addClass("active"),$("#accepted-enquiry-content").addClass("active");break;case"REJECTED":$("#nav-rejected-enquiry-tab").addClass("active"),$("#rejected-enquiry-content").addClass("active");break;case"CLOSED":$("#nav-closed-enquiry-tab").addClass("active"),$("#closed-enquiry-content").addClass("active")}},bindEnquiryClickEvents:function(){$(".view-enquiry-details").on("click",function(){var t=$(this).attr("id").trim();admissionEnquiry.showEnquiryDetails(t)})},showEnquiryDetails:function(t){var e=$("#"+t).closest("tr").find(".enquiry-details-info").text().trim(),a=JSON.parse(e);admissionEnquiry.populateEnquiryDetails(a),$("#view-enquiry-details-modal").modal("toggle")},populateEnquiryDetails:function(t){admissionEnquiry.resetFields(),$("#tracking-id").text(t.trackingId),$("#parent-name").text(t.guardianName),$("#child-name").text(t.childName),$("#standard").text(t.standardName),$("#phone-number").text(t.guardianContactInfo),$("#email-id").text(t.guardianEmailId),$("#message").text(t.message),$("#successful").text(t.successful),null!=t.studentDetailedRow&&($("#student").text(t.studentDetailedRow.studentBasicInfo.name),$("#student-adm-no").text(t.studentDetailedRow.studentBasicInfo.admissionNumber)),"CLOSED"!==t.enquiryStatus&&($("#successful").closest("tr").css("display","none"),$("#student").closest("tr").css("display","none"),$("#student-adm-no").closest("tr").css("display","none"))},resetFields:function(){$("#tracking-id").text(""),$("#parent-name").text(""),$("#child-name").text(""),$("#standard").text(""),$("#phone-number").text(""),$("#email-id").text(""),$("#message").text(""),$("#successful").text(""),$("#student").text(""),$("#student-adm-no").text(""),$("#successful").closest("tr").css("display",""),$("#student").closest("tr").css("display",""),$("#student-adm-no").closest("tr").css("display","")},showEnquiry:function(t,e){var a=$(e).attr("id").trim(),n=$("#"+a).closest("tr").find(".enquiry-details-info").text().trim(),s=JSON.parse(n);$("#accept-action-button").hide(),$("#reject-action-button").hide(),$("#complete-action-button").hide(),$("#enquiry-id-action-div").text(s.enquiryId),"ACCEPTED"===t?$("#accept-action-button").show():"REJECTED"===t?$("#reject-action-button").show():"COMPLETED"===t&&$("#complete-action-button").show(),$("#reason-outcome").val(""),$("#enquiry-details-action-modal").modal("toggle")},updateEnquiryStatus:function(t,e,a,n){var s=$("#"+a).val(),i=$("#"+n).text();admissionEnquiry.dataCache={reason:s,status:t,enquiryId:i},$("#"+e).modal("toggle"),admissionEnquiry.updateActionModalContent(t),$("#enquiry-action-modal").modal("toggle")},updateActionModalContent:function(t){var e="",a="";"REJECTED"===t?(e="Reject Enquiry",a="Do you wish to reject the enquiry?"):"CLOSED"===t?(e="Close Enquiry",a="Do you wish to close the enquiry?"):(e="Accept Enquiry",a="Do you wish to accept the enquiry?"),$("#enquiry-title").text(e),$("#enquiry-details-manage-id").html(a)},confirmAction:function(){var t=admissionEnquiry.dataCache.reason,e=admissionEnquiry.dataCache.status,a=admissionEnquiry.dataCache.enquiryId,n=!1,s="";"CLOSED"==admissionEnquiry.dataCache.status&&(n=admissionEnquiry.dataCache.isSuccessful,null==admissionEnquiry.dataCache.studentId&&""!==admissionEnquiry.dataCache.studentId||(s=admissionEnquiry.dataCache.studentId));var i=academicSessionHandler.getSelectedSessionId();$("#enquiry-action-modal").modal("toggle");var o={academicSessionId:i,enquiryId:a,studentId:s,message:t,enquiryStatus:e,successful:n};ajaxClient.post("/admission/update-enquiry-status",{enquiryDetailsPayload:JSON.stringify(o)},function(t){$("#enquiry-status-modal-container").html(t),$("#update-enquiry-status-modal").modal({backdrop:"static",keyboard:!1}),admissionEnquiry.viewEnquiryDetails(!0,"SUBMITTED")})},showDetails:function(t){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/admission/enquiry-detail/session/"+e+"/"+t,function(t){$("#enquiryDetailsResult").html(t),$(".student-details-screen").css("display","none"),admissionEnquiry.bindEnquiryClickEvents(),admissionEnquiry.dataCache={},enquiryPagination.initPagination()})},editFollowUpDetails:function(t){initDateWithYearRange("-10:+10",!0),initPastDateById("enquiry-follow-up-date",4e3);var e=JSON.parse($(t).parent().find("#follow-up-json").text().trim());$("#update-transaction-entity-name").html("<option>"+e.entityName+"</option>"),$("#update-enquiry-follow-up-date").val(getFormattedDate(e.followUpDate)),$("#update-transaction-follow-up-mode").html("<option>"+e.followUpMode+"</option>"),$("#update-transaction-contact-person-name").val(e.contactPersonName),$("#update-transaction-follow-up-type").html("<option>"+e.followUpType+"</option>"),$("#update-transaction-conversation").val(e.conversation),null!=e.nextFollowUpDate?$("#update-next-enquiry-follow-up-date").val(getFormattedDate(e.nextFollowUpDate)):$("#update-next-enquiry-follow-up-date").val(""),$("#update-transaction-amount").val(e.amount),$("#update-enquiry-follow-up-id").text(e.followUpId),$("#student-id").text(e.entityId),$("#update-enquiry-follow-up-modal").modal("toggle")},closeEnquiry:function(t){var e=$(t).attr("id").trim(),a=$("#"+e).closest("tr").find(".enquiry-details-info").text().trim(),n=JSON.parse(a);$("#enquiry-outcome-id-action-div").text(n.enquiryId),$("#outcome").val(""),$("#is-successful").prop("checked",!1),$(".enquiry_selected_student_row").remove(),$("#enquiry-students-search").prop("disabled",!0),$("#close-details-action-modal").modal("toggle");academicSessionHandler.getSelectedSessionId();admissionEnquiry.bindSearchStudentEvent()},bindSearchStudentEvent:function(){var t="#enquiry-students-search-result";$("#enquiry-students-search").on("keyup",function(e){13==e.keyCode&&admissionEnquiry.doneStudentSearchTyping(t)}),liveSearchHandler.bindEvent("#enquiry-students-search",t,admissionEnquiry.doneStudentSearchTyping)},doneStudentSearchTyping:function(t){var e=$("#enquiry-students-search").val().trim();admissionEnquiry.studentLiveSearchEvent(e,t,admissionEnquiry.loadStudentDetails,"ENROLLED")},studentLiveSearchEvent:function(t,e,a,n){var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/admission/enquiry-student-live-search/"+s+"?searchText="+t+"&status="+n,function(t){$(e).html(t),admissionEnquiry.loadStudentDetails()})},loadStudentDetails:function(){$("#live-search-student-results .student-result-row .student-details").on("click",function(){var t=JSON.parse($(this).closest(".student-result-row").find(".student-info-div").text().trim());admissionEnquiry.addSelectedStudentRow(t)})},addSelectedStudentRow:function(t){var e=t.name,a=t.admissionNumber,n=siblingDetails.checkIfStringEmpty(t.fathersName)?"-":t.fathersName,s=t.studentSessionData.standardNameWithSection,i=t.studentId;closeButtonHTML=' <button type="button" class="close delete-student-row " aria-label="Close"> <span aria-hidden="true">&times;</span> </button>',$(".enquiry_selected_student_row").remove();var o='<div class="enquiry_selected_student_row " id="'+i+'" style="display: flex; justify-content: space-between; align-items: center; padding: 10px;"><div class="student-name-div" style="; ">Student: '+e+" ("+a+", "+s+", "+n+')</div><div class="close-button-div" style="margin-right: 300px; cursor: pointer;">'+closeButtonHTML+"</div></div>";$("#enquiry-students-search-row").after(o),$("#enquiry-students-search-result").html(""),$("#enquiry-students-search").val(""),siblingDetails.deletePurchaseItemEntry()},toggleStudentSelect:function(){const t=$("#is-successful").is(":checked");$("#enquiry-students-search").prop("disabled",!t),t||$(".enquiry_selected_student_row").remove()},closeEnquiryDetail:function(){var t=$("#outcome").val(),e=$("#enquiry-outcome-id-action-div").text(),a=$("#is-successful").is(":checked");if(a)var n=$(".enquiry_selected_student_row").attr("id");admissionEnquiry.dataCache={reason:t,status:"CLOSED",enquiryId:e,isSuccessful:a,studentId:n},$("#close-details-action-modal").modal("toggle"),admissionEnquiry.updateActionModalContent("CLOSED"),$("#enquiry-action-modal").modal("toggle")}},enquiryPagination={dataCache:{},initPagination:function(){pagination.bindEvents(function(){var t=enquiryPagination.dataCache.status;admissionEnquiry.viewEnquiryDetails(!1,t)},function(){var t=enquiryPagination.dataCache.status;admissionEnquiry.viewEnquiryDetails(!1,t)},function(){var t=enquiryPagination.dataCache.status;admissionEnquiry.viewEnquiryDetails(!1,t)},function(){var t=enquiryPagination.dataCache.status;admissionEnquiry.viewEnquiryDetails(!0,t)})}};function loadAllStudentsPage(){ajaxClient.get("/admission/relieve-student-screen",function(t){$("#main-content").html(t),initiateAllStudentPage()})}function reloadAllStudentsPage(){ajaxClient.get("/admission/relieve-student-screen-reload",function(t){$("#search-students-without-academic-session-results").html(t),returnToRelieveStudentMainScreen(),initiateAllStudentPage()})}function initiateAllStudentPage(){registerStudentWithoutAcademicSessionSearchCallback(bindEnrolledStudentStatusChangeButtonCallBack),studentSearchWithoutAcademicSession.search("",bindEnrolledStudentStatusChangeButtonCallBack)}function returnToMainScreenFromRelieveStudentPage(t){var e=$("#student-status").text().trim();"ENROLMENT_PENDING"===e?reloadPendingEnrolementStudentDetailsPage():"ENROLLED"==e?reloadStudentDetailsPage():"RELIEVED"==e?reloadRelievedStudentDetailsPage():"NSO"==e?reloadNSOStudentDetailsPage():reloadAllStudentsPage()}function bindEnrolledStudentStatusChangeButtonCallBack(t){bindStudentStatusChangeButtonCallBack(ALL_ENROLLED_STUDENT_SCREEN_ID)}function bindStudentStatusChangeButtonCallBack(t){$(".relieve-student-button").on("click",function(){loadStudentStatusChangeDetails($(this).attr("id"),"RELIEVED",t)}),$(".mark-nso-student-button").on("click",function(){loadStudentStatusChangeDetails($(this).attr("id"),"NSO",t)}),$(".enroll-student-button").on("click",function(){var t=$(this).attr("id");$("#enroll-student-details-modal").modal("toggle"),$("#enroll-student-id").text(t)})}function loadStudentStatusChangeDetails(t,e,a){$(".relieve-student-screen").attr("style","display:none"),$(".student-details-screen").attr("style","display:none"),ajaxClient.get("/admission/relieve-student-status/"+t+"/"+e,function(t){$("#relieve-student-status-screen").html(t),$("#relieve-status-incoming-page-id").text(a),$("#relieve-student-status-screen").attr("style","display:block"),initDateInput(),bindRelieveReason()})}function bindRelieveReason(){$(".relieve-reason").on("change",function(){"other"==$(this).find(":selected").val().trim()?$("#other-relieve-reason-container").attr("style","display:block"):$("#other-relieve-reason-container").attr("style","display:none")})}function returnToRelieveStudentMainScreen(){$(".relieve-student-screen").attr("style","display:none"),$("#relieve-student-main-screen").attr("style","display:block"),$(".student-details-screen").attr("style","display:block"),$("#student-list\\.update-student-screen").attr("style","display:none"),$("#student-list\\.upload-document-screen").attr("style","display:none")}function updateRelieveDetails(t){var e=$("#p-student-id").text();$("#relieve-student-confirmation-modal").modal("toggle"),relieveStudent(t,e,"RELIEVED",!0)}function relieveStudent(t,e,a,n){$("#relieve-nso-student-confirmation-modal").modal("toggle"),null==n||""===n?n=!1:$("#modal-footer-yes-button").text("");var s=getDate($(t).parent().parent().find("#relieve-date").val());if(null!=s){var i="",o=null,r=null;if("RELIEVED"==a){if(""==(i="other"==$(t).parent().parent().find(".relieve-reason").find(":selected").val().trim()?$(t).parent().parent().find("#other-relieve-reason").val().trim():$(t).parent().parent().find(".relieve-reason").find(":selected").text().trim()))return void showErrorDialogBox("Please select the proper relieve reason.");if(null==(o=$(t).parent().parent().find("#last-active-session").val())||""===o)return void showErrorDialogBox("Please select a last active session to relieve student.");if(null==(r=$(t).parent().parent().find("#is-last-session-completed").val())||""===r)return void showErrorDialogBox("Please select if last active session was completed or not.")}var d={TRANSFER_REASON:i,CODE_OF_CONDUCT:$(t).parent().parent().find("#code-of-conduct").find(":selected").text().trim()},l={LAST_ACTIVE_SESSION:o,LAST_ACTIVE_SESSION_COMPLETED:r},u={relieveDate:s.getTime()/1e3,relieveReason:i,tcVariables:d,relievedMetadata:l};ajaxClient.post("/admission/relieve-student/"+e+"/"+a+"/"+n,{relievePayload:JSON.stringify(u)},function(t){$("#relieve-student-confirmation-modal-container").html(t),$("#relieve-student-success-status-modal").modal({backdrop:"static",keyboard:!1}),$("#relieve-student-status-screen").attr("style","display:none;"),$("#student-details").attr("style","display:block;"),$("#relieve-student-main-screen").attr("style","display:block;")})}else showErrorDialogBox("Please fill date.")}function enrollNSOStudent(){$("#enroll-student-details-modal").modal("toggle");var t=$("#enroll-student-id").text();ajaxClient.post("/admission/enroll-student/"+t+"/ENROLLED",{},function(t){$("#enroll-student-confirmation-modal-container").html(t),$("#relieve-student-success-status-modal").modal({backdrop:"static",keyboard:!1})})}function registerUpdateStudentDetailsCallBack(){$(".update-student-info").on("click",function(){var t=$(this).parent().parent().find(".student-info-json").text().trim();fillStudentInformation(JSON.parse(t)),bindAdmissionClassSectionEvent(),enforceConstraints(),permanentAddressUpdate("update-permanent-same-as-present","update\\.student-permanent-address","update\\.student-city","update\\.student-state","update\\.student-post-office","update\\.student-police-station","update\\.student-zipcode","update\\.student-present-address","update\\.student-present-city","update\\.student-present-state","update\\.student-present-post-office","update\\.student-present-police-station","update\\.student-present-zipcode"),$(".student-details-screen").attr("style","display:none"),$("#student-list\\.update-student-screen").attr("style","display:block"),$("#screen-name").text("STUDENT-LIST")})}var OTHER_DOCUMENT_TYPE="OTHER",VIEW_SIBLINGS="VIEW_SIBLINGS",ADD_SIBLINGS="ADD_SIBLINGS",DELETE_SIBLINGS="DELETE_SIBLINGS";function loadStudentDetailsPage(){ajaxClient.get("/admission/student-details",function(t){$("#main-content").html(t),initiateStudentPage()})}function reloadStudentDetailsPage(){ajaxClient.get("/admission/student-details-reload",function(t){$("#student-main-screen").html(t),initiateStudentPage()})}function initiateStudentPage(){$("#student-status").text("ENROLLED"),academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession),searchStudents(!0),$(".selectpicker").selectpicker(),registerStudentSearchCallback(),bindStudentListPageEvents(),initDate(36500),$(document).ready(function(){$('[data-toggle="tooltip"]').tooltip()})}function bindStudentListPageEvents(){previousTabSwtichingEvent(),nextTabSwtichingEvent(),bindRemoveErrorDisplayEvent(),studentList.initPagination()}function loadRelievedStudentDetailsPage(){ajaxClient.get("/admission/relieved-student-details",function(t){$("#main-content").html(t),initiateRelievedStudentPage()})}function reloadRelievedStudentDetailsPage(){ajaxClient.get("/admission/relieved-student-details-reload",function(t){$("#relieve-student-main-screen").html(t),initiateRelievedStudentPage()})}function initiateRelievedStudentPage(){$("#student-status").text("RELIEVED"),academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession),searchStudents(!0),$(".selectpicker").selectpicker(),registerStudentSearchCallback(),registerViewStudentDetailsCallBack(),bindStudentListPageEvents()}function loadNSOStudentDetailsPage(){ajaxClient.get("/admission/relieved-student-details",function(t){$("#main-content").html(t),initiateNSOStudentPage()})}function reloadNSOStudentDetailsPage(){ajaxClient.get("/admission/relieved-student-details-reload",function(t){$("#relieve-student-main-screen").html(t),initiateNSOStudentPage()})}function initiateNSOStudentPage(){$("#student-status").text("NSO"),academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession),searchStudents(!0),$(".selectpicker").selectpicker(),registerStudentSearchCallback(),registerViewStudentDetailsCallBack(),bindStudentListPageEvents(),bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID)}function loadPendingEnrolmentsPage(){ajaxClient.get("/admission/pending-enrolments-details",function(t){$("#main-content").html(t),initiatePendingStudentPage()})}function reloadPendingEnrolementStudentDetailsPage(){ajaxClient.get("/admission/pending-enrolments-details-reload",function(t){$("#student-main-screen").html(t),initiatePendingStudentPage()})}function initiatePendingStudentPage(){$("#student-status").text("ENROLMENT_PENDING"),academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession),searchStudents(!0),$(".selectpicker").selectpicker(),registerStudentSearchCallback(),registerViewStudentDetailsCallBack(),registerEnrollStudentDetailsCallBack(),bindStudentListPageEvents(),initDate(36500),$(document).ready(function(){$('[data-toggle="tooltip"]').tooltip()})}var studentRegistration={dataCache:{},loadHomePage:function(){ajaxClient.get("/admission/student-registration-home",function(t){$("#main-content").html(t),studentRegistration.dataCache={},academicSessionHandler.bindSessionChangeEvent(studentRegistration.changeSession),studentRegistration.bindStatusChangeEvent(),studentRegistration.searchRegisteredStudents(!0),studentRegistration.registerStudentSearchCallback()})},changeSession:function(){var t=academicSessionHandler.getSelectedSessionDisplayName();$("#academic-year-display").text("Student Registrations : "+t),studentRegistration.searchRegisteredStudents(!0)},initPagination:function(){pagination.bindEvents(function(){studentRegistration.searchRegisteredStudents(!1)},function(){studentRegistration.searchRegisteredStudents(!1)},function(){studentRegistration.searchRegisteredStudents(!1)},function(){studentRegistration.searchRegisteredStudents(!0)})},registerStudentSearchCallback:function(){$("#search-users").on("click",function(){studentRegistration.searchRegisteredStudents(!0)}),$("#search-user-input").on("keyup",function(t){13==t.keyCode&&studentRegistration.searchRegisteredStudents(!0)})},searchRegisteredStudents:function(t){var e=$(".page-item.active").find(".page-number").text().trim();t&&(e=1);var a=$("#items-per-page").val();null!=e&&""!=e||(e=1);var n=(e-1)*a,s=$("#search-user-input").val().trim(),i=academicSessionHandler.getSelectedSessionId(),o=studentRegistration.getStatus();ajaxClient.get("/admission/student-registration/search/"+i+"?search_text="+s+"&offset="+n+"&itemsPerPage="+a+"&status="+o,function(t){$("#search-user-result").html(t),pagination.updatePaginationDetails("search-user-result","student-registration-fixed-height-list-wrapper"),studentRegistration.initPagination(),studentRegistration.bindActionEvents()})},getStatus:function(){var t=studentRegistration.dataCache.pageId;return"student-registration-status-applied"==t?"APPLIED":"student-registration-status-approved"==t?"APPROVED":"student-registration-status-rejected"==t?"REJECTED":"APPLIED"},bindStatusChangeEvent:function(){$(".student-registration-status-options").click(function(){studentRegistration.dataCache.pageId=$(this).attr("id").trim(),studentRegistration.searchRegisteredStudents(!0)})},bindActionEvents:function(){$(".preview-student-info").click(function(){"APPLIED"==studentRegistration.getStatus()?$("#apply-registration-action-buttons").attr("style","display:block"):$("#apply-registration-action-buttons").attr("style","display:none"),$("#preview-student-registration-details-modal").modal("toggle");var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t);studentRegistration.dataCache.selectedStudent=e,studentRegistration.fillStudentPreviewDetails(e)})},applyRegistrationActionConfirmation:function(t){$("#preview-student-registration-details-modal").modal("toggle");var e=studentRegistration.dataCache.selectedStudent,a="",n="";"approve"==t?(a="You are <strong>approving</strong>  the registration reqest of <strong>"+e.studentBasicInfo.name+" (Registration# "+e.studentBasicInfo.onlineRegistrationNumber+")</strong> . Do you want to proceed?",n='<button type="button" class="btn btn-success" onclick="studentRegistration.loadRegistrationApprovalForm()">Yes, Approve</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'):"reject"==t&&(a="You are <strong> declining</strong>  the registration reqest of <strong>"+e.studentBasicInfo.name+" (Registration# "+e.studentBasicInfo.onlineRegistrationNumber+")</strong> . Do you want to proceed?",n='<button type="button" class="btn btn-danger" onclick="studentRegistration.rejectStudentRegistration()">Yes, Reject</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'),$("#student-registration-confirmation-text").html(a),$("#student-registration-action-confirm-modal").find(".modal-footer").html(n),$("#student-registration-action-confirm-modal").modal({backdrop:"static",keyboard:!1})},loadRegistrationApprovalForm:function(){$("#student-registration-action-confirm-modal").modal("toggle");var t=studentRegistration.dataCache.selectedStudent;ajaxClient.get("/admission/registration-enroll-metadata/"+t.academicSession.academicSessionId+"/"+t.standard.standardId,function(t){$("#register-student-form-modal-container").html(t),$("#register-student-form-modal").modal("toggle")})},rejectStudentRegistration:function(){$("#student-registration-action-confirm-modal").modal("toggle");var t=studentRegistration.dataCache.selectedStudent;ajaxClient.post("/admission/student-registration/"+t.studentRegistrationId+"/apply-action/REJECTED",{},function(t){$("#student-registration-status-modal-container").html(t),$("#student-registration-status-modal").modal("toggle"),studentRegistration.searchRegisteredStudents(!0)})},approveStudentRegistration:function(){$("#register-student-form-modal").modal("toggle");var t=studentRegistration.dataCache.selectedStudent,e=$("#student-registration-number").val(),a=[];$("#applicable-enrollment-fees").find("input.applicable-fee-structure-assign-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).attr("id");a.push(t)}});var n={registrationNumber:e,feeStructureIds:a};ajaxClient.post("/admission/student-registration/"+t.studentRegistrationId+"/apply-action/APPROVED",{registrationDetails:JSON.stringify(n)},function(t){$("#student-registration-status-modal-container").html(t),$("#student-registration-status-modal").modal("toggle"),studentRegistration.searchRegisteredStudents(!0)})},fillStudentPreviewDetails:function(t){"NOT_APPLICABLE"==t.paymentStatus?$(".student-payment-information").attr("style","display:none;"):($(".student-payment-information").attr("style","display:block;"),$("#preview-payment-status").text(t.paymentStatusDisplayName),$("#preview-assigned-amount").text(t.paymentData.assignedAmount),$("#preview-paid-amount").text(t.paymentData.paidAmount),$("#preview-transaction-mode").text(t.paymentData.transactionModeDisplayName),$("#preview-transaction-date").text(getFormattedDate(t.paymentData.transactionDate)),$("#preview-transaction-reference").text(t.paymentData.transactionReference)),$("#preview\\.student-name").text(t.studentBasicInfo.name),$("#preview\\.registration-number").text(t.studentBasicInfo.onlineRegistrationNumber),$("#preview\\.standard").text(t.standard.displayName),$("#preview\\.gender").text(t.studentBasicInfo.gender),null!=t.studentBasicInfo.dateOfBirth&&t.studentBasicInfo.dateOfBirth>0?$("#preview\\.dob").text(getFormattedDate(t.studentBasicInfo.dateOfBirth)):$("#preview\\.dob").text(""),$("#preview\\.birth-place").text(t.studentBasicInfo.birthPlace),$("#preview\\.category").text(t.studentBasicInfo.userCategory),$("#preview\\.religion").text(t.studentBasicInfo.religion),$("#preview\\.caste").text(t.studentBasicInfo.caste),$("#preview\\.rte").text(t.studentBasicInfo.rteDisplay),$("#preview\\.mother-tongue").text(t.studentBasicInfo.motherTongue),$("#preview\\.area-type").text(t.studentBasicInfo.areaType),$("#preview\\.specially-abled").text(t.studentBasicInfo.speciallyAbledDisplay),$("#preview\\.bpl").text(t.studentBasicInfo.bplDisplay),$("#preview\\.present-address").text(getStudentPresentAddress(t.studentBasicInfo)),$("#preview\\.address").text(getStudentPermanentAddress(t.studentBasicInfo)),$("#preview\\.aadhar-number").text(t.studentBasicInfo.aadharNumber),$("#preview\\.registration-date").text(getFormattedDate(t.studentBasicInfo.registrationDate)),$("#preview\\.admission-date").text(getFormattedDate(t.studentBasicInfo.admissionDate)),$("#preview\\.primary-contact-number").text(t.studentBasicInfo.primaryContactNumber),$("#preview\\.primary-email").text(t.studentBasicInfo.primaryEmail),$("#preview\\.whatsapp-number").text(t.studentBasicInfo.whatsappNumber),$("#preview\\.is-sponsored").text(t.studentBasicInfo.sponsoredDisplay),$("#preview\\.nationality").text(t.studentBasicInfo.nationality),$("#preview\\.student-new-admission").text(t.studentBasicInfo.newAdmissionDisplay),$("#preview\\.student-hosteller").text(t.studentBasicInfo.hostellerDisplay),null!=t.studentImage?$("#preview\\.student-image").text("Uploaded"):$("#preview\\.student-image").text("Not present"),$("#preview\\.student-house").text(null==t.studentBasicInfo.instituteHouse?"":t.studentBasicInfo.instituteHouse.houseName),$("#preview\\.student-admission-in-class").text(t.studentBasicInfo.admissionInClass),$("#preview\\.student-name-as-per-aadhar").text(t.studentBasicInfo.studentNameAsPerAadhar),$("#preview\\.child-category-criteria").text(t.studentBasicInfo.childCategoryCriteria),$("#preview\\.specially-abled-type").text(t.studentBasicInfo.speciallyAbledType),$("#preview\\.student-mother-name").text(t.studentFamilyInfo.mothersName),$("#preview\\.student-father-name").text(t.studentFamilyInfo.fathersName),$("#preview\\.student-mother-qualification").text(t.studentFamilyInfo.mothersQualification),$("#preview\\.student-father-qualification").text(t.studentFamilyInfo.fathersQualification),$("#preview\\.mother-contact-number").text(t.studentFamilyInfo.mothersContactNumber),$("#preview\\.father-contact-number").text(t.studentFamilyInfo.fathersContactNumber),$("#preview\\.mother-occupation").text(t.studentFamilyInfo.mothersOccupation),$("#preview\\.father-occupation").text(t.studentFamilyInfo.fathersOccupation),$("#preview\\.mother-aadhar-number").text(t.studentFamilyInfo.mothersAadharNumber),$("#preview\\.father-aadhar-number").text(t.studentFamilyInfo.fathersAadharNumber),$("#preview\\.mother-pan-card-details").text(t.studentFamilyInfo.mothersPanCardDetails),$("#preview\\.father-pan-card-details").text(t.studentFamilyInfo.fathersPanCardDetails),$("#preview\\.annual-family-income").text(t.studentFamilyInfo.approxFamilyIncome),null!=t.studentGuardianInfoList&&t.studentGuardianInfoList.length>0?($("#preview\\.guardian-name").text(t.studentGuardianInfoList[0].guardianName),$("#preview\\.guardian-relation").text(t.studentGuardianInfoList[0].relation),$("#preview\\.guardian-age").text(t.studentGuardianInfoList[0].age),$("#preview\\.guardian-gender").text(t.studentGuardianInfoList[0].gender),$("#preview\\.guardian-occupation").text(t.studentGuardianInfoList[0].occupation),$("#preview\\.guardian-email").text(t.studentGuardianInfoList[0].email),$("#preview\\.guardian-contact-number").text(t.studentGuardianInfoList[0].contactNumber),$("#preview\\.guardian-address").text(getGuardianAddress(t.studentGuardianInfoList[0]))):($("#preview\\.guardian-name").text(""),$("#preview\\.guardian-relation").text(""),$("#preview\\.guardian-age").text(""),$("#preview\\.guardian-gender").text(""),$("#preview\\.guardian-occupation").text(""),$("#preview\\.guardian-email").text(""),$("#preview\\.guardian-contact-number").text(""),$("#preview\\.guardian-address").text("")),$("#preview\\.previous-school-name").text(t.studentPreviousSchoolInfo.schoolName),$("#preview\\.medium").text(t.studentPreviousSchoolInfo.medium),$("#preview\\.class-passed").text(t.studentPreviousSchoolInfo.classPassed),$("#preview\\.year-of-passing").text(t.studentPreviousSchoolInfo.yearOfPassing),$("#preview\\.result").text(t.studentPreviousSchoolInfo.result),$("#preview\\.percentage-grade").text(t.studentPreviousSchoolInfo.percentage),$("#preview\\.student-is-admission-tc-based").text(t.studentPreviousSchoolInfo.admissionTcBasedDisplay),$("#preview\\.previous-school-tc-number").text(t.studentPreviousSchoolInfo.tcNumber),$("#preview\\.blood-group").text(t.studentMedicalInfo.bloodGroup),$("#preview\\.blood-pressure").text(t.studentMedicalInfo.bloodPressure),$("#preview\\.pulse").text(t.studentMedicalInfo.pulse),$("#preview\\.height").text(t.studentMedicalInfo.height),$("#preview\\.weight").text(t.studentMedicalInfo.weight),$("#preview\\.date-of-physical-examination").text(getFormattedDate(t.studentMedicalInfo.dateOfPhysicalExamination))}},admissionStudentDetails={changeSession:function(){searchStudents(!0)}};function registerStudentSearchCallback(){$("#searchStudents").on("click",function(){searchStudents(!0)}),$("#searchStudentsInput").on("keyup",function(t){13==t.keyCode&&searchStudents(!0)})}function searchStudents(t){var e=$("#student-status").text().trim(),a=$("#searchStudentsInput").val(),n=academicSessionHandler.getSelectedSessionId(),s=$(".page-item.active").find(".page-number").text().trim();t&&(s=1);var i=$("#items-per-page").val();null!=s&&""!=s||(s=1);var o=(s-1)*i;$("#searchStudentsResult").html("");var r="";"none"!=$("#advance-search-option-div").css("display")&&null!=(r=$("select.filter-student-classes").val())&&null!=r&&(r=r.join(",")),"RELIEVED"===e?ajaxClient.get("/admission/relieved-student-search/"+n+"/"+o+"/"+i+"?text="+a+"&requiredStandards="+r,function(t){$("#searchStudentsResult").html(t);var e=$("#sidebar").height()-$("#searchStudentsResult").position().top-50;$("#student-fixed-height-list-wrapper").attr("style","height:"+e+"px;  overflow-y: scroll; cursor: pointer;");var a=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(a.itemsPerPage),$(".page-item").removeClass("active");a.offset,a.itemsPerPage;$("#page-number-"+s).addClass("active"),registerViewStudentDetailsCallBack(),registerEnrollStudentDetailsCallBack(),registerUpdateStudentDetailsCallBack(),registerUploadStudentDocumentCallBack(),registerUpdateRelieveDetailsCallBack(),registerTransferCertificateCallBack(),studentList.initPagination(),bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID)}):ajaxClient.get("/admission/student-search/"+n+"/"+e+"/"+o+"/"+i+"?text="+a+"&requiredStandards="+r,function(t){$("#searchStudentsResult").html(t);var e=$("#sidebar").height()-$("#searchStudentsResult").position().top-50;$("#student-fixed-height-list-wrapper").attr("style","height:"+e+"px;  overflow-y: scroll; cursor: pointer;");var a=JSON.parse($("#pagination-info").text().trim());$("#items-per-page").val(a.itemsPerPage),$(".page-item").removeClass("active");a.offset,a.itemsPerPage;$("#page-number-"+s).addClass("active"),registerViewStudentDetailsCallBack(),registerEnrollStudentDetailsCallBack(),registerUpdateStudentDetailsCallBack(),registerUploadStudentDocumentCallBack(),registerUpdateRelieveDetailsCallBack(),registerTransferCertificateCallBack(),studentList.initPagination(),bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID)})}function registerViewStudentDetailsCallBack(){$(".view-student-info").on("click",function(){$("#view-student-details-modal").modal("toggle");var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t);studentDetails.fillStudentViewDetails(e)})}function registerEnrollStudentDetailsCallBack(){$(".enroll-student-info").on("click",function(){var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t),a=e.studentId,n=e.studentBasicInfo.name,s=e.studentBasicInfo.registrationDate,i=academicSessionHandler.getSelectedSessionId();"True"==$(".restrict-enrollment-payment-pending").text().trim()?ajaxClient.get("/admission/verify-metadata/"+i+"/"+a,function(e){$("#relieve-student-confirmation-modal-container").html(e),"False"!=$("#success").text().trim()?registerEnrollStudentDetails(t,s,i,n,a):$("#enroll-status-modal").modal("toggle")}):registerEnrollStudentDetails(t,s,i,n,a)})}function registerEnrollStudentDetails(t,e,a,n,s){ajaxClient.get("/admission/enroll-metadata/"+a+"/"+s,function(i){$("#student-main-screen").html(i),$("#enroll-student-id").text(s),$("#enroll-student-name").text(n),$("#academic-session-id").text(a),$("#enroll-student-registration-date").text(e),$("#student-details-json").text(t),$(".datatables-reponsive").DataTable({paging:!0,searching:!0}),assignTransportFees.transportBindEvents(),assignTransportFees.resetTransportForm(),discountStructure.initDataCache(),initDateWithYearRange("-5:+5",!0),bindSameAsRegistrationDateCheckbox(),$('[data-toggle="tooltip"]').tooltip(),bindSearchEnrollStudentEvent()})}function autoSuggestHouse(t,e){var a=$("#student-details-json").text().trim(),n=JSON.parse(a),s=n.studentId,i=(n.studentBasicInfo.name,n.studentAcademicSessionInfoResponse.standard.standardId),o=n.studentBasicInfo.gender,r=$("#academic-session-id").text(),d=$("#selected-sibling-student-id").text();""===d&&(d=null);var l=n.studentBasicInfo.siblingGroupId;t=t;"UPDATE"===e&&(o=$("#update\\.student-gender").val()),""!==o&&void 0!==o||(o=null),$("#update\\.student-house").val(""),$("#add-student-house").val("");var u={studentId:s,studentStandardId:i,gender:o,siblingStudentId:d,siblingGroupId:l,clearCurrentHouse:t};console.log(u),ajaxClient.post("/admission/auto-suggest-house/"+r,{autoSuggestHousePayload:JSON.stringify(u)},function(t){$("#auto-suggest-house-span").html(t)})}function bindSearchEnrollStudentEvent(){var t="#student-payment-search-result";$("#student-payment-search").on("click",function(){doneStudentSearchTyping(t)}),$("#student-payment-search-text").on("keyup",function(e){13==e.keyCode&&doneStudentSearchTyping(t)}),liveSearchHandler.bindEvent("#student-payment-search-text",t,doneStudentSearchTyping)}function doneStudentSearchTyping(t){var e=$("#student-payment-search-text").val().trim();studentLiveSearchEvent($("#academic-session-id").text().trim(),e,t,loadSiblingStudentDetails,"ENROLLED")}function studentLiveSearchEvent(t,e,a,n,s){ajaxClient.get("/admission/student-live-with-session-search/"+t+"?searchText="+e+"&status="+s,function(t){$(a).html(t),studentLiveSearchHandler.bindStudentSearchClickEvent(a,n)})}function loadSiblingStudentDetails(t){var e=$("#academic-session-id").text().trim();ajaxClient.get("/admission/student-sibling-details/"+e+"/"+t,function(t){$("#sibling-listing-div").html(t)})}function bindSameAsRegistrationDateCheckbox(){$("#same-as-registration-date").change(function(){var t=$("#same-as-registration-date").is(":checked"),e=new Date,a=getDate(e).getTime()/1e3;t&&(a=$("#enroll-student-registration-date").text()),setFormattedDate(a,"#enroll\\.student-admission-date")})}function removeEnrollSiblingDetails(){$("#sibling-listing-div").html("")}var assignTransportFees={dataCache:{},transportBindEvents:function(){assignTransportFees.initDataCache(),assignTransportFees.bindDivideEquallyCheck(),assignTransportFees.bindRouteSelectEvent(),assignTransportFees.bindFullSessionTransportCheck()},bindDivideEquallyCheck:function(){$("#divide-equally-check").change(function(){if($(this).is(":checked")){$(".transport-fees-proportion-input").prop("readonly",!0);var t=1;if($(".transport-fee-checkbox").each(function(e,a){$(this).is(":checked")&&(t+=1)}),t>1){var e=100/(t-1);$(".transport-fees-proportion-input").val(Math.round(100*e)/100)}}else $(".transport-fees-proportion-input").prop("readonly",!1)})},initDataCache:function(){var t=readJson("p.trasport-service-route-json"),e=readJson("p.trasport-fee-config-json");assignTransportFees.dataCache.routes=t,assignTransportFees.dataCache.feeConfigs=e,assignTransportFees.dataCache.transportAssignmentPayload=null},createRouteOptionList:function(){for(var t=assignTransportFees.dataCache.routes,e='<option value="">--select--</option>',a=0;a<t.length;a++)e+='<option value="'+t[a].serviceRouteId+'">'+t[a].serviceRouteName+"</option>";return e},resetTransportForm:function(){$("#trasnport-assign-footer").attr("style",""),$("#transport-assignment-area").val(""),$("#transport-assignment-area").attr("disabled",!1),$("#pickup-assignment-service-route").html('<option value="">--select--</option>'),$("#drop-assignment-service-route").html('<option value="">--select--</option>'),$("#pickup-assignment-service-route").attr("disabled",!1),$("#drop-assignment-service-route").attr("disabled",!1),assignTransportFees.bindRouteAreaSelectEvent(),$("#complete-session-transport").prop("checked",!0),$("#complete-session-transport").prop("disabled",!1),$("#transport-assign-dates").attr("style","display:none;"),$(".transport-fees-amount-input").prop("readonly",!0),$("#transport-start-date").val(""),$("#transport-end-date").val(""),$("#transport-start-date").prop("readonly",!1),$("#transport-end-date").prop("readonly",!1),$("#transport-start-date").css("pointer-events",""),$("#transport-end-date").css("pointer-events",""),$("input.transport-fees-amount-input").each(function(){$(this).val(0)})},bindRouteSelectEvent:function(){$("#pickup-assignment-service-route").change(function(){assignTransportFees.computeTransportFeeAmount()}),$("#drop-assignment-service-route").change(function(){assignTransportFees.computeTransportFeeAmount()})},getAreaOptions:function(t,e){for(var a='<option value="">--select--</option>',n=0;n<t.length;n++)if(t[n].serviceRouteId==e){for(var s=t[n],i=0;i<s.stoppagesList.length;i++)a+='<option value="'+s.stoppagesList[i].transportArea.areaId+'">'+s.stoppagesList[i].transportArea.area+"</option>";break}return a},getRouteOptions:function(t,e,a){for(var n='<option value="">--select--</option>',s=0;s<t.length;s++){var i=t[s];if(i.transportServiceRouteMetadata.routeType==e)for(var o=0;o<i.stoppagesList.length;o++){if(i.stoppagesList[o].transportServiceRouteStoppageDetails.transportArea.areaId.toString()==a){n+='<option value="'+i.transportServiceRouteMetadata.serviceRouteId+'">'+i.transportServiceRouteMetadata.serviceRouteName+"</option>";break}}}return n},bindRouteAreaSelectEvent:function(){$("#transport-assignment-area").change(function(){var t=$(this).find(":selected").val().trim();assignTransportFees.populateServiceRoutesForArea(t)})},populateServiceRoutesForArea:function(t){var e=assignTransportFees.dataCache.routes,a=assignTransportFees.getRouteOptions(e,"PICK_UP",t),n=assignTransportFees.getRouteOptions(e,"DROP_OFF",t);$("#pickup-assignment-service-route").html(a),$("#drop-assignment-service-route").html(n),assignTransportFees.bindRouteSelectEvent()},computeTransportFeeAmount:function(t,e){for(var a=$("#pickup-assignment-service-route").find(":selected").val().trim(),n=$("#drop-assignment-service-route").find(":selected").val().trim(),s=(e=parseInt($("#transport-assignment-area").find(":selected").val().trim()),assignTransportFees.dataCache.routes),i=0,o=0;o<s.length;o++){var r=s[o];if("PICK_UP"==r.transportServiceRouteMetadata.routeType&&a==r.transportServiceRouteMetadata.serviceRouteId)for(var d=0;d<r.stoppagesList.length;d++){if((l=r.stoppagesList[d]).transportServiceRouteStoppageDetails.transportArea.areaId==e&&null!=l.amount&&l.amount>0){i+=l.amount;break}}if("DROP_OFF"==r.transportServiceRouteMetadata.routeType&&n==r.transportServiceRouteMetadata.serviceRouteId)for(d=0;d<r.stoppagesList.length;d++){var l;if((l=r.stoppagesList[d]).transportServiceRouteStoppageDetails.transportArea.areaId==e&&null!=l.amount&&l.amount>0){i+=l.amount;break}}}var u=assignTransportFees.dataCache.feeConfigs.transportConfiguredFeeDatas;for(o=0;o<u.length;o++){var c=i*u[o].moduleFeeProportion.feeProportion;$("#"+u[o].feeConfigurationResponse.feeConfigurationBasicInfo.feeId).val(round(c,2))}},bindFullSessionTransportCheck:function(){$("#complete-session-transport").change(function(){$(this).is(":checked")?($("#transport-assign-dates").attr("style","display:none"),$(".transport-fees-amount-input").prop("readonly",!0),assignTransportFees.computeTransportFeeAmount()):($("#transport-assign-dates").attr("style",""),$(".transport-fees-amount-input").prop("readonly",!1))})},addTransportService:function(){var t=$("#pickup-assignment-service-route").find(":selected").val().trim(),e=$("#drop-assignment-service-route").find(":selected").val().trim(),a=$("#transport-assignment-area").find(":selected").val().trim(),n=$("#enroll-student-id").text().trim(),s=$("#academic-session-id").text().trim();if(""===a)return!0;if(""===t&&""===e)return alert("Please select either a pickup route or drop route to assign transport!"),!1;var i=$("#complete-session-transport").is(":checked"),o=0,r=0;if(!i){var d=$("#transport-start-date").val(),l=$("#transport-end-date").val();if(null==d||null==l||null==d||null==l||""===d||""===l)return alert("Service start and end date are mandatory"),!1;o=getDate(d).getTime()/1e3,r=getDate(l).getTime()/1e3}var u=[];$("input.transport-fees-amount-input").each(function(){var t=$(this).attr("id"),e=parseFloat($(this).val());u.push({feeId:t,amount:e})});var c={academicSessionId:s,studentId:n,pickupServiceRouteId:t,dropServiceRouteId:e,areaId:a,startDate:o,endDate:r,completeSession:i,transportHistoryFeeIdAmountList:u};return assignTransportFees.dataCache.transportAssignmentPayload=c,!0}};function registerUpdateStudentDetailsCallBack(){$(".update-student-info").on("click",function(){var t=academicSessionHandler.getSelectedSessionId(),e=$(this).parent().parent().find(".student-info-json").text().trim();fillStudentInformation(JSON.parse(e)),bindAdmissionClassSectionEvent(),enforceConstraints(),permanentAddressUpdate("update-permanent-same-as-present","update\\.student-permanent-address","update\\.student-city","update\\.student-state","update\\.student-post-office","update\\.student-police-station","update\\.student-zipcode","update\\.student-present-address","update\\.student-present-city","update\\.student-present-state","update\\.student-present-post-office","update\\.student-present-police-station","update\\.student-present-zipcode"),$("#student-details-json").text(e),$("#academic-session-id").text(t),$("#auto-suggest-house-span").text(""),$(".student-details-screen").attr("style","display:none"),$("#student-list\\.update-student-screen").attr("style","display:block"),$("#screen-name").text("STUDENT-LIST")})}function registerUpdateRelieveDetailsCallBack(){$(".update-student-relieve-details").on("click",function(){$("#relieve-student-confirmation-modal").modal("toggle");var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t),a=e.studentId;$("#p-student-id").text(a),initDateInput(),bindRelieveReason(),setFormattedDate(e.studentBasicInfo.relieveDate,"#relieve-date"),$(".relieve-reason").val(e.studentBasicInfo.relieveReason),$("#code-of-conduct").val(null==e.tcVariables?"":e.tcVariables.CODE_OF_CONDUCT),$("#last-active-session").val(null==e.studentBasicInfo.relievedMetadata?"":e.studentBasicInfo.relievedMetadata.LAST_ACTIVE_SESSION),$("#is-last-session-completed").val(null==e.studentBasicInfo.relievedMetadata?"":e.studentBasicInfo.relievedMetadata.LAST_ACTIVE_SESSION_COMPLETED)})}function registerTransferCertificateCallBack(){$(".generate-student-tc-form").on("click",function(){var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t),a=e.studentId;"true"===$("#is-detailed-tc-enabled").text()?ajaxClient.get("/admission/student-tc-details/"+a,function(t){$("#student-list\\.generate-tc-document-screen").html(t),$(".student-details-screen").attr("style","display:none"),$("#student-list\\.generate-tc-document-screen").attr("style","display:block"),$("#tc-generation-student-id").text(e.studentId),$("#tc-generation-student-name").text(e.studentBasicInfo.name),initDateWithYearRange("-20:+5",!1),initPastDateById("tc-generation-date",2e3),initPastDateById("tc-relieve-date",2e3)}):transferCertificate.generateStaticTC(a)})}function registerUploadStudentDocumentCallBack(){$(".upload-student-document").on("click",function(){var t=$(this).parent().parent().find(".student-info-json").text().trim(),e=JSON.parse(t);$("#upload-document-student-id").text(e.studentId),$(".student-details-screen").attr("style","display:none"),$("#student-list\\.upload-document-screen").attr("style","display:block"),$("#upload-document-student-name").text(e.studentBasicInfo.name),populateUploadedDocuments(e.studentDocuments)}),$("#upload-student-document-type").change(function(){$(this).find(":selected").val().trim()==OTHER_DOCUMENT_TYPE?$("#upload-student-document-name").parent().attr("style","display:block"):$("#upload-student-document-name").parent().attr("style","display:none")}),registerUploadFileCallback()}function bindStudentDocumentActions(){$(".download-student-document").on("click",function(){var t=$("#upload-document-student-id").text().trim(),e=$(this).parent().find("p.view-document-id").text().trim();window.open(baseURL+"/admission/document-download/"+t+"/"+e,"_blank")}),$(".delete-student-document").on("click",function(){var t=$(this).parent().find("p.view-document-id").text().trim();$("#student-document-delete-confirm-button").attr("onclick","deleteStudentDocument('"+t+"')"),$("#student-document-delete-confirm-modal").modal({backdrop:"static",keyboard:!1})})}function deleteStudentDocument(t){var e=$("#upload-document-student-id").text().trim();ajaxClient.post("/admission/document-delete/"+e+"/"+t,{},function(t){$("#student-document-status-modal-container").html(t),$("#student-document-delete-status-modal").modal({backdrop:"static",keyboard:!1});var e=$("#success-document-delete-response").text().trim();populateUploadedDocuments(JSON.parse(e))})}function populateUploadedDocuments(t){if(null!=t&&0!=t.length){for(var e="<br>",a=0,n=0;n<t.length;n++){var s=t[n];if("STUDENT_PROFILE_IMAGE_THUMBNAIL"!==s.documentType&&null!=s.documentId){a%3==0&&(0!=a&&(e+="</div>"),e+='<div class="row">'),a++;var i="Uploaded on : "+getFormattedDate(s.uploadTime);e+='<div class="col-sm-4"> <div class="card bg-light text-center"> <div class="card-header"> <h5> <strong> '+s.documentName+' </strong></h5> </div> <div class="card-body"> <p style="display:none;" class="view-document-id"> '+s.documentId+' </p> <p class="card-text"> Category : '+s.documentTypeDisplayName+' </p> <a href="#" class="btn btn-outline-info download-student-document">Download </a> <a href="#" class="btn btn-outline-danger delete-student-document">Delete </a> </div> <div class="card-footer text-muted"> '+i+" </div> </div> </div>"}}e+="</div> <br>",$("#student-uploaded-documents").html(e),bindStudentDocumentActions()}else $("#student-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")}function resetNewDocumentUploadPopup(){$("#upload-student-document-type").val(""),$("#upload-document-file").val(""),$("#upload-document-file-label").text(""),$("#upload-student-document-name").val(""),$("#upload-student-document-name").parent().attr("style","display:none")}async function uploadStudentDocument(){var t=$("#upload-document-student-id").text(),e=$("#upload-student-document-type option:selected").val().trim();if(""!=e){var a;if($("#upload-document-file")[0].files.length>0){var n=$("#upload-document-file")[0].files[0];if(console.log(n),a=await compressFileUtils.compress(n),console.log(a),console.log("final file size : "+a.size),a.size/1024>500)showErrorDialogBoxWithExistingModalDetails("File size exceeds 500 KB after compression. Please reduce the file size and try uploading again.","#upload-new-document-modal");else{var s="";if(e!=OTHER_DOCUMENT_TYPE||""!=(s=$("#upload-student-document-name").val())){var i=new FormData;i.append("document",a),i.append("documentType",e),i.append("documentName",s),i.append("studentId",t),$("#upload-new-document-modal").modal("toggle"),ajaxClient.uploadFile("/admission/document-upload",i,function(t){$("#student-document-status-modal-container").html(t),$("#student-document-upload-status-modal").modal({backdrop:"static",keyboard:!1});var e=$("#success-document-upload-response").text().trim();populateUploadedDocuments(JSON.parse(e))})}else showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed","#upload-new-document-modal")}}else showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload","#upload-new-document-modal")}else showErrorDialogBox("Document type field is mandatory please fill it then proceed.")}function admitStudent(){var t=$("#enroll-student-id").text().trim(),e=$("#enroll\\.student-admission-number").val().trim();if(""!==e){var a=getDate($("#enroll\\.student-admission-date").val()),n=null;null!=a&&(n=a.getTime()/1e3);var s=$("#selected-sibling-student-id").text();null!=s&&""!==s&&null!==s||(s=null);var i=$("#add-student-house").val();null!=i&&""!==i&&null!==i||(i=null);var o=[];$("tr.fee-structure-row").each(function(){if(!(!$(this).find(".custom-control-input:checkbox:checked").length>0)){var t=$(this).attr("id");o.push(t)}});var r=$("#holiday-student-template").find(":selected").val();r=""==r||null==r?null:r.trim();var d=[];$("tr.course-row").each(function(){if(!(!$(this).find(".custom-control-input:checkbox:checked").length>0)){var t=$(this).attr("id");d.push(t)}});var l=$("#send-admission-notification").is(":checked"),u=$("#send-admission-email-notification").is(":checked"),c=[];$("tr.discount-structure-row").each(function(){if(!(!$(this).find(".custom-control-input:checkbox:checked").length>0)){var t=$(this).attr("id");c.push(t)}});var p=discountStructure.dataCache.discountAssignmentStructurePayload;if(assignTransportFees.addTransportService()){var m=assignTransportFees.dataCache.transportAssignmentPayload,f={studentId:t,admissionNumber:e,admissionDate:n,houseId:i,siblingStudentId:s,feeStructureIds:o,optionalCourseIds:d,sendNotification:l,sendEmail:u,discountStructureIds:c,entityDiscountStructurePayload:p,transportAssignmentPayload:m,holidayTemplateId:r};ajaxClient.post("/admission/admit-student",{admissionDetails:JSON.stringify(f)},function(t){$("#relieve-student-confirmation-modal-container").html(t),$("#admission-status-modal").modal("toggle"),JSON.parse($("#student-data-response").text().trim()).success&&reloadPendingEnrolementStudentDetailsPage()})}}else showErrorDialogBox("Admission Number cannot be empty")}var discountStructure={dataCache:{},INSTITUTE_ENTITY:"institute",initDataCache:function(){for(var t=readJson("#fee-data"),e={},a={},n={},s=t.feeConfigurationBasicInfoList,i=t.feeHeadConfigurationResponseList,o=0;o<s.length;o++){var r=s[o];e[r.feeId]=r;var d=discountStructure.createFeeHeadSelectMenu(i,"");n[r.feeId]=d}for(o=0;o<i.length;o++){var l=i[o];a[l.feeHeadConfiguration.feeHeadId]=l}discountStructure.dataCache={},discountStructure.dataCache.feeMap=e,discountStructure.dataCache.feeHeadMap=a,discountStructure.dataCache.feeHeadConfigurationResponseList=i,discountStructure.dataCache.configureNewStructure={institute:{selectedFees:{}},standard:{selectedStandards:{},selectedFees:{}}},discountStructure.dataCache.feeIdFeeHeadSelectOptionData=n,discountStructure.dataCache.discountAssignmentStructurePayload=null},createFeeHeadSelectMenu:function(t,e){for(var a='<select class="form-control form-control-sm mandatory-field fee-head"> <option value="">select</option>',n=0;n<t.length;n++){var s=t[n].feeHeadConfiguration.feeHeadId,i=t[n].feeHeadConfiguration.feeHead;a+=e==s?'<option value="'+s+'" selected>'+i+"</option>":'<option value="'+s+'">'+i+"</option>"}return a+="</select>"},loadNewFeeStructureConfigureModal:function(){$("#discount-structure-config-modal").modal("toggle"),discountStructure.resetDiscountStructureModal(),$("#discount-structure-config-modal").find(".modal-title").html("Assign Instant Discounts"),$("#submit-fee-structure").html("Assign Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={},discountStructure.dataCache.configureNewStructure.standard.selectedStandards={},discountStructure.dataCache.configureNewStructure.standard.selectedFees={},discountStructure.dataCache.selectedStructureId=null,discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees)},resetDiscountStructureModal:function(){$("#institute-fee-structure-container").html('<p class="institute-add-structure-hint-text pb-7 pt-7"> Select fees to add new discount structure</p>'),$("#discount-structure-name").val(""),$("#discount-structure-name").attr("disabled",!1),$("#discount-structure-config-modal").find(".modal-footer").css("display","")},populateSelectFeesDropdown:function(t,e,a){var n=discountStructure.dataCache.feeMap,s="";for(feeId in n)feeId in a||(s+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+e+'">'+n[feeId].feeName+"</a>");$(t).find(".fee-select-dropdown").html(s),discountStructure.bindSelectFeesEvent(t,e,a)},bindSelectFeesEvent:function(t,e,a){$(t).find(".fee-select-option").on("click",function(e){e.stopPropagation();var n=$(this).attr("id").split("-select-")[0],s=$(this).attr("id").split("-select-")[1];discountStructure.insertFeeDetailsCard(t,s,n,a)})},bindStandardSelectEvent:function(){$(".class-fee-structure").find(".standard-select-option").on("click",function(){var t=$(this).attr("id").split("-select")[0];discountStructure.insertStandardDetailsCard(t)})},insertFeeDetailsCard:function(t,e,a,n){n[a]=!0,$(t).find(".institute-add-structure-hint-text").attr("style","display:none;");var s=discountStructure.createEntityContainerId(e),i=discountStructure.createFeeContainerId(e,a),o='<div class="fee-structure"> <div class="card card-border text-center" id="'+i+'"> <p class="fee-id" style="display:none;">'+a+'</p> <div class="card-body"> <div style="float:right;"> <button type="button" class="close delete-fee-structure-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h5 class="card-title"> <strong>'+discountStructure.dataCache.feeMap[a].feeName.toUpperCase()+'</strong> </h5> <div style="padding-left:15%; padding-right:15%;"> <table class="table table-borderless"> <thead> <tr> <th scope="col" class="w-50">Fee Head</th> <th scope="col">Amount</th> <th scope="col">Percentage</th> <th scope="col"></th> </tr> </thead> <tbody class="fee-structure-body"> </tbody> </table> <button type="button" class="btn btn-outline-secondary btn-sm add-fee-head-button"> + Add More Fee Head</button> </div> </div> </div> </br> <div>';$("#"+s).append(o),discountStructure.bindFeeContainerEvents(t,i,e,a,n),discountStructure.populateSelectFeesDropdown(t,e,n)},insertStandardDetailsCard:function(t){discountStructure.dataCache.configureNewStructure.standard.selectedStandards[t]=!0,$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");var e=discountStructure.dataCache.standardsMap[t].standardName.toUpperCase(),a=discountStructure.getStandardContainerId(t),n='<div id="'+a+'" class="card card-border standard-fee-structure"> <p class="standard-id" style="display:none;">'+t+'</p> <div class="card-header card-header-color" id="'+t+'-heading" data-toggle="collapse" data-target="#collapse-'+t+'" aria-expanded="true" aria-controls="collapse-'+t+'"> <div style="float:right;"> <button type="button" class="close delete-standard-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </div> <h2 class="mb-0"> <button class="btn btn-link" type="button"> <strong>'+e+'</strong> </button> </h2> </div> <div id="collapse-'+t+'" class="collapse" aria-labelledby="'+t+'-heading" data-parent="#class-fee-structure-config-accordion"> <div class="card-body"> <div id="'+t+'-fee-structure-container" style="text-align:center;"> <p class="institute-add-structure-hint-text black-color"> Select fees to add new fee structure</p> \x3c!-- Next card here --\x3e </div> <br /> <div class="fee-select-dropdown-container" style="float:right;"> <div class="btn-group dropup" style="width:150px;"> <button type="button" class="btn btn-info btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Select Fees </button> <div class="dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right"> </div> </div> </div> <br/> </div> </div> </div>';$("#class-fee-structure-config-accordion").append(n),discountStructure.dataCache.configureNewStructure.standard.selectedFees[t]={},discountStructure.populateSelectFeesDropdown("#"+a,t,discountStructure.dataCache.configureNewStructure.standard.selectedFees[t])},createEntityContainerId:function(t){return t+"-fee-structure-container"},createFeeContainerId:function(t,e){return t+"-"+e},bindFeeContainerEvents:function(t,e,a,n,s){$("#"+e).find(".add-fee-head-button").on("click",function(){discountStructure.insertFeeHeadRow(e,n,null,0,"")}),$("#"+e).find(".delete-fee-structure-row").on("click",function(){$(this).closest(".fee-structure").remove(),delete s[n],isEmpty(s)&&$(t).find(".institute-add-structure-hint-text").attr("style","display:block;"),discountStructure.populateSelectFeesDropdown(t,a,s)})},populateSelectFeesDropdown:function(t,e,a){var n=discountStructure.dataCache.feeMap,s="";for(feeId in n)feeId in a||(s+='<a class="dropdown-item fee-select-option" href="#" id="'+feeId+"-select-"+e+'">'+n[feeId].feeName+"</a>");$(t).find(".fee-select-dropdown").html(s),discountStructure.bindSelectFeesEvent(t,e,a)},getStandardContainerId:function(t){return t+"-container"},bindStandardContainerEvents:function(t,e){$("#"+t).find(".delete-standard-row").on("click",function(){$(this).closest(".standard-fee-structure").remove(),delete discountStructure.dataCache.configureNewStructure.standard.selectedStandards[e],isEmpty(discountStructure.dataCache.configureNewStructure.standard.selectedStandards)&&$("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;")})},insertFeeHeadRow:function(t,e,a,n,s){var i="";if(null==a){i='<tr id=""> <td> '+discountStructure.dataCache.feeIdFeeHeadSelectOptionData[e]+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..."> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"> <span class="slider round"></span> </label> </td><td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}else{var o=discountStructure.dataCache.feeHeadConfigurationResponseList;i='<tr id=""> <td> '+discountStructure.createFeeHeadSelectMenu(o,a)+' </td> <td> <input type="number" class="form-control form-control-sm fee-head-amount" aria-describedby="" placeholder="Enter amount..." value="'+n+'"> </td> <td> <label class="switch"> <input type="checkbox" class="primary toggle-switch is-percentage-switch"'+(s=s?"checked":"")+' /><span class="slider round"></span> </label> </td> <td> <button type="button" class="close delete-fee-head-row" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> </td> </tr>'}$("#"+t).find(".fee-structure-body").append(i),discountStructure.bindFeeHeadRowEvents(t)},bindFeeHeadRowEvents:function(t){var e="";$("#"+t).find(".fee-head").on("focus",function(){e=$(this).find(":selected").val().trim()}).change(function(){var t=$(this).find(":selected").val().trim();if(""!=t){var a=0;$(this).closest("tbody.fee-structure-body").find("tr").each(function(){var e=$(this).find(".fee-head").find(":selected").val().trim();""!=e&&t==e&&a++}),a>1&&(showErrorDialogBox("Fee Head "+$(this).find(":selected").text()+" already selected. Please fill amount in that"),$(this).val(e))}}),$("#"+t).find(".delete-fee-head-row").on("click",function(){$(this).closest("tr").remove()}),$("#"+t).find(".fee-head-amount").change(function(){if($(this).parent().parent().find(".is-percentage-switch").is(":checked")){var t=$(this).val();if(t>100||t<=0)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).val("")}else{var e=$(this).val();if(e<=0&&""!=e)return showErrorDialogBox("Amount should be a positive number."),void $(this).val("")}}),$("#"+t).find(".is-percentage-switch").change(function(){if($(this).is(":checked")){var t=$(this).parent().parent().parent().find(".fee-head-amount").val();if((t>100||t<=0)&&""!=t)return showErrorDialogBox("Percentage should be between 1 to 100."),void $(this).parent().parent().parent().find(".fee-head-amount").val("")}})},submitDiscountStructure:function(){var t=$("#discount-structure-name").val();if(""!=t){$("#academic-session-id").text().trim();var e=null;null!=discountStructure.dataCache.selectedStructureId&&""!=discountStructure.dataCache.selectedStructureId.trim()&&(e=discountStructure.dataCache.selectedStructureId);var a=discountStructure.getFeeStructureData("#institute-fee-structure-container");if(!Array.isArray(a)||a.length){var n={feeEntity:"INSTITUTE",feeIdFeeHeadsList:a},s=[];s.push(n);var i={structureId:e,discountStructureType:"SYSTEM",metadata:{title:t},entityFeeAssignmentPayloads:s};$("#discount-structure-config-modal").modal("toggle"),discountStructure.dataCache.discountAssignmentStructurePayload=i,discountStructure.createDiscountStructureTable()}}else alert("Discount Detail is mandatory!")},createDiscountStructureTable:function(){var t=discountStructure.dataCache.discountAssignmentStructurePayload,e='<div class="card-header" style="padding-bottom:0px;padding-left:0px;"><br><h5 style="padding-bottom:0px;color:#43a2ad;cursor:pointer;" onclick="discountStructure.loadNewFeeStructureConfigureModal();"><u>Assign Instant Discounts</u></h5></div>';if(null!=t&&null!=t)e='<br/><div class="card-header" style="padding-bottom:0px;padding-left:0px;"><br><h5>Instant Discount Details</h5></div><table id="datatables-reponsive" class="table table-striped datatables-reponsive-table table-bordered"><thead><tr><th scope="col" class="w-50">Structure Name</th><th scope="col" colspan="3">Action</th></tr></thead><tbody><tr><td id="">'+t.metadata.title+'</td><td><button type="button" class="btn btn-outline-primary btn-sm view-fee-structure-config-button" data-target="#discount-structure-config-modal" data-keyboard="false" data-toggle="modal" data-backdrop="static" onclick="discountStructure.fillFeeDiscountStructureModal(true)">View</button></td><td><button type="button" class="btn btn-outline-warning btn-sm update-fee-structure-config-button" data-target="#discount-structure-config-modal" data-keyboard="false" data-toggle="modal" data-backdrop="static" onclick="discountStructure.fillFeeDiscountStructureModal(false)">Update</button></td><td><button type="button" class="btn btn-outline-danger btn-sm delete-fee-structure-config-button" data-target="#delete-fee-structure-modal" data-keyboard="false" data-toggle="modal" data-backdrop="static" onclick="discountStructure.populateDeleteStructureModal()">Delete</button></td></tr></tbody></table>';$("#assign-instant-discounts-div").html(e)},getFeeStructureData:function(t){var e=[];return $(t).find(".fee-structure").each(function(){var t=$(this).find(".fee-id").text().trim(),a=[],n={};$(this).find("tbody.fee-structure-body").find("tr").each(function(){var t=$(this).find(".fee-head").find(":selected").val().trim();if(""!=t&&!(t in n)){var e=$(this).find(".fee-head-amount").val(),s=$(this).find(".toggle-switch").is(":checked");if(""===e||void 0===e)return alert("Amount cannot be empty"),[];a.push({feeHeadId:t,amount:e,isPercentage:s})}}),a.length>0&&e.push({feeId:t,feeHeadAmountList:a})}),Array.isArray(e)&&!e.length?(alert("Please select atleast one fees to create discount structure"),[]):e},fillFeeDiscountStructureModal:function(t){$("#discount-structure-name").attr("disabled",!1);var e=discountStructure.dataCache.discountAssignmentStructurePayload;discountStructure.resetDiscountStructureModal(),$("#discount-structure-name").val(e.metadata.title),$("#discount-structure-config-modal").find(".modal-title").html("Update Instant Discounts"),$("#submit-discount-structure").html("Update Instant Discounts"),$(".standard-select-dropdown-container").css("display","block"),$(".fee-select-dropdown-container").css("display","block"),discountStructure.dataCache.configureNewStructure.institute.selectedFees={};for(var a={},n=0;n<e.entityFeeAssignmentPayloads.length;n++)for(var s=e.entityFeeAssignmentPayloads[n],i=0;i<s.feeIdFeeHeadsList.length;i++){var o=s.feeIdFeeHeadsList[i],r=o.feeId;a[r]=o}for(r in a){discountStructure.insertFeeDetailsCard(".institute-structure",discountStructure.INSTITUTE_ENTITY,r,discountStructure.dataCache.configureNewStructure.institute.selectedFees);var d=discountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,r);for(n=0;n<a[r].feeHeadAmountList.length;n++){var l=a[r].feeHeadAmountList[n].feeHeadId,u=a[r].feeHeadAmountList[n].amount,c=a[r].feeHeadAmountList[n].isPercentage;discountStructure.insertFeeHeadRow(d,r,l,u,c)}}discountStructure.populateSelectFeesDropdown(".institute-structure",discountStructure.INSTITUTE_ENTITY,discountStructure.dataCache.configureNewStructure.institute.selectedFees),t&&($(".standard-select-dropdown-container").css("display","none"),$(".fee-select-dropdown-container").css("display","none"),$(".add-fee-head-button").css("display","none"),$(".fee-head").attr("disabled","true"),$(".fee-head-amount").attr("disabled","true"),$(".toggle-switch").attr("disabled","true"),$(".delete-fee-head-row").remove(),$(".delete-fee-structure-row").remove(),$(".delete-standard-row").remove(),$("#discount-structure-config-modal").find(".modal-title").html("View Instant Discounts"),$("#discount-structure-name").attr("disabled",!0),$("#fee-structure-types").attr("disabled",!0),$("#discount-structure-config-modal").find(".modal-footer").css("display","none"))},populateDeleteStructureModal:function(t){$("#delete-fee-structure-modal-text").html("Do you want to delete instant discount: "+discountStructure.dataCache.discountAssignmentStructurePayload.metadata.title+"?")},deleteFeeStructure:function(){$("#delete-fee-structure-modal").modal("toggle"),discountStructure.dataCache.discountAssignmentStructurePayload=null,discountStructure.createDiscountStructureTable()}},studentList={dataCache:{},initPagination:function(){pagination.bindEvents(function(){searchStudents(!1)},function(){searchStudents(!1)},function(){searchStudents(!1)},function(){searchStudents(!0)})}},documentPage={dataCache:{},generateDocument:function(){var t=$("#generate-student-document-type").find(":selected").val().trim(),e=$("#upload-document-student-id").text(),a=academicSessionHandler.getSelectedSessionId();window.open(baseURL+"/admission/generate-document/"+a+"/"+e+"/"+t,"_blank")},generateStaticAdmissionForm:function(){var t=$("#add-student-academic-session").find(":selected").val().trim();window.open(baseURL+"/admission/generate-static-document/"+t+"/ADMISSION_FORM","_blank")}},identityCards={dataCache:{},loadMainScreen:function(){ajaxClient.get("/admission/identitycard",function(t){$("#main-content").html(t),commonUtils.bindCardHoverEvent(),commonUtils.bindCardClickEvent(".identitycard-item"),identityCards.bindGenerateIdentityCardEvent()})},bindGenerateIdentityCardEvent:function(){$(".generate-identitycard").on("click",function(){$(this).closest("div.modal").modal("toggle");var t=$(this).closest("div.identitycard-field-container"),e=$(t).find(".identitycard-academic-session option:selected").val(),a=$(t).find(".identitycard-standards option:selected").val();""!=e&&""!=a?window.open(baseURL+"/admission/generate-identitycard/"+e+"/"+a,"_blank"):showErrorDialogBox("Please select all the required fields to generate admit card")})}},siblingDetails={loadHomePage:function(){ajaxClient.get("/admission/siblings?status=ENROLMENT_PENDING,ENROLLED,NSO",function(t){$("#main-content").html(t),siblingDetails.bindSearchStudentEvent(),siblingDetails.registerStudentSearchCallback(),academicSessionHandler.bindSessionChangeEvent(siblingDetails.changeSession),initSelect2("None")})},changeSession:function(){siblingDetails.loadSiblingList()},registerStudentSearchCallback:function(){$("#searchStudents").on("click",function(){siblingDetails.loadSiblingList()}),$("#searchStudentsInput").on("keyup",function(t){13==t.keyCode&&siblingDetails.loadSiblingList()})},loadSiblingList:function(){var t=$("#searchStudentsInput").val(),e=academicSessionHandler.getSelectedSessionId(),a="ENROLMENT_PENDING,ENROLLED,NSO";$(".student-status").val().length>0?(a=$(".student-status").val().join(),ajaxClient.get("/admission/siblings-list/"+e+"?text="+t+"&status="+a,function(t){$("#sibling-list-div").html(t)})):showErrorDialogBox("Please select atleast one student status to search students!")},bindSearchStudentEvent:function(){$("#students-search").on("keyup",function(t){13==t.keyCode&&siblingDetails.doneStudentSearchTyping("#students-search-result")}),liveSearchHandler.bindEvent("#students-search","#students-search-result",siblingDetails.doneStudentSearchTyping)},doneStudentSearchTyping:function(t){var e=$("#students-search").val().trim();siblingDetails.studentLiveSearchEvent(e,t,siblingDetails.loadStudentDetails,"ENROLLED")},studentLiveSearchEvent:function(t,e,a,n){var s=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/admission/student-live-search/"+s+"?searchText="+t+"&status="+n,function(t){$(e).html(t),siblingDetails.loadStudentDetails()})},loadStudentDetails:function(){$("#live-search-student-results tbody tr td").on("click",function(){var t=JSON.parse($(this).parent().find("td.student-info-td").text().trim());siblingDetails.addSelectedStudentRow(t,!0,!1,!0)})},addSelectedStudentRow:function(t,e,a,n){var s=t.name,i=t.admissionNumber,o=siblingDetails.checkIfStringEmpty(t.fathersName)?"-":t.fathersName,r=siblingDetails.checkIfStringEmpty(t.mothersName)?"-":t.mothersName,d=siblingDetails.checkIfStringEmpty(t.gender)?"-":t.gender,l=t.studentId,u=($(this).parent(),"");e&&(u=' <button type="button" class="close delete-student-row " aria-label="Close"> <span aria-hidden="true">&times;</span> </button>');var c="";a&&(c=' <input type="checkbox" class="delete-student-checkbox" name="" value=""> &nbsp; &nbsp;');var p='<tr class="selected_student_row '+(n?"add":"")+'" id="'+l+'"><td class="student-name-td" scope="row"> '+c+s+' </td><td class="student-admission-number-td" scope="row"> '+i+' </td><td class="student-gender-td" scope="row"> '+d+' </td><td class="student-father-name-td" scope="row"> '+o+' </td><td class="student-mother-name-td" scope="row"> '+r+" </td><td>"+u+"</td> </tr>";$("#students-search-row").before(p),$("#students-search-result").html(""),$("#students-search").val(""),siblingDetails.deletePurchaseItemEntry()},deletePurchaseItemEntry:function(){$(".delete-student-row").click(function(){$(this).parent().parent().remove()})},checkIfStringEmpty:function(t){return null==t||!t.trim()},editSiblingDetails:function(t,e){var a=[],n=!1,s=!1;if(t){if($(".selected_student_row").find("input.delete-student-checkbox").each(function(){if($(this).is(":checked")){var t=$(this).parent().parent().attr("id");null!=t&&null!=t&&a.push(t)}}),n)return;if(a.length<=0)return void alert("Please select atleast one student to remove!")}else if(e){if($(".selected_student_row").each(function(){var t=$(this).attr("id");if(siblingDetails.checkIfStudentAlreadySelected(t,a))return alert("Please remove duplicate entries from student list!"),void(n=!0);a.push(t)}),n)return;if(a.length<=1)return void alert("Please select atleast two student for sibling information!")}else{if($(".selected_student_row.add").each(function(){var t=$(this).attr("id");if(siblingDetails.checkIfStudentAlreadySelected(t,a))return alert("Please remove duplicate entries from student list!"),void(n=!0);a.push(t)}),n)return;if(a.length<=0)return void alert("Please select atleast a student to add!")}var i="add-siblings",o=null;t&&(i="update-siblings"),!t&&e||(o=$("#sibling-group-id").text(),s=!0);var r={instituteId:null,siblingGroupId:o,studentIdList:a};console.log(r),$("#sibling-config-modal").modal("toggle"),ajaxClient.post("/admission/"+i+"/"+s,{studentSiblingPayload:JSON.stringify(r)},function(t){$("#siblings-status-modal-container").html(t),$("#admission-status-modal").modal("toggle"),siblingDetails.loadSiblingList()})},checkIfStudentAlreadySelected:function(t,e){return e.indexOf(t)>-1},viewSiblingDetailsButton:function(t,e){$("#sibling-config-modal").modal("toggle"),siblingDetails.fillSiblingInformation(!0,t,e)},fillSiblingInformation:function(t,e,a){siblingDetails.resetSibligDetailsPopup();var n=JSON.parse($(e).parent().find(".sibling-details-info").text().trim());$("#sibling-group-id").text(n.siblingGroupId);var s=n.studentList,i=a===DELETE_SIBLINGS;$.each(s,function(t,e){siblingDetails.addSelectedStudentRow(e,!1,i,!1)}),t?($("#students-search-row").attr("style","display:none;"),$("#sibling-details-modal-footer").html('<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="closeModal()">Close</button>')):a===DELETE_SIBLINGS?($("#students-search-row").attr("style","display:none;"),$("#sibling-details-modal-footer").html('<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="closeModal()">Cancel</button><button type="button" class="btn btn-primary" onclick="siblingDetails.editSiblingDetails(true, false);">Delete Siblings</button>')):a===ADD_SIBLINGS&&($("#students-search-row").attr("style",""),$("#sibling-details-modal-footer").html('<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="closeModal()">Cancel</button><button type="button" class="btn btn-primary" onclick="siblingDetails.editSiblingDetails(false, false);">Add Siblings</button>'))},updateSiblingDetailsButton:function(t,e){$("#sibling-config-modal").modal("toggle"),siblingDetails.fillSiblingInformation(!1,t,e)},resetSibligDetailsPopup:function(){$("#sibling-details-modal-body").html('<p id="sibling-group-id" style="display:none;"></p><table id="datatables-reponsive" class="table datatables-reponsive-table"><thead><tr><th scope="col">Student Name*</th><th scope="col">Admission Number</th><th scope="col">Gender</th><th scope="col">Father Name</th><th scope="col">Mother Name</th><th scope="col"></th></tr></thead><tbody><tr id="students-search-row"><td width="30%"><input type="text" class="form-control" id="students-search" placeholder="Type Student Name to add . . ."><div id="students-search-result"></div></td><td><p></p></td><td><p></p></td><td><p></p></td><td><p></p></td></tr></tbody></table>');$("#sibling-details-modal-footer").html('<button type="button" class="btn btn-danger" data-dismiss="modal" onclick="closeModal()">Cancel</button><button type="button" class="btn btn-primary" onclick="siblingDetails.editSiblingDetails(false, true);">Add Siblings Details</button>'),siblingDetails.bindSearchStudentEvent()},deleteSiblingDetailPopup:function(t){$("#delete-sibling-modal").modal("toggle");$("#delete-sibling-details-modal-body").html('<p id="delete-sibling-group-id" style="display:none;"></p><span style="color:red" id="delete-siblings-modal-text">Do you want to Delete Sibling Information?</span>');$("#delete-sibling-details-modal-footer").html('<button type="button" class="btn btn-success" data-dismiss="modal">Do Not Delete</button><button type="button" class="btn btn-danger" onclick="siblingDetails.deleteSiblingDetails();">Yes, Delete Sibling Information</button>');var e=JSON.parse($(t).parent().find(".sibling-details-info").text().trim());$("#delete-sibling-group-id").text(e.siblingGroupId)},deleteSiblingDetails:function(){var t=$("#delete-sibling-group-id").text();$("#delete-sibling-modal").modal("toggle"),ajaxClient.post("/admission/delete-sibling-details/"+t,{},function(t){$("#siblings-status-modal-container").html(t),$("#admission-status-modal").modal("toggle"),siblingDetails.loadSiblingList()})}};