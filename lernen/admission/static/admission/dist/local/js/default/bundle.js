var regexEmail = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
var regexPhoneNumber = /^\d{10}$/;
var regexAadharNumber = /^\d{12}$/;
var regexAge = /^(0?[1-9]|[1-9][0-9]|[1][0-1][0-9]|120)$/;
// var STUDENT_IMAGE_SIZE_LIMIT = 500;

function loadAdmitStudentMenu() {
    ajaxClient.get("/admission/add-student-view", function(data) {
      $("#main-content").html(data);
      admission.bindSessionChangeEvent();
      bindAdmissionClassSectionEvent();
      bindRemoveErrorDisplayEvent();
      initDate(365*30);
      previousTabSwtichingEvent();
      nextTabSwtichingEvent();
      enforceConstraints();
      permanentAddressUpdate("permanent-same-as-present",
      "student-permanent-address", "student-city", "student-state", "student-post-office", "student-police-station", "student-zipcode",
      "student-present-address", "student-present-city", "student-present-state", "student-present-post-office", "student-present-police-station", "student-present-zipcode");
      $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
      });
    });
}

var admission = {

  dataCache : {},

  bindSessionChangeEvent : function () {
    $("#add-student-academic-session").change(function() {
        var admissionAcademicSessionId = $(this).find(':selected').val().trim();
        ajaxClient.get("/admission/standards/"+admissionAcademicSessionId, function(data) {
          $(".student-standard-json-wrapper").html(data);
          var standardId = $("#add-student-class").find(':selected').val().trim();
          fillStudentSections(standardId);
          admission.updateInstituteHouses();
        });
    });
  },
  updateInstituteHouses : function () {
    var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
    ajaxClient.get("/admission/institute-houses/"+admissionAcademicSessionId, function(data) {
      $("#institute-houses-info-button").html(data);
      $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
      });
    });
  },
}

function bindAdmissionClassSectionEvent() {
  $(".student-class").change(function() {
      var classId = $(this).find(':selected').val().trim();
      fillStudentSections(classId);
  });
}

function loadCreateStudentConfirmModal() {
  var invalid = validateMandatoryFields($("#add\\.basic-info-content"));
  if(invalid){
    showErrorDialogBox("Invalid student information. Please try again.")
    return;
  }
  var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
  var standardId = $("#add-student-class").find(':selected').val().trim();
  ajaxClient.get("/admission/default-fee-assignment-structure/"+admissionAcademicSessionId+"/"+standardId, function(data) {
    $("#create-student-confirm-modal-container").html(data);
    $("#create-student-confirm-modal").modal({backdrop: 'static', keyboard: false});
    // var myModal = new bootstrap.Modal(document.getElementById('create-student-confirm-modal'), {
    //   keyboard: false,
    //   backdrop: 'static'
    // })
    // // var modal = bootstrap.Modal.getInstance($("#create-student-confirm-modal"));
    // myModal.show();
    // $("#create-student-confirm-modal").show();
  });
}

function createStudent() {
    $("#create-student-confirm-modal").modal('toggle');
    var invalid = validateMandatoryFields($("#add\\.basic-info-content"));
    if(invalid){
      showErrorDialogBox("Invalid student information. Please try again.")
      return;
    }

    var documentName = "";
    var file = "";
    // var ONE_KB = 1024;
    // if (($("#student-photo"))[0].files.length > 0) {
    //   file = ($("#student-photo"))[0].files[0];
    //     if((file.size / ONE_KB) > STUDENT_IMAGE_SIZE_LIMIT){
    //       showErrorDialogBox("Size Of document cannot be greater than "+STUDENT_IMAGE_SIZE_LIMIT+" kb");
    //       return;
    //     }
    // }

    var admissionAcademicSessionId = $("#add-student-academic-session").find(':selected').val().trim();
    var standardId = $("#add-student-class").find(':selected').val().trim();
    var sectionId = $("#add-student-section").find(':selected').val().trim();
    if(sectionId == ""){
      sectionId = null;
    }
    var studentGuardianInfoList = [];
    var studentBasicInfo = getStudentBasicInfo();
    if(studentBasicInfo === undefined) {
      return;
    }
    console.log(studentBasicInfo);
    var studentFamilyInfo = null;
    if(studentFamilyInfo === undefined) {
      return;
    }
    var studentGuardianInfo = null;
    if(studentGuardianInfo === undefined) {
      return;
    }
    var studentPreviousSchoolInfo = null;
    if(studentPreviousSchoolInfo === undefined) {
      return;
    }
    var studentMedicalInfo = null;

    var applicableFeeStructures = [];
    $("#applicable-registration-fees").find("input.applicable-fee-structure-assign-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var feeStructureId = $(this).attr("id");
        applicableFeeStructures.push(feeStructureId);
    });
    // var studentFamilyInfo = getStudentFamilyInfo();
    // var studentGuardianInfo = getStudentGuardianInfo();
    // var studentPreviousSchoolInfo = getStudentPreviousSchoolInfo();
    // var studentMedicalInfo = getStudentMedicalInfo();
    // studentGuardianInfoList.push(studentGuardianInfo);
    var newAdmission = $("#student-new-admission").is(":checked");
    var addStudentPayload = {'admissionAcademicSession' : admissionAcademicSessionId, 'standardId':standardId, 'sectionId':sectionId,
     'studentBasicInfo': studentBasicInfo, 'studentFamilyInfo': studentFamilyInfo, 'studentGuardianInfoList': studentGuardianInfoList,
     'studentPreviousSchoolInfo':studentPreviousSchoolInfo, 'studentMedicalInfo':studentMedicalInfo, "newAdmission" : newAdmission};

     var registerStudentPayload = {'studentPayload':addStudentPayload, 'feeStructureIds' : applicableFeeStructures}

     var formData = new FormData();
     if(file != "") {
       formData.append('document', file);
       formData.append('documentName', documentName);
     }

     formData.append('registerStudentPayload', JSON.stringify(registerStudentPayload));
     ajaxClient.uploadFile("/admission/add-new-student", formData, function(data) {
         $("#admission-status-modal-container").html(data);
         var response = JSON.parse($("#student-data-response").text().trim());
         if(response.success){
           fillStudentInformation(response.student);
           // bindAdmissionClassSectionEvent();
           $("#update-student-family-info").trigger("click");
           $('.add-student-screen').attr('style','display:none');
           $('#add\\.update-student-screen').attr('style','display:block');
           $('#screen-name').text('ADMIT-STUDENT');
         }
         enforceConstraints();
         $("#admission-status-modal").modal('toggle');
     });
}

function getStudentBasicInfo(){
  var registrationNumber = $("#student-registration-number").val().trim();
  var admissionDate = getDate($("#student-admission-date").val());
  var registrationDate = getDate($("#student-registration-date").val());
  var name = $("#student-name").val().trim();
  var gender = $("#student-gender").val().trim();
  if(gender == ""){
    gender = null;
  }
  var dob = getDate($("#student-date-of-birth").val());
  var birthPlace = $("#student-birth-place").val().trim();
  var userCategory = $("#student-category").val().trim();
  if(userCategory == ""){
    userCategory = null;
  }
  var religion = $("#student-religion").val().trim();
  var caste = $("#student-caste").val().trim();
  var isRTE = $("#student-rte").is(":checked");
  var motherTongue = $("#student-mother-tongue").val().trim();
  var areaType = $("#student-area-type").val().trim();
  if(areaType == ""){
    areaType = null;
  }
  var speciallyAbled = $("#student-specially-abled").is(":checked");
  var bpl = $("#student-bpl").is(":checked");

  var presentAddress = $("#student-present-address").val().trim();
  var presentCity = $("#student-present-city").val().trim();
  var presentState = $("#student-present-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#student-present-state").find(".country-states").find(':selected').val().trim();
  var presentPostOffice = $("#student-present-post-office").val().trim();
  var presentPoliceStation = $("#student-present-police-station").val().trim();
  var presentZipcode = $("#student-present-zipcode").val().trim();


  var permanentAddress = $("#student-permanent-address").val().trim();
  var city = $("#student-city").val().trim();
  var state = $("#student-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#student-state").find(".country-states").find(':selected').val().trim();
  var postOffice = $("#student-post-office").val().trim();
  var policeStation = $("#student-police-station").val().trim();
  var zipcode = $("#student-zipcode").val().trim();


  var registrationDateInt = null;
  if(registrationDate != null){
    registrationDateInt = registrationDate.getTime()/1000;
  }

  var admissionDateInt = null;
  if(admissionDate != null){
    admissionDateInt = admissionDate.getTime()/1000;
  }
  var dobInt = null;
  if(dob != null){
    dobInt = dob.getTime()/1000;
  }


  var isError = false;
  var impactedElement = null;
  var aadharNumber = $("#student-aadhar-number").val().trim();
  if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
    isError = true;
    impactedElement = "student-aadhar-number";
    string = "Invalid Aadhar Number";
  }

  var nationality = $("#student-nationality").val().trim();

  var primaryContactNumber = $("#student-primary-contact-number").val().trim();
  if(primaryContactNumber != "" && !regexPhoneNumber.test(primaryContactNumber)) {
    isError = true;
    impactedElement = "student-primary-contact-number";
    string = "Primary Contact Number should be of 10 digits.";
  }

  var primaryEmail = $("#student-primary-email").val().trim();
  if(primaryEmail != "" && !regexEmail.test(primaryEmail)) {
    isError = true;
    impactedElement = "student-primary-email";
    string = "Type correct format of Primary Email";
  }

  var whatsappNumber = $("#student-whatsapp-number").val().trim();
  if(whatsappNumber != "" && !regexPhoneNumber.test(whatsappNumber)) {
    isError = true;
    impactedElement = "student-whatsapp-number";
    string = "Whatsapp Number should be of 10 digits.";
  }

  var isSponsored = $("#student-is-sponsored").is(":checked");
  var hosteller = $("#student-hosteller").is(":checked");

  if(isError) {
    if(impactedElement != null) {
      $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }

  // var instituteHouseId = $("#add-student-house").val();
  // if(instituteHouseId == undefined || instituteHouseId === "") {
  //   instituteHouseId = null;
  // }
  var instituteHouseId = null;
  var admissionInClass = $("#add-student-admission-in-class").val().trim();

  var studentNameAsPerAadhar = $("#add-student-name-as-per-aadhar").val().trim();

  var childCategoryCriteria = $("#add-child-category-criteria").val().trim();
  if(childCategoryCriteria == ""){
    childCategoryCriteria = null;
  }

  var speciallyAbledType = $("#add-specially-abled-type").val().trim();

  var penNumber = $("#add-pen-number").val().trim();

  var apaarIdNo = $("#add-apaar-id-number").val().trim();

  var studentBasicInfo = {'registrationRequestNumber' : registrationNumber + "-" + Math.random().toString(36),
     'registrationNumber' : registrationNumber, 'registrationDate' : registrationDateInt, 'admissionDate' : admissionDateInt,
     'name': name, 'gender' :gender, 'dateOfBirth' : dobInt, 'birthPlace' :  birthPlace, 'aadharNumber' : aadharNumber,
     'userCategory' : userCategory, 'religion' : religion, 'caste' : caste, 'rte' : isRTE, 'motherTongue' : motherTongue,
     'areaType' : areaType, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl, 'presentAddress' : presentAddress,
     'presentCity' : presentCity, 'presentState' : presentState, 'presentPostOffice' : presentPostOffice,
     'presentPoliceStation' : presentPoliceStation, 'presentZipcode' : presentZipcode, 'permanentAddress' : permanentAddress,
     'permanentCity' : city, 'permanentState' : state, 'permanentPostOffice' : postOffice,
     'permanentPoliceStation' : policeStation, 'permanentZipcode' : zipcode, 'nationality' : nationality,
     'primaryContactNumber' : primaryContactNumber, 'primaryEmail' : primaryEmail, 'isSponsored' : isSponsored, 'hosteller' : hosteller,
     'whatsappNumber' : whatsappNumber, 'instituteHouseId' : instituteHouseId, 'admissionInClass' : admissionInClass,
     'speciallyAbledType' : speciallyAbledType, 'studentNameAsPerAadhar' : studentNameAsPerAadhar, 'childCategoryCriteria' : childCategoryCriteria,
     'penNumber' : penNumber, 'apaarIdNo' : apaarIdNo};
  return studentBasicInfo;
}

function getStudentBasicInfoUpdate(){
  var registrationNumber = $("#update\\.student-registration-number").val().trim();
  var admissionNumber = $("#update\\.student-admission-number").val().trim();
  var penNumber = $("#update\\.pen-number").val().trim();
  var apaarIdNo = $("#update\\.apaar-id-number").val().trim();
  var admissionDate = getDate($("#update\\.student-admission-date").val());
  var registrationDate = getDate($("#update\\.student-registration-date").val());
  var name = $("#update\\.student-name").val().trim();
  var gender = $("#update\\.student-gender").val().trim();
  if(gender == ""){
    gender = null;
  }
  var dob = getDate($("#update\\.student-date-of-birth").val());
  var birthPlace = $("#update\\.student-birth-place").val().trim();
  var userCategory = $("#update\\.student-category").val().trim();
  if(userCategory == ""){
    userCategory = null;
  }
  var religion = $("#update\\.student-religion").val().trim();
  var caste = $("#update\\.student-caste").val().trim();
  var isRTE = $("#update\\.student-rte").is(":checked");
  var motherTongue = $("#update\\.student-mother-tongue").val().trim();
  var areaType = $("#update\\.student-area-type").val().trim();
  if(areaType == ""){
    areaType = null;
  }
  var speciallyAbled = $("#update\\.student-specially-abled").is(":checked");
  var bpl = $("#update\\.student-bpl").is(":checked");

  var presentAddress = $("#update\\.student-present-address").val().trim();
  var presentCity = $("#update\\.student-present-city").val().trim();
  var presentState = $("#update\\.student-present-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#update\\.student-present-state").find(".country-states").find(':selected').val().trim();
  var presentPostOffice = $("#update\\.student-present-post-office").val().trim();
  var presentPoliceStation = $("#update\\.student-present-police-station").val().trim();
  var presentZipcode = $("#update\\.student-present-zipcode").val().trim();


  var permanentAddress = $("#update\\.student-permanent-address").val().trim();
  var city = $("#update\\.student-city").val().trim();
  var state = $("#update\\.student-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#update\\.student-state").find(".country-states").find(':selected').val().trim();
  var postOffice = $("#update\\.student-post-office").val().trim();
  var policeStation = $("#update\\.student-police-station").val().trim();
  var zipcode = $("#update\\.student-zipcode").val().trim();

  var registrationDateInt = null;
  if(registrationDate != null){
    registrationDateInt = registrationDate.getTime()/1000;
  }
  var admissionDateInt = null;
  if(admissionDate != null){
    admissionDateInt = admissionDate.getTime()/1000;
  }
  var dobInt = null;
  if(dob != null){
    dobInt = dob.getTime()/1000;
  }

  var isError = false;
  var impactedElement = null;
  var aadharNumber = $("#update\\.student-aadhar-number").val().trim();
  if(aadharNumber != "" && !regexAadharNumber.test(aadharNumber)) {
    isError = true;
    impactedElement = "student-aadhar-number";
    string = "Invalid Aadhar Number";
  }

  var nationality = $("#update\\.student-nationality").val().trim();

  var primaryContactNumber = $("#update\\.student-primary-contact-number").val().trim();
  if(primaryContactNumber != "" && !regexPhoneNumber.test(primaryContactNumber)) {
    isError = true;
    impactedElement = "student-primary-contact-number";
    string = "Primary Contact Number should be of 10 digits.";
  }

  var primaryEmail = $("#update\\.student-primary-email").val().trim();
  if(primaryEmail != "" && !regexEmail.test(primaryEmail)) {
    isError = true;
    impactedElement = "student-primary-email";
    string = "Type correct format of Primary Email";
  }

  var whatsappNumber = $("#update\\.student-whatsapp-number").val().trim();
  if(whatsappNumber != "" && !regexPhoneNumber.test(whatsappNumber)) {
    isError = true;
    impactedElement = "student-whatsapp-number";
    string = "Whatsapp Number should be of 10 digits.";
  }

  var isSponsored = $("#update\\.student-is-sponsored").is(":checked");
  var hosteller = $("#update\\.student-hosteller").is(":checked");

  if(isError) {
    if(impactedElement != null) {
      $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }


  var instituteHouseId = $("#update\\.student-house").val();
  if(instituteHouseId == undefined || instituteHouseId === "") {
    instituteHouseId = null;
  }
  var admissionInClass = $("#update\\.student-admission-in-class").val().trim();

  var studentNameAsPerAadhar = $("#update\\.student-name-as-per-aadhar").val().trim();

  var childCategoryCriteria = $("#update\\.child-category-criteria").val();
  if(childCategoryCriteria == ""){
    childCategoryCriteria = null;
  } else {
    childCategoryCriteria = childCategoryCriteria.trim();
  }

  var speciallyAbledType = $("#update\\.specially-abled-type").val().trim();

  var studentBasicInfo = {'registrationNumber' : registrationNumber, 'admissionNumber' : admissionNumber, 'penNumber' : penNumber, 'apaarIdNo' : apaarIdNo, 'registrationDate' : registrationDateInt, 'admissionDate' : admissionDateInt, 'name': name,
   'gender' :gender, 'dateOfBirth' : dobInt, 'birthPlace' :  birthPlace,
   'aadharNumber' : aadharNumber, 'userCategory' : userCategory, 'religion' : religion, 'caste' : caste, 'rte' : isRTE,
   'motherTongue' : motherTongue, 'areaType' : areaType, 'speciallyAbled' : speciallyAbled, 'bpl' : bpl,
   'presentAddress' : presentAddress, 'presentCity' : presentCity, 'presentState' : presentState,
   'presentPostOffice' : presentPostOffice, 'presentPoliceStation' : presentPoliceStation, 'presentZipcode' : presentZipcode,
   'permanentAddress' : permanentAddress, 'permanentCity' : city, 'permanentState' : state, 'permanentPostOffice' : postOffice,
   'permanentPoliceStation' : policeStation, 'permanentZipcode' : zipcode, 'nationality' : nationality,
   'primaryContactNumber' : primaryContactNumber, 'primaryEmail' : primaryEmail, 'isSponsored' : isSponsored,'hosteller' : hosteller,
   'whatsappNumber' : whatsappNumber, 'instituteHouseId' : instituteHouseId, 'admissionInClass' : admissionInClass,
   'speciallyAbledType' : speciallyAbledType, 'studentNameAsPerAadhar' : studentNameAsPerAadhar,
   'childCategoryCriteria' : childCategoryCriteria};
  return studentBasicInfo;
}

function getStudentFamilyInfo(){

  var motherName = $("#student-mother-name").val().trim();
  var fatherName = $("#student-father-name").val().trim();

  var mothersQualification = $("#student-mother-qualification").val().trim();
  var fathersQualification = $("#student-father-qualification").val().trim();


  var isError = false;
  var impactedElement = null;

  var motherContactNumber = $("#mother-contact-number").val().trim();
  if(motherContactNumber != "" && !regexPhoneNumber.test(motherContactNumber)) {
    isError = true;
    impactedElement = "mother-contact-number";
    string = "Mother Contact Number should be of 10 digits.";
  }

  var fatherContactNumber = $("#father-contact-number").val().trim();
  if(fatherContactNumber != "" && !regexPhoneNumber.test(fatherContactNumber)) {
    isError = true;
    impactedElement = "father-contact-number";
    string = "Father Contact Number should be of 10 digits.";
  }

  var motherOccupation = $("#mother-occupation").val().trim();
  var fatherOccupation = $("#father-occupation").val().trim();
  var motherAadharNumber = $("#mother-aadhar-number").val().trim();
  if(motherAadharNumber != "" && !regexAadharNumber.test(motherAadharNumber)) {
    isError = true;
    impactedElement = "mother-aadhar-number";
    string = "Invalid Mother Aadhar Number";
  }
  var fatherAadharNumber = $("#father-aadhar-number").val().trim();
  if(fatherAadharNumber != "" && !regexAadharNumber.test(fatherAadharNumber)) {
    isError = true;
    impactedElement = "father-aadhar-number";
    string = "Invalid Father Aadhar Number";
  }

  var mothersPanCardDetails = $("#mother-pan-card-details").val().trim();

  var fathersPanCardDetails = $("#father-pan-card-details").val().trim();

  var approxFamilyIncome = $("#approx-family-income").val().trim();
  if(approxFamilyIncome < 0) {
    isError = true;
    impactedElement = "approx-family-income";
    string = "Please put the proper value for the income";
  }

  var motherAnnualIncome = $("#mother-annual-income").val().trim();
  if(motherAnnualIncome < 0) {
      isError = true;
      impactedElement = "mother-annual-income";
      string = "Please put the proper value for the income";
  }

  var fatherAnnualIncome = $("#father-annual-income").val().trim();
  if(fatherAnnualIncome < 0) {
       isError = true;
       impactedElement = "father-annual-income";
       string = "Please put the proper value for the income";
  }

  if(isError) {
  if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
  }
    showErrorDialogBox(string);
    return;
  }

  var studentFamilyInfo = {'mothersName' : motherName, 'fathersName' : fatherName,
  'mothersQualification' : mothersQualification, 'fathersQualification' : fathersQualification,
  'mothersContactNumber' : motherContactNumber, 'fathersContactNumber' : fatherContactNumber,
  'mothersOccupation' : motherOccupation, 'mothersAnnualIncome' : motherAnnualIncome, 'fathersOccupation' : fatherOccupation,
  'fathersAnnualIncome' : fatherAnnualIncome, 'mothersAadharNumber' : motherAadharNumber, 'fathersAadharNumber' : fatherAadharNumber,
  'mothersPanCardDetails':mothersPanCardDetails,'fathersPanCardDetails':fathersPanCardDetails,
  'approxFamilyIncome' : approxFamilyIncome};

  return studentFamilyInfo;
}

function getStudentGuardianInfo(){
  var isError = false;
  var impactedElement = null;

  var guardianName = $("#guardian-name").val().trim();
  var guardianRelation = $("#guardian-relation").val().trim();
  var guardianAge = $("#guardian-age").val().length == 0 ? null : $("#guardian-age").val().trim();
  if(guardianAge != null && !regexAge.test(guardianAge)) {
    isError = true;
    impactedElement = "guardian-age";
    string = "Invalid Age";
  }
  var guardianGender = $("#guardian-gender").val().trim();
  var guardianOccupation = $("#guardian-occupation").val().trim();
  var guardianEmail = $("#guardian-email").val().trim();
  if(guardianEmail != "" && !regexEmail.test(guardianEmail)) {
    isError = true;
    impactedElement = "guardian-email";
    string = "Type correct format of guardian Email";
  }
  var guardianContactNumber = $("#guardian-contact-number").val().trim();
  if(guardianContactNumber != "" && !regexPhoneNumber.test(guardianContactNumber)) {
    isError = true;
    impactedElement = "guardian-contact-number";
    string = "Guardian contact Number should be of 10 digits.";
  }
  var guardianAddress = $("#guardian-address").val().trim();
  var guardianCity = $("#guardian-city").val().trim();
  var guardianState = $("#guardian-state").find(".country-states").find(':selected').val() == undefined ? '' : $("#guardian-state").find(".country-states").find(':selected').val().trim();
  var guardianZipcode = $("#guardian-zipcode").val().trim();



  var studentGuardianInfo = {};

  if(isError) {
    if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
    }
    showErrorDialogBox(string);
    return;
  }

  if(guardianGender != ''){
    studentGuardianInfo = {'guardianName' : guardianName, 'relation' : guardianRelation,
    'age' : guardianAge, 'gender' : guardianGender, 'occupation' : guardianOccupation,
    'email' : guardianEmail, 'contactNumber' : guardianContactNumber, 'occupation' : guardianOccupation,
    'address' : guardianAddress, 'city' : guardianCity, 'state' : guardianState, 'zipcode' : guardianZipcode};
  } else{
    studentGuardianInfo = {'guardianName' : guardianName, 'relation' : guardianRelation,
    'age' : guardianAge, 'occupation' : guardianOccupation,
    'email' : guardianEmail, 'contactNumber' : guardianContactNumber, 'occupation' : guardianOccupation,
    'address' : guardianAddress, 'city' : guardianCity, 'state' : guardianState, 'zipcode' : guardianZipcode};
  }
  return studentGuardianInfo;
}

function getStudentPreviousSchoolInfo(){
  var isError = false;
  var impactedElement = null;
  var isAdmissionTcBased = $("#student-is-admission-tc-based").is(":checked");
  var tcNumber = $("#previous-school-tc-number").val().trim();
  var previousSchoolName = $("#previous-school-name").val().trim();
  var medium = $("#previous-school-medium").val().trim();
  var classPassed = $("#class-passed").val().trim();
  var yearOfPassing = $("#year-of-passing").val().length == 0 ? null : $("#year-of-passing").val().trim();
  if(yearOfPassing < 0) {
    isError = true;
    impactedElement = "year-of-passing";
    string = "Invalid Year of Passing";
  }
  var result = $("#result").val().trim();
  var percentage = $("#percentage").val().trim();
  if(isError) {
  if(impactedElement != null) {
    $("#" + impactedElement).css("border", "1px solid #ff8795");
  }
    showErrorDialogBox(string);
    return;
  }
  var studentPreviousSchoolInfo = {'isAdmissionTcBased' : isAdmissionTcBased, 'tcNumber' : tcNumber,
    'schoolName' : previousSchoolName, 'medium' : medium, 'classPassed' : classPassed,
    'yearOfPassing' : yearOfPassing, 'result' : result, 'percentage' : percentage};
  return studentPreviousSchoolInfo;
}

function getStudentMedicalInfo(){
  var bloodGroup = $("#blood-group").val().trim();
  var bloodPressure = $("#blood-pressure").val().trim();
  var pulse = $("#pulse").val().trim();
  var height = $("#height").val().trim();
  var weight = $("#weight").val().length == 0 ? null : $("#weight").val().trim();
  var dateOfPhysicalExamination = getDate($("#date-of-physical-examination").val());
  var dateOfPhysicalExaminationInt = null;
  if(dateOfPhysicalExamination != null){
    dateOfPhysicalExaminationInt = dateOfPhysicalExamination.getTime()/1000;
  }

  var studentMedicalInfo = {};
  if(bloodGroup == ''){
    studentMedicalInfo = {'bloodPressure' : bloodPressure,
     'pulse' : pulse, 'height' : height, 'weight' : weight, 'dateOfPhysicalExamination' : dateOfPhysicalExamination};
  } else{
    studentMedicalInfo = {'bloodGroup' : bloodGroup, 'bloodPressure' : bloodPressure,
     'pulse' : pulse, 'height' : height, 'weight' : weight, 'dateOfPhysicalExamination' : dateOfPhysicalExaminationInt};
  }
  return studentMedicalInfo;
}

function updateStudent(finish) {
    updateStudentWithScreen(finish, 'STUDENT-LIST');
}

function updateStudentWithScreen(finish, screenName) {
  var invalid = validateMandatoryFields($("#update-basic-info-content"));
  invalid |= validateMandatoryFields($("#update-family-info-content"));
  invalid |= validateMandatoryFields($("#update-guardian-info-content"));
  invalid |= validateMandatoryFields($("#update-previous-school-info-content"));
  invalid |= validateMandatoryFields($("#update-medical-info-content"));
  if(invalid){
    showErrorDialogBox("Please fill mandatory fields.")
    return;
  }

  var documentName = "";
  var file = "";
  // var ONE_KB = 1024;
  // if (($("#update-student-photo"))[0].files.length > 0) {
  //   file = ($("#update-student-photo"))[0].files[0];
  //     if((file.size / ONE_KB) > STUDENT_IMAGE_SIZE_LIMIT){
  //       showErrorDialogBox("Size Of document cannot be greater than " + STUDENT_IMAGE_SIZE_LIMIT + " kb");
  //       return;
  //     }
  // }

  var studentId = $("#update-admission-student-id").text().trim();
  var standardId = $("#update-student-class").find(':selected').val().trim();
  var sectionId = $("#update-student-section").find(':selected').val().trim();
  if(sectionId == ""){
    sectionId = null;
  }
  var admissionAcademicSessionId = $("#update-student-academic-session").find(':selected').val().trim();
  var studentGuardianInfoList = [];
  var studentBasicInfo = getStudentBasicInfoUpdate();
  if(studentBasicInfo === undefined) {
    return;
  }
  console.log(studentBasicInfo);
  var studentFamilyInfo = getStudentFamilyInfo();
  if(studentFamilyInfo === undefined) {
    return;
  }
  var studentGuardianInfo = getStudentGuardianInfo();
  if(studentGuardianInfo === undefined) {
    return;
  }
  var studentPreviousSchoolInfo = getStudentPreviousSchoolInfo();
  if(studentPreviousSchoolInfo === undefined) {
    return;
  }
  var studentMedicalInfo = getStudentMedicalInfo();
  if(studentMedicalInfo === undefined) {
    return;
  }
  studentGuardianInfoList.push(studentGuardianInfo);

  var collectedStudentDocumentType = getStudentDocumentInformation();

  var newAdmission = $("#update\\.student-new-admission").is(":checked");
  var updateStudentPayload = {'studentId' :studentId, 'standardId': standardId,
  'sectionId': sectionId, 'studentBasicInfo': studentBasicInfo, 'studentFamilyInfo': studentFamilyInfo,
  'studentGuardianInfoList': studentGuardianInfoList, 'studentPreviousSchoolInfo':studentPreviousSchoolInfo,
  'studentMedicalInfo':studentMedicalInfo, 'newAdmission' : newAdmission, 'collectedStudentDocumentType' : collectedStudentDocumentType};

  var formData = new FormData();
  if(file != "") {
    formData.append('document', file);
    formData.append('documentName', documentName);
  }

  formData.append('updateStudentPayload', JSON.stringify(updateStudentPayload));

  console.log(screenName);
  ajaxClient.uploadFile("/admission/update-student/"+admissionAcademicSessionId, formData, function(data) {
          $("#admission-status-modal-container").html(data);
          var response = JSON.parse($("#student-data-response").text());
          // $('#screen-name').text(screenName);
          if(response.success){
            if(finish){
              returnToMainScreen();
              $("#student-document-warning-popup").modal("toggle");
            }else{
                switchToNextTab();
            }
         } else {
           $("#admission-status-modal").modal('toggle');
         }

  });
}

function getStudentDocumentInformation() {
  var collectedStudentDocumentType = [];
  $("input.student-document-collected-checkbox").each(function() {
      if($(this).is(":checked")) {
        var documentType = $(this).parent().parent().parent().attr("id");
        collectedStudentDocumentType.push(documentType);
      }
  });
  return collectedStudentDocumentType;
}

var sidebarFull = 200;
var sidebarCollapsed = 80;
var graphLibraryLoaded = false;
var ALL_ENROLLED_STUDENT_SCREEN_ID = "all-enrolled-student-screen";
var NSO_STUDENTS_LIST_SCREEN_ID = "nso-student-list-screen";
var NEW_ADMISSION_MALE_COLOR = "#93c47d";
var NEW_ADMISSION_FEMALE_COLOR = "#cbe6c0";
var NEW_ADMISSION_TRANSGENDER_COLOR = "#E69138";
var LOGO_COLOR = "#43a2ad";
var OLD_STUDENTS_FEMALE_COLOR = "#bfe9ef";
var LIGHT_GREY_COLOR = "#F3F3F3";
var STUDENT_DOCUMENT_TYPE = ["TRANSFER_CERTIFICATE", "AADHAR_CARD", "STUDENT_PROFILE_IMAGE", "FATHER_PROFILE_IMAGE", "MOTHER_PROFILE_IMAGE", "RATION_CARD", "PREVIOUS_SCHOOL_STUDY_CERTIFICATE", "BIRTH_CERTIFICATE", "STUDENT_PAN_CARD", "BIRTH_CERTIFICATE_AND_AFFIDAVIT"];

$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    var urlParams = new URLSearchParams(window.location.search);
    actionType = urlParams.get(ACTION_TYPE);

    if(actionType != null && actionType != undefined && actionType != "") {
      if (actionType === "admit-student"){
        $("#admitStudentNav").parent().addClass("active");
        loadAdmitStudentMenu();
      }

      //remove params from url
      var url_string = window.location.href;
      url = removeURLParameter(url_string, ACTION_TYPE)
      let stateObj = { id: "100" };
      window.history.replaceState(stateObj, url_string, url);
    }

    paymentReminder.readPaymentState();
});

var menuLoader = {

  registerSidebarMenu : function () {
    sideBarHoverEventCallback();
    activateMenuItem();
    menuLoader.registerHomeMenu();
    menuLoader.registerAdmitStudentMenu();
    menuLoader.registerPendingEnrolmentsMenu();
    menuLoader.registerAllStudentMenu();
    menuLoader.registerStudentDetailsMenu();
    menuLoader.registerNSOStudentDetailsMenu();
    menuLoader.registerRelievedStudentDetailsMenu();
    menuLoader.registerSiblingDetailsNav();
    menuLoader.registerStudentRegistrationDetailsMenu();
    menuLoader.registerReportsDetailsMenu();
    menuLoader.registerIdentityCardsMenu();
    menuLoader.registerAdmissionEnquiryMenu();
  },

  registerHomeMenu : function () {
    $('#statisticsNav').on('click', function() {
        statistics.loadHomePage();
    });
  },

  registerAdmitStudentMenu : function () {
    $('#admitStudentNav').on('click', function() {
        loadAdmitStudentMenu();
    });
  },

  registerPendingEnrolmentsMenu : function () {
      $('#pendingEnrolmentsNav').on('click', function() {
          loadPendingEnrolmentsPage();
      });
  },

  registerStudentDetailsMenu : function () {
    $('#studentDetailsNav').on('click', function() {
        loadStudentDetailsPage();
    });
  },

  registerAllStudentMenu : function () {
    $('#relieveStudentNav').on('click', function() {
        loadAllStudentsPage();
    });
  },

  registerRelievedStudentDetailsMenu : function () {
    $('#relievedStudentDetailsNav').on('click', function() {
        loadRelievedStudentDetailsPage();
    });
  },

  registerNSOStudentDetailsMenu : function () {
    $('#nsoStudentDetailsNav').on('click', function() {
        loadNSOStudentDetailsPage();
    });
  },

  registerStudentRegistrationDetailsMenu : function () {
    $('#registrationStudentsDetailsNav').on('click', function() {
        studentRegistration.loadHomePage();
    });
  },

  registerSiblingDetailsNav : function () {
    $('#siblingDetailsNav').on('click', function() {
        siblingDetails.loadHomePage();
    });
  },

  registerReportsDetailsMenu : function () {
    $('#reportsNav').on('click', function() {
        admissionReports.loadMainScreen();
    });
  },

  registerIdentityCardsMenu : function () {
    $('#identityCardsNav').on('click', function() {
        identityCards.loadMainScreen();
    });
  },

  registerAdmissionEnquiryMenu : function () {
    $('#admissionEnquiryDetailsNav').on('click', function() {
        admissionEnquiry.loadMainScreen();
    });
  }
};


var statistics =  {

    initHomePage : function () {
        academicSessionHandler.bindSessionChangeEvent(statistics.loadHomePageForSession);
        statistics.displayDashboardContent();
        statistics.onClickAdmissionStatsViewSwitchButton();
    },

    loadHomePage : function () {
      ajaxClient.get("/admission/home", function(data) {
          $("#main-content").html(data);
          statistics.initHomePage();
      });
    },

    loadStudentList : function(inputAdmissionStatus) {
      var admissionStats = readJson("#admission-home-stats");
      if(admissionStats == null){
        return;
      }

      var tabularViewHtml = "<table id=\"datatables-reponsive\" class=\"table table-bordered table-striped datatables-reponsive-table\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Sr No.</th><th scope=\"col\">Admission Date</th><th scope=\"col\">Admission Number</th><th scope=\"col\">Student Name</th><th scope=\"col\">Father Name</th><th scope=\"col\">Class</th><th scope=\"col\">Gender</th><th scope=\"col\">DOB</th></tr></thead><tbody>";
      
      var studentDetails;
      if(inputAdmissionStatus == "NEW_ADMISSION"){
        studentDetails = admissionStats.newAdmissionStudentList;
      }
      else if(inputAdmissionStatus == "RTE_STUDENT"){
        studentDetails = admissionStats.rteStudentList;
      }

      var srNo = 1;
      for( var i = 0 ; i < studentDetails.length ; i++){
        var date = studentDetails[i].studentBasicInfo.admissionDate;
        var admissionDate = "";
        if(date != null && date > 0){
          admissionDate = getFormattedDate(date);
        }
        var admissionNumber = studentDetails[i].studentBasicInfo.admissionNumber;
        var studentName = studentDetails[i].studentBasicInfo.name;
        var fatherName = "";
        if(studentDetails[i].studentFamilyInfo.fathersName != null){
          fatherName = studentDetails[i].studentFamilyInfo.fathersName
        }
        var standard = studentDetails[i].studentAcademicSessionInfoResponse.standard.displayNameWithSection;
        var gender = studentDetails[i].studentBasicInfo.gender;
        var dob = "";
        if(studentDetails[i].studentBasicInfo.dateOfBirth != null){
          var dobDate = studentDetails[i].studentBasicInfo.dateOfBirth;
          var dob = "";
          if(dobDate != null && dobDate > 0){
            dob = getFormattedDate(dobDate);
          }
        }
        
        tabularViewHtml += "<tr><th scope=\"row\">" + srNo++ + "</th><td>" + admissionDate + "</td><td>" + admissionNumber +"</td><td>" + studentName + "</td> <td>" + fatherName + "</td> <td>" + standard + "</td> <td>" + gender + "</td> <td>" + dob + "</td></tr>";

      }

      tabularViewHtml += "</tbody></table>";
      $("#admission-student-data-display").html(tabularViewHtml);
      $('#datatables-reponsive').DataTable({
        searching: true,
        bPaginate: true,
        columnDefs: [
            { orderable: false, targets: "no-sort" },
        ],
        order: [[0, 'asc']]
    });
      $("#display-admission-student-data-modal").modal({backdrop: 'static', keyboard: false});
    },

    loadHomePageForSession : function () {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      ajaxClient.get("/admission/session-home/"+academicSessionId, function(data) {
          $("#admission-dashboard-session-content").html(data);
          var currentCardViewType = $("#admission-stats-view-type").val();
          if(currentCardViewType === TABULAR) {
            $("#current-card-view-type").text(TABULAR);
            statistics.displayTabularDashboardContent();
          } else {
            statistics.displayDashboardContent();
            $("#current-card-view-type").text(GRAPHICAL);
          }
          statistics.onClickAdmissionStatsViewSwitchButton();
      });
    },

    refreshHomePage : function () {
        statistics.loadHomePageForSession();
    },

    onClickAdmissionStatsViewSwitchButton : function () {
      $("#admission-stats-view-type").on('change', function () {
        var currentCardViewType = $("#admission-stats-view-type").val();
        if(currentCardViewType === TABULAR) {
          $("#current-card-view-type").text(TABULAR);
          statistics.displayTabularDashboardContent();
        } else {
          statistics.displayDashboardContent();
          $("#current-card-view-type").text(GRAPHICAL);
        }
      });
    },

    displayTabularDashboardContent : function () {
      var admission_home_stats_json = readJson("#admission-home-stats");
      var standards = readJson("#home-page-standards-stats");

      admission_home_stats = statistics.initAdmissionHomeStats(admission_home_stats_json, standards)

      statistics.renderTopBoxesData(admission_home_stats);
      statistics.renderStudentGenderCountTable(admission_home_stats);
      statistics.renderRTECountTable(admission_home_stats);
      statistics.renderNewAdmissionCountTable(admission_home_stats);
      statistics.renderCategoryCountTable(admission_home_stats);

    },

    initAdmissionHomeStats : function (admission_home_stats, standards) {
      var classNameList = [];
      for( var i = 0 ; i < standards.length; i++){
        classNameList.push(standards[i].displayName);
      }

      var totalStudents = admission_home_stats.totalStudents;
      var oldStudents = admission_home_stats.oldStudents;
      var newRegistrationStudents = admission_home_stats.newRegistrationStudents;
      var newAdmissionStudents = admission_home_stats.newAdmissionStudents;
      var totalLastYearStudents = admission_home_stats.totalLastYearStudents;
      var oldLastYearStudents = admission_home_stats.oldLastYearStudents;
      var newRegistrationLastYearStudents = admission_home_stats.newRegistrationLastYearStudents;
      var newAdmissionLastYearStudents = admission_home_stats.newAdmissionLastYearStudents;

      var classWiseOldStudentGenderCountMapObj = admission_home_stats.classWiseOldStudentGenderCountMap;
      var classWiseNewAdmissionGenderCountMapObj = admission_home_stats.classWiseNewAdmissionGenderCountMap;
      var classWiseNewAdmissionCountMapObj = admission_home_stats.classWiseNewAdmissionCountMap;
      var classWiseLastYearNewAdmissionCountMapObj = admission_home_stats.classWiseLastYearNewAdmissionCountMap;
      var classWiseRteCountMapObj = admission_home_stats.classWiseRteCountMap;
      var classWiseStudentCountMapObj = admission_home_stats.classWiseStudentCountMap;
      var classWiseCategoryCountMapObj = admission_home_stats.classWiseCategoryCountMap;

      let classWiseOldStudentGenderCountMap = new Map();
      let classWiseNewAdmissionGenderCountMap = new Map();
      let classWiseNewAdmissionCountMap = new Map();
      let classWiseLastYearNewAdmissionCountMap = new Map();
      let classWiseRteCountMap = new Map();
      let classWiseStudentCountMap = new Map();
      let classWiseCategoryCountMap = new Map();

      for(var i = 0; i < classNameList.length; i++) {
        var className = classNameList[i];
        if(!isObjectEmpty(classWiseOldStudentGenderCountMapObj)) {
            classWiseOldStudentGenderCountMap.set(className,classWiseOldStudentGenderCountMapObj[className]);
        }

        if(!isObjectEmpty(classWiseNewAdmissionGenderCountMapObj)) {
            classWiseNewAdmissionGenderCountMap.set(className, classWiseNewAdmissionGenderCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseNewAdmissionCountMapObj)) {
            classWiseNewAdmissionCountMap.set(className, classWiseNewAdmissionCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseLastYearNewAdmissionCountMapObj)) {
            classWiseLastYearNewAdmissionCountMap.set(className, classWiseLastYearNewAdmissionCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseRteCountMapObj)) {
            classWiseRteCountMap.set(className, classWiseRteCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseStudentCountMapObj)) {
            classWiseStudentCountMap.set(className, classWiseStudentCountMapObj[className]);
        }
        if(!isObjectEmpty(classWiseCategoryCountMapObj)) {
            classWiseCategoryCountMap.set(className, classWiseCategoryCountMapObj[className]);
        }
      }

      admission_home_stats = {'totalStudents' : totalStudents, 'oldStudents' : oldStudents, 'newRegistrationStudents' : newRegistrationStudents, 'newAdmissionStudents' : newAdmissionStudents, 'totalLastYearStudents' : totalLastYearStudents, 'oldLastYearStudents' : oldLastYearStudents, 'newRegistrationLastYearStudents' : newRegistrationLastYearStudents, 'newAdmissionLastYearStudents' : newAdmissionLastYearStudents, 'classWiseOldStudentGenderCountMap' : classWiseOldStudentGenderCountMap, 'classWiseNewAdmissionGenderCountMap' : classWiseNewAdmissionGenderCountMap, 'classWiseNewAdmissionCountMap' : classWiseNewAdmissionCountMap, 'classWiseLastYearNewAdmissionCountMap' : classWiseLastYearNewAdmissionCountMap, 'classWiseRteCountMap' : classWiseRteCountMap, 'classWiseStudentCountMap' : classWiseStudentCountMap, 'classWiseCategoryCountMap' : classWiseCategoryCountMap};

      return admission_home_stats;
    },

    renderCategoryCountTable : function (admission_home_stats) {
      $("#categorywise-graph-header-text").text("Categorywise Student Distribution (Tabular View)");
      $("#chartjs-categorywise-student-bar-distribution").attr("style", "display:none;");
      $("#tabularview-categorywise-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseCategoryCountMap = admission_home_stats.classWiseCategoryCountMap;
      var totalStudents = admission_home_stats.totalStudents;

      var length = classWiseCategoryCountMap.size;

      var classNameList = [];
      var bcStudentCountList = [];
      var genralStudentCountList = [];
      var minStudentCountList = [];
      var obcStudentCountList = [];
      var otherStudentCountList = [];
      var sbcStudentCountList = [];
      var scStudentCountList = [];
      var stStudentCountList = [];
      var classTotalList = [];
      for (let [className, categoryCountMap] of  classWiseCategoryCountMap.entries()) {
        classNameList.push(className);
        var classTotal = 0;
        $.each(categoryCountMap, function(category, count) {
          if(category === "BC") {
            bcStudentCountList.push(count);
          } else if(category === "GENERAL") {
            genralStudentCountList.push(count);
          } else if(category === "MIN") {
            minStudentCountList.push(count);
          } else if(category === "OBC") {
            obcStudentCountList.push(count);
          } else if(category === "OTHER") {
            otherStudentCountList.push(count);
          } else if(category === "SBC") {
            sbcStudentCountList.push(count);
          } else if(category === "SC") {
            scStudentCountList.push(count);
          } else if(category === "ST") {
            stStudentCountList.push(count);
          }
          classTotal += count;
        });
        classTotalList.push(classTotal);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">BC</th><th scope=\"col\">General</th><th scope=\"col\">MIN</th><th scope=\"col\">OBC</th><th scope=\"col\">OTHER</th><th scope=\"col\">SBC</th><th scope=\"col\">SC</th><th scope=\"col\">ST</th><th scope=\"col\">Total</th></tr></thead><tbody>";
      var grandBcStudentCountList = 0;
      var grandGeneralStudentCountList = 0;
      var grandMinStudentCountList = 0;
      var grandObcStudentCountList =0;
      var grandOtherStudentCountList = 0;
      var grandSbcStudentCountList = 0;
      var grandScStudentCountList = 0;
      var grandStStudentCountList = 0;
      var grandClassTotalList = 0;
      for(var i = 0; i < length; i++) {
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + bcStudentCountList[i] +"</td><td scope=\"row\">" + genralStudentCountList[i] +"</td><td scope=\"row\">" + minStudentCountList[i] +"</td><td scope=\"row\">" + obcStudentCountList[i] +"</td><td scope=\"row\">" + otherStudentCountList[i] +"</td><td scope=\"row\">" + sbcStudentCountList[i] +"</td><td scope=\"row\">" + scStudentCountList[i] +"</td><td scope=\"row\">" + stStudentCountList[i] +"</td><td scope=\"row\">" + classTotalList[i] +"</td></tr>";
        grandBcStudentCountList += bcStudentCountList[i];
        grandGeneralStudentCountList += genralStudentCountList[i];
        grandMinStudentCountList += minStudentCountList[i];
        grandObcStudentCountList += obcStudentCountList[i];
        grandOtherStudentCountList += otherStudentCountList[i];
        grandSbcStudentCountList += sbcStudentCountList[i];
        grandScStudentCountList += scStudentCountList[i];
        grandStStudentCountList += stStudentCountList[i];
        grandClassTotalList += classTotalList[i];
      }

      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandBcStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandGeneralStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandMinStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandObcStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" +  grandOtherStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandSbcStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandScStudentCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandStStudentCountList + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassTotalList + "</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-categorywise-student-distribution").html(genderwiseTableHTML);

    },

    renderNewAdmissionCountTable : function (admission_home_stats) {
      $("#new-admission-graph-header-text").text("New Admissions (Tabular View)");
      $("#chartjs-new-admission-student-bar-distribution").attr("style", "display:none;");
      $("#chartjs-new-admission").attr("style", "display:none;");
      $("#percentage-change-new-admission").attr("style", "display:none;float:right;");
      $("#tabularview-new-admission-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseNewAdmissionCountMap = admission_home_stats.classWiseNewAdmissionCountMap;
      var classWiseLastYearNewAdmissionCountMap = admission_home_stats.classWiseLastYearNewAdmissionCountMap;

      var length = classWiseNewAdmissionCountMap.size;

      var classNameList = [];
      var totalNewAdmissionCountList = [];
      for (let [className, count] of  classWiseNewAdmissionCountMap.entries()) {
        classNameList.push(className);
        totalNewAdmissionCountList.push(count);
      }

      var totalLastYearNewAdmissionCountList = [];
      var length2 = classWiseLastYearNewAdmissionCountMap.size;
      if(length2 <= 0) {
        for(var i = 0; i < length; i++) {
          totalLastYearNewAdmissionCountList.push(0);
        }
      } else {
        for (let [className, count] of  classWiseLastYearNewAdmissionCountMap.entries()) {
          totalLastYearNewAdmissionCountList.push(count);
        }
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">New Admissions</th><th scope=\"col\">%</th></tr></thead><tbody>";
      var grandTotalNewAdmissionCountList = 0;
      var grandPercentageChangeHTML = 0;
      for(var i = 0; i < length; i++) {
        var percentageChangeHTML = "";
        if(totalLastYearNewAdmissionCountList[i] == 0) {
          percentageChangeHTML = "<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span>";
        } else {
          var percentageChange = getPercentageChange(totalLastYearNewAdmissionCountList[i], totalNewAdmissionCountList[i]);
          percentageChangeHTML = percentageChange >= 0 ? "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "<i class=\"bi bi-arrow-up\"></i></span>"
           : "<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "<i class=\"bi bi-arrow-down\"></i></span>";
        }
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + totalNewAdmissionCountList[i] +"</td><td scope=\"row\">" + percentageChangeHTML +"</td></tr>";
        grandTotalNewAdmissionCountList += parseFloat(totalNewAdmissionCountList[i]);
        grandPercentageChangeHTML += parseFloat(percentageChangeHTML);
      }

      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" +"</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalNewAdmissionCountList +"</td><td scope=\"row\" style=\"font-weight:bold\">" + (isNaN(grandPercentageChangeHTML)?"-":grandPercentageChangeHTML) +"</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-new-admission-student-distribution").html(genderwiseTableHTML);

    },

    renderRTECountTable : function (admission_home_stats) {
      $("#rte-graph-header-text").text("RTE (Tabular View)");
      $("#chartjs-rte-student-pie").attr("style", "display:none;");
      $("#percentage-rte-students").attr("style", "display:none;");
      $("#tabularview-rte-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseRteCountMap = admission_home_stats.classWiseRteCountMap;
      var classWiseStudentCountMap = admission_home_stats.classWiseStudentCountMap;

      var length = classWiseRteCountMap.size;

      var classNameList = [];
      var allStudentsClassWiseCount = [];
      for (let [className, count] of  classWiseStudentCountMap.entries()) {
        classNameList.push(className);
        allStudentsClassWiseCount.push(count);
      }
      var rteClassWiseCount = [];
      for (let [className, count] of  classWiseRteCountMap.entries()) {
        rteClassWiseCount.push(count);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\">Class</th><th scope=\"col\">RTE Students</th><th scope=\"col\">%</th></tr></thead><tbody>";

      var grandRteClassWiseCount = 0;
      var grandPercentage = 0;
      for(var i = 0; i < length; i++) {
        if(allStudentsClassWiseCount[i] == 0) {
          percentageChangeHTML = "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + rteClassWiseCount[i] +"</td><td scope=\"row\">-</td></tr>";
        } else {
          var percentage = getPercentage(rteClassWiseCount[i], allStudentsClassWiseCount[i]);
          genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + rteClassWiseCount[i] +"</td><td scope=\"row\">" + percentage +"</td></tr>";
        }
        grandRteClassWiseCount += parseFloat(rteClassWiseCount[i]);
        grandPercentage += parseFloat(percentage);
      }
      genderwiseTableHTML += "<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandRteClassWiseCount + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandPercentage + "</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-rte-student-distribution").html(genderwiseTableHTML);

    },

    renderStudentGenderCountTable : function (admission_home_stats) {
      $("#genderwise-graph-header-text").text("Genderwise Student Distribution (Tabular View)");
      $("#chartjs-genderwise-student-bar-distribution").attr("style", "display:none;");
      $("#tabularview-genderwise-student-distribution").attr("style", "display:block;overflow-y: auto;max-height: 450px;");
      var classWiseNewAdmissionGenderCountMap = admission_home_stats.classWiseNewAdmissionGenderCountMap;
      var classWiseOldStudentGenderCountMap = admission_home_stats.classWiseOldStudentGenderCountMap;

      var length = classWiseNewAdmissionGenderCountMap.size;

      var classNameList = [];
      var classWiseNewAdmissionMaleCount = [];
      var classWiseNewAdmissionFemaleCount = [];
      // var classWiseNewAdmissionTransgenderCount = [];
      var classWiseNewAdmissionTotalCount = [];

      for (let [className, genderCountMap] of  classWiseNewAdmissionGenderCountMap.entries()) {
        classNameList.push(className);
        var classTotal = 0;
        $.each(genderCountMap, function(gender, count) {
          if(gender === "MALE") {
            classWiseNewAdmissionMaleCount.push(count);
            classTotal += count;
          } else if(gender === "FEMALE") {
            classWiseNewAdmissionFemaleCount.push(count);
            classTotal += count;
          }
          // else if(gender === "TRANSGENDER") {
          //   classWiseNewAdmissionTransgenderCount.push(count);
          //   classTotal += count;
          // }
        });
        classWiseNewAdmissionTotalCount.push(classTotal);
      }

      var classWiseOldStudentsMaleCount = [];
      var classWiseOldStudentsFemaleCount = [];
      // var classWiseOldStudentsTransgenderCount = [];
      var classWiseOldStudentsTotalCount = [];

      for (let [className, genderCountMap] of  classWiseOldStudentGenderCountMap.entries()) {
        var classTotal = 0;
        $.each(genderCountMap, function(gender, count) {
          if(gender === "MALE") {
            classWiseOldStudentsMaleCount.push(count);
            classTotal += count;
          } else if(gender === "FEMALE") {
            classWiseOldStudentsFemaleCount.push(count);
            classTotal += count;
          }
          // else if(gender === "TRANSGENDER") {
          //   classWiseOldStudentsTransgenderCount.push(count);
          //   classTotal += count;
          // }
        });
        classWiseOldStudentsTotalCount.push(classTotal);
      }

      var genderwiseTableHTML = "<table  class=\"table table-bordered table-striped text-center\"><thead style=\"background-color:#f2f2f2;position:sticky;top:0;z-index-1\"><tr><th scope=\"col\" rowspan=\"2\">Class</th><th scope=\"col\" colspan=\"3\">New Students</th><th scope=\"col\" colspan=\"3\">Old Students</th><th scope=\"col\" colspan=\"3\">Total Students</th></tr><tr><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th><th scope=\"col\">M</th><th scope=\"col\">F</th><th scope=\"col\">Total</th></tr></thead><tbody>";

      var grandClassWiseNewAdmissionMaleCount = 0;
      var grandClassWiseNewAdmissionFemaleCount = 0;
      var grandClassWiseNewAdmissionTotalCount = 0;
      var grandClassWiseOldStudentsMaleCount = 0;
      var grandClassWiseOldStudentsFemaleCount = 0;
      var grandClassWiseOldStudentsTotalCount = 0;
      var grandTotalAdmissionStudentMaleCount = 0;
      var grandTotalAdmissionStudentFemaleCount = 0;
      var grandTotalAdmissionStudentTotalCount = 0;
      for(var i = 0; i < length; i++) {
        genderwiseTableHTML += "<tr><td scope=\"row\">" + classNameList[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionMaleCount[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionFemaleCount[i] +"</td><td scope=\"row\">" + classWiseNewAdmissionTotalCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsMaleCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsFemaleCount[i] +"</td><td scope=\"row\">" + classWiseOldStudentsTotalCount[i] +"</td><td scope=\"row\">" + (classWiseNewAdmissionMaleCount[i] + classWiseOldStudentsMaleCount[i]) +"</td><td scope=\"row\">" + (classWiseNewAdmissionFemaleCount[i] + classWiseOldStudentsFemaleCount[i]) +"</td><td scope=\"row\">" + (classWiseNewAdmissionTotalCount[i] + classWiseOldStudentsTotalCount[i]) +"</td></tr>";
        grandClassWiseNewAdmissionMaleCount += classWiseNewAdmissionMaleCount[i];
        grandClassWiseNewAdmissionFemaleCount +=  classWiseNewAdmissionFemaleCount[i];
        grandClassWiseNewAdmissionTotalCount += classWiseNewAdmissionTotalCount[i];
        grandClassWiseOldStudentsMaleCount += classWiseOldStudentsMaleCount[i];
        grandClassWiseOldStudentsFemaleCount += classWiseOldStudentsFemaleCount[i];
        grandClassWiseOldStudentsTotalCount += classWiseOldStudentsTotalCount[i];
        grandTotalAdmissionStudentMaleCount += (classWiseNewAdmissionMaleCount[i] + classWiseOldStudentsMaleCount[i]);
        grandTotalAdmissionStudentFemaleCount += (classWiseNewAdmissionFemaleCount[i] + classWiseOldStudentsFemaleCount[i]);
        grandTotalAdmissionStudentTotalCount += (classWiseNewAdmissionTotalCount[i] + classWiseOldStudentsTotalCount[i]);
      }

      genderwiseTableHTML+="<tr><td scope=\"row\" style=\"font-weight:bold\">" + "Grand Total" + "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionMaleCount +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseNewAdmissionTotalCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsMaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandClassWiseOldStudentsTotalCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentMaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentFemaleCount  +  "</td><td scope=\"row\" style=\"font-weight:bold\">" + grandTotalAdmissionStudentTotalCount  +"</td></tr>";
      genderwiseTableHTML += "</tbody></table>";
      $("#tabularview-genderwise-student-distribution").html(genderwiseTableHTML);
    },

    displayDashboardContent : function () {
     var admission_home_stats_json = readJson("#admission-home-stats");
     var standards = readJson("#home-page-standards-stats");

     admission_home_stats = statistics.initAdmissionHomeStats(admission_home_stats_json, standards)

     statistics.renderTopBoxesData(admission_home_stats);
     statistics.renderGenderCountChartData(admission_home_stats);
     statistics.renderRTECountChartData(admission_home_stats);
     statistics.renderNewAdmissionCountChartData(admission_home_stats);
     statistics.renderCategoryCountChartData(admission_home_stats);

   },

   renderCategoryCountChartData : function (admission_home_stats) {
     $("#categorywise-graph-header-text").text("Categorywise Student Distribution (Graphical View)");
     $("#chartjs-categorywise-student-bar-distribution").attr("style", "display:block;");
     $("#tabularview-categorywise-student-distribution").attr("style", "display:none;");
     var classWiseCategoryCountMap = admission_home_stats.classWiseCategoryCountMap;
     var totalStudents = admission_home_stats.totalStudents;

     var labelArr = ["BC", "GENERAL", "MIN", "OBC", "OTHER", "SBC", "SC", "ST"];
     var bcStudentCount = 0;
     var genralStudentCount = 0;
     var minStudentCount = 0;
     var obcStudentCount = 0;
     var otherStudentCount = 0;
     var sbcStudentCount = 0;
     var scStudentCount = 0;
     var stStudentCount = 0;

     for (let [className, categoryCountMap] of  classWiseCategoryCountMap.entries()) {
       $.each(categoryCountMap, function(category, count) {
         if(category === "BC") {
           bcStudentCount += count;
         } else if(category === "GENERAL") {
           genralStudentCount += count;
         } else if(category === "MIN") {
           minStudentCount += count;
         } else if(category === "OBC") {
           obcStudentCount += count;
         } else if(category === "OTHER") {
           otherStudentCount += count;
         } else if(category === "SBC") {
           sbcStudentCount += count;
         } else if(category === "SC") {
           scStudentCount += count;
         } else if(category === "ST") {
           stStudentCount += count;
         }
       });
     }

     var studentCategoryList = [bcStudentCount, genralStudentCount, minStudentCount, obcStudentCount, otherStudentCount, sbcStudentCount, scStudentCount, stStudentCount];
     var totalStudentList = [totalStudents - bcStudentCount, totalStudents - genralStudentCount, totalStudents - minStudentCount, totalStudents - obcStudentCount, totalStudents - otherStudentCount, totalStudents - sbcStudentCount, totalStudents - scStudentCount, totalStudents - stStudentCount];
     statistics.renderStudentCategoryCountChart(labelArr, studentCategoryList, totalStudentList);
   },

   renderNewAdmissionCountChartData : function (admission_home_stats) {
     $("#new-admission-graph-header-text").text("New Admissions (Graphical View)");
     $("#chartjs-new-admission-student-bar-distribution").attr("style", "display:block;");
     $("#chartjs-new-admission").attr("style", "display:block;");
     $("#percentage-change-new-admission").attr("style", "display:block;float:right;");
     $("#tabularview-new-admission-student-distribution").attr("style", "display:none;");
     var classWiseNewAdmissionCountMap = admission_home_stats.classWiseNewAdmissionCountMap;
     var classWiseLastYearNewAdmissionCountMap = admission_home_stats.classWiseLastYearNewAdmissionCountMap;

     var totalNewAdmissionCount = 0;
     for (let [className, count] of  classWiseNewAdmissionCountMap.entries()) {
       totalNewAdmissionCount += count;
     }

     var totalLastYearNewAdmissionCount = 0;
     var length2 = classWiseLastYearNewAdmissionCountMap.size;
     if(length2 <= 0) {
     } else {
       for (let [className, count] of  classWiseLastYearNewAdmissionCountMap.entries()) {
         totalLastYearNewAdmissionCount += count;
       }
     }

     var labelArr = ["Previous Session", "This Session"];
     var newAdmissionCount = [];
     newAdmissionCount.push(totalLastYearNewAdmissionCount);
     newAdmissionCount.push(totalNewAdmissionCount);

     var html = "";
     if(totalLastYearNewAdmissionCount == 0) {
       html = "<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span>";
     } else {
       var percentageChange = getPercentageChange(totalLastYearNewAdmissionCount, totalNewAdmissionCount);
       html = percentageChange >= 0 ? "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>"
        : "<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>";
      }
     $("#percentage-change-new-admission").html(html);

     statistics.renderNewAdmissionStudentChart(labelArr, newAdmissionCount);
   },

   renderRTECountChartData : function (admission_home_stats) {
     $("#rte-graph-header-text").text("RTE (Graphical View)");
     $("#chartjs-rte-student-pie").attr("style", "display:block;");
     $("#percentage-rte-students").attr("style", "display:block;");
     $("#tabularview-rte-student-distribution").attr("style", "display:none;");
     var classWiseRteCountMap = admission_home_stats.classWiseRteCountMap;
     var classWiseStudentCountMap = admission_home_stats.classWiseStudentCountMap;

     var allStudentsClassWiseCount = [];
     for (let [className, count] of  classWiseStudentCountMap.entries()) {
       allStudentsClassWiseCount.push(count);
     }
     var rteClassWiseCount = [];
     for (let [className, count] of  classWiseRteCountMap.entries()) {
       rteClassWiseCount.push(count);
     }
     var nonRTEStudentsClassWiseCount = [];
     for(var i = 0; i < allStudentsClassWiseCount.length; i++) {
       nonRTEStudentsClassWiseCount.push(allStudentsClassWiseCount[i] - rteClassWiseCount[i]);
     }

     var totalRTEStudent = 0;
     var totalNonRTEStudent = 0;
     for(var i = 0; i < rteClassWiseCount.length; i++) {
       totalRTEStudent += rteClassWiseCount[i];
     }
     for(var i = 0; i < nonRTEStudentsClassWiseCount.length; i++) {
       totalNonRTEStudent += nonRTEStudentsClassWiseCount[i];
     }

     var html = "";
     if((totalRTEStudent + totalNonRTEStudent) == 0) {
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>0%</span><span class=\"text-muted\"> of total students</span>";
     } else {
       var percentageChange = getPercentage(totalRTEStudent, (totalRTEStudent + totalNonRTEStudent));
       html = "<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(percentageChange) + "%</span><span class=\"text-muted\"> of total students</span>";
     }
     $("#percentage-rte-students").html(html);

     var pieDataArr = [totalRTEStudent, totalNonRTEStudent]
     var pielabelArr = ["RTE", "Non RTE"];
     statistics.renderRTEStudentsPieChart(pielabelArr, pieDataArr);
   },

   renderGenderCountChartData : function (admission_home_stats) {
     $("#genderwise-graph-header-text").text("Genderwise Student Distribution (Graphical View)");
     $("#chartjs-genderwise-student-bar-distribution").attr("style", "display:block;");
     $("#tabularview-genderwise-student-distribution").attr("style", "display:none;");
     var classWiseNewAdmissionGenderCountMap = admission_home_stats.classWiseNewAdmissionGenderCountMap;
     var classWiseOldStudentGenderCountMap = admission_home_stats.classWiseOldStudentGenderCountMap;

     var labelArr = [];
     var newAdmissionMale = [];
     var newAdmissionFemale = [];
     // var newAdmissionTransgender = [];
     var oldStudentsMale = [];
     var oldStudentsFemale = [];
     // var oldStudentsTransgender = [];

      for (let [className, genderCountMap] of  classWiseNewAdmissionGenderCountMap.entries()) {
       labelArr.push(className);
       $.each(genderCountMap, function(gender, count) {
         if(gender === "MALE") {
           newAdmissionMale.push(count);
         } else if(gender === "FEMALE") {
           newAdmissionFemale.push(count);
         }
         // else if(gender === "TRANSGENDER") {
         //   newAdmissionTransgender.push(count);
         // }
       });
     }

     for (let [className, genderCountMap] of  classWiseOldStudentGenderCountMap.entries()) {
       $.each(genderCountMap, function(gender, count) {
         if(gender === "MALE") {
           oldStudentsMale.push(count);
         } else if(gender === "FEMALE") {
           oldStudentsFemale.push(count);
         }
         // else if(gender === "TRANSGENDER") {
         //   oldStudentsTransgender.push(count);
         // }
       });
     }

    statistics.renderStudentGenderCountChart(labelArr, newAdmissionMale, newAdmissionFemale, oldStudentsMale, oldStudentsFemale);
   },

   renderTopBoxesData : function(admission_home_stats) {
     var totalStudents = admission_home_stats.totalStudents;
     var oldStudents = admission_home_stats.oldStudents;
     var newRegistrationStudents = admission_home_stats.newRegistrationStudents;
     var newAdmissionStudents = admission_home_stats.newAdmissionStudents;
     $("#total-student-count").text(totalStudents);
     console.log("newRegistrationStudents = " + newRegistrationStudents);
     $("#total-new-registration-count").text(newRegistrationStudents);
     $("#total-new-admissions-count").text(newAdmissionStudents);
     $("#total-old-students-count").text(oldStudents);

     var totalLastYearStudents = admission_home_stats.totalLastYearStudents;
     var oldLastYearStudents = admission_home_stats.oldLastYearStudents;
     var newRegistrationLastYearStudents = admission_home_stats.newRegistrationLastYearStudents;
     var newAdmissionLastYearStudents = admission_home_stats.newAdmissionLastYearStudents;

     if(totalLastYearStudents == 0) {
       $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var totalStudentPercentageChange = getPercentageChange(totalLastYearStudents, totalStudents);
       if(totalStudentPercentageChange >= 0) {
         $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(totalStudentPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-student-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(totalStudentPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }

     console.log("newRegistrationLastYearStudents : " + newRegistrationLastYearStudents);
     if(newRegistrationLastYearStudents == 0) {
       $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var newRegistrationPercentageChange = getPercentageChange(newRegistrationLastYearStudents, newRegistrationStudents);
       if(newRegistrationPercentageChange >= 0) {
         $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newRegistrationPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-new-registration-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newRegistrationPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }

     if(newAdmissionLastYearStudents == 0) {
       $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-secondary\"><i class=\"mdi mdi-arrow-bottom-right\"></i>-</span><span class=\"text-muted\"> from last year</span>");
     } else {
       var newAdmissionPercentageChange = getPercentageChange(newAdmissionLastYearStudents, newAdmissionStudents);
       if(newAdmissionPercentageChange >= 0) {
         $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-success\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newAdmissionPercentageChange) + "%<i class=\"bi bi-arrow-up\"></i></span><span class=\"text-muted\"> from last year</span>");
       } else {
         $("#total-new-admissions-count-badge-value").html("<span class=\"badge badge-soft-danger\"><i class=\"mdi mdi-arrow-bottom-right\"></i>" + Math.abs(newAdmissionPercentageChange) + "%<i class=\"bi bi-arrow-down\"></i></span><span class=\"text-muted\"> from last year</span>");
       }
     }
   },

   renderNewAdmissionStudentChart : function (labelArr, newAdmissionCount) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 20;
     };
     // Bar chart
     var newAdmissionChart = new Chart($("#chartjs-new-admission-student-bar-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [
           {
            label: "New Admission",
            backgroundColor: LOGO_COLOR,
            borderColor: LOGO_COLOR,
            hoverBackgroundColor: LOGO_COLOR,
            hoverBorderColor: LOGO_COLOR,
            data: newAdmissionCount,
            barPercentage: .325,
            categoryPercentage: .5
          }
    ]},
   options: {
     responsive: !window.MSInputMethodContext,
     maintainAspectRatio: false,
     legend: {
       display: false,
     },
     scales: {
       yAxes: [{
         ticks: {
              beginAtZero: true
         },
         gridLines: {
           display: false
         },
         stacked: false,
         stacked: true,
       }],
       xAxes: [{
         stacked: false,
         gridLines: {
           color: "transparent"
         },
         stacked: true,
       }]
     }
   }
  });
  newAdmissionChart.canvas.parentNode.style.height = '311px';
  newAdmissionChart.canvas.parentNode.style.width = '311px';
   },

   renderStudentCategoryCountChart : function (labelArr, studentCategoryList, totalStudentList) {

      new Chart($("#chartjs-categorywise-student-bar-distribution"), {
        type: 'horizontalBar',
        data: {
          labels: labelArr,
          datasets: [
            {
             label: "Category Student",
             backgroundColor: LOGO_COLOR,
             borderColor: LOGO_COLOR,
             hoverBackgroundColor: LOGO_COLOR,
             hoverBorderColor: LOGO_COLOR,
             data: studentCategoryList,
             barPercentage: .325,
             categoryPercentage: .5
           },
           {
            label: "Other Students",
            backgroundColor: LIGHT_GREY_COLOR,
            borderColor: LIGHT_GREY_COLOR,
            hoverBackgroundColor: LIGHT_GREY_COLOR,
            hoverBorderColor: LIGHT_GREY_COLOR,
            data: totalStudentList,
            barPercentage: .325,
            categoryPercentage: .5
          }
     ]},
        options: {
            responsive: !window.MSInputMethodContext,
            maintainAspectRatio: true,
            legend: {
              display: false
            },
            tooltips: {
             enabled: true,
             mode: 'single',
             displayColors: false,
             callbacks: {
               label: function(tooltipItems, data) {
                 return  " : " + data.datasets[0].data[tooltipItems.index] + '';
               }
             }
           },
            scales: {
              yAxes: [{
                ticks: {
                     beginAtZero: true
                },
                gridLines: {
                  display: false
                },
                stacked: false,
                stacked: true,
              }],
              xAxes: [{
                stacked: false,
                gridLines: {
                  color: "transparent"
                },
                stacked: true,
              }]
            }
        }
      });
   },

   renderStudentGenderCountChart : function (labelArr, newAdmissionMale, newAdmissionFemale, oldStudentsMale, oldStudentsFemale) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 20;
     };
     // Bar chart
     new Chart($("#chartjs-genderwise-student-bar-distribution"), {
       type: "bar",
       data: {
         labels: labelArr,
         datasets: [
           {
            label: "Old Student Male",
            backgroundColor: LOGO_COLOR,
            borderColor: LOGO_COLOR,
            hoverBackgroundColor: LOGO_COLOR,
            hoverBorderColor: LOGO_COLOR,
            data: oldStudentsMale,
            barPercentage: .325,
            categoryPercentage: .5
          },
          {
           label: "Old Student Female",
           backgroundColor: OLD_STUDENTS_FEMALE_COLOR,
           borderColor: OLD_STUDENTS_FEMALE_COLOR,
           hoverBackgroundColor: OLD_STUDENTS_FEMALE_COLOR,
           hoverBorderColor: OLD_STUDENTS_FEMALE_COLOR,
           data: oldStudentsFemale,
           barPercentage: .325,
           categoryPercentage: .5
         },
         {
					label: "New Admission Male",
					backgroundColor: NEW_ADMISSION_MALE_COLOR,
					borderColor: NEW_ADMISSION_MALE_COLOR,
					hoverBackgroundColor: NEW_ADMISSION_MALE_COLOR,
					hoverBorderColor: NEW_ADMISSION_MALE_COLOR,
					data: newAdmissionMale,
					barPercentage: .325,
					categoryPercentage: .5
				},
        {
         label: "New Admission Female",
         backgroundColor: NEW_ADMISSION_FEMALE_COLOR,
         borderColor: NEW_ADMISSION_FEMALE_COLOR,
         hoverBackgroundColor: NEW_ADMISSION_FEMALE_COLOR,
         hoverBorderColor: NEW_ADMISSION_FEMALE_COLOR,
         data: newAdmissionFemale,
         barPercentage: .325,
         categoryPercentage: .5
       }
    ]},
   options: {
     responsive: !window.MSInputMethodContext,
     maintainAspectRatio: true,
     cornerRadius: 15,
     legend: {
       display: true,
       position: 'top',
       align: 'start',
       labels:{
         boxWidth: 12,
       }
     },
     scales: {
       yAxes: [{
         ticks: {
              beginAtZero: true
         },
         gridLines: {
           display: false
         },
         stacked: false,
         stacked: true,
       }],
       xAxes: [{
         stacked: false,
         gridLines: {
           color: "transparent"
         },
         stacked: true,
       }]
     }
   }
  });
   },

   renderRTEStudentsPieChart : function (labelArr, dataArr) {
     Chart.Legend.prototype.afterFit = function() {
       this.height = this.height + 40;
     };
     var rteChart = new Chart($("#chartjs-rte-student-pie"), {
       type: "pie",
       data: {
         labels: labelArr,
         datasets: [{
           data: dataArr,
           backgroundColor: [
             LOGO_COLOR,
             LIGHT_GREY_COLOR
           ],
           borderWidth: 1,
           borderColor: window.theme.white
         }]
       },
       options: {
         responsive: !window.MSInputMethodContext,
         maintainAspectRatio: false,
         cutoutPercentage: 75,
         legend: {
           display: true,
           position: 'top',
           align: 'center',
           labels:{
             boxWidth: 12,
             padding: 10,
           }
         },
       }
     });
     rteChart.canvas.parentNode.style.height = '250px';
     rteChart.canvas.parentNode.style.width = '311px';
   },
};

function previousTabSwtichingEvent(){
  $('.previous-switch-tab').on('click', function () {
    var tabPrev = $('.switch-tab.active').prev();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabPrev.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabPrev.removeClass('disabled').addClass('active');
  });
}

function nextTabSwtichingEvent(){
  $('.next-switch-tab').on('click', function () {
    switchToNextTab();
  });
}

function switchToNextTab(){
    var tabNext = $('.switch-tab.active').next();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabNext.removeClass('disabled').addClass('active');
}

function swtichingTostudentUpdateFormFirstTab(){
    var tabNext = $('.switch-tab').first();
    $('.switch-tab').removeClass('active');
    $('.switch-tab').removeClass('disabled');
    tabNext.trigger("click");
    // $('.switch-tab').addClass('disabled');
    tabNext.removeClass('disabled').addClass('active');
}

function closeModal(){
  clearMandatoryFieldsErrorDisplay();
}

function returnToMainScreen() {
  var screenVal = $('#screen-name').text().trim();
  if(screenVal == 'ADMIT-STUDENT'){
    loadAdmitStudentMenu();
  } else{
    searchStudents(true);
    $('.student-details-screen').attr('style','display:none');
    $('#student-details').attr('style','display:block');
    swtichingTostudentUpdateFormFirstTab();
  }
}

function fillStudentSections(classId) {
  var standardsJson = $('p.student-standard-json').first().text().trim();
  var standards = JSON.parse(standardsJson);
  var classSectionMap = {}

  for(var i = 0 ; i < standards.length ; i++){
      var standard = standards[i]
      classSectionMap[standard.standardId] = standard
  }

  if(!(classId in classSectionMap)){
      return;
  }
  var options = "<option value=\"\"></option>"
  var standard = classSectionMap[classId];
  for(var i = 0 ; i < standard.standardSectionList.length; i++){
      options += "<option value=\""+standard.standardSectionList[i].sectionId+"\">"+standard.standardSectionList[i].sectionName+"</option>"
  }
  $('.student-section').html(options);
}

function fillStudentAcademicSession(academicSession) {
  var options = "<option value=\""+academicSession.academicSessionId+"\">"+academicSession.displayName+"</option>"
  $('#update-student-academic-session').html(options);
}

function fillStudentInformation(studentInfo) {
    fillStudentAcademicSession(studentInfo.studentAcademicSessionInfoResponse.academicSession);
    $("#update-admission-student-id").text(studentInfo.studentId);
    $("#update\\.student-registration-number").val(studentInfo.studentBasicInfo.registrationNumber);
    $("#update\\.student-admission-number").val(studentInfo.studentBasicInfo.admissionNumber);
    $("#update\\.pen-number").val(studentInfo.studentBasicInfo.penNumber);
    $("#update\\.apaar-id-number").val(studentInfo.studentBasicInfo.apaarIdNo);
    // $("#update-student-academic-session").val(studentInfo.studentAcademicSessionInfoResponse.academicSession.academicSessionId);
    $("#update-student-class").val(studentInfo.studentAcademicSessionInfoResponse.standard.standardId);
    fillStudentSections(studentInfo.studentAcademicSessionInfoResponse.standard.standardId);
    if(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList != null && studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList.length > 0){
      $("#update-student-section").val(studentInfo.studentAcademicSessionInfoResponse.standard.standardSectionList[0].sectionId);
    }
    $('#update\\.student-name').val(studentInfo.studentBasicInfo.name);
    $('#update\\.student-gender').val(studentInfo.studentBasicInfo.gender);
    setFormattedDate(studentInfo.studentBasicInfo.dateOfBirth, "#update\\.student-date-of-birth");
    $('#update\\.student-birth-place').val(studentInfo.studentBasicInfo.birthPlace);
    $('#update\\.student-category').val(studentInfo.studentBasicInfo.userCategory);
    $('#update\\.student-religion').val(studentInfo.studentBasicInfo.religion);
    $('#update\\.student-caste').val(studentInfo.studentBasicInfo.caste);
    $('#update\\.student-rte').prop("checked", studentInfo.studentBasicInfo.rte);
    $("#update\\.student-mother-tongue").val(studentInfo.studentBasicInfo.motherTongue);
    $("#update\\.student-area-type").val(studentInfo.studentBasicInfo.areaType);
    $("#update\\.student-specially-abled").prop("checked", studentInfo.studentBasicInfo.speciallyAbled);
    $("#update\\.student-bpl").prop("checked", studentInfo.studentBasicInfo.bpl);
    $('#update\\.student-present-address').val(studentInfo.studentBasicInfo.presentAddress);
    $('#update\\.student-present-city').val(studentInfo.studentBasicInfo.presentCity);
    $('#update\\.student-present-state').find(".country-states").val(studentInfo.studentBasicInfo.presentState);
    $('#update\\.student-present-post-office').val(studentInfo.studentBasicInfo.presentPostOffice);
    $('#update\\.student-present-police-station').val(studentInfo.studentBasicInfo.presentPoliceStation);
    $('#update\\.student-present-zipcode').val(studentInfo.studentBasicInfo.presentZipcode);
    $('#update\\.student-permanent-address').val(studentInfo.studentBasicInfo.permanentAddress);
    $('#update\\.student-city').val(studentInfo.studentBasicInfo.permanentCity);
    $('#update\\.student-state').find(".country-states").val(studentInfo.studentBasicInfo.permanentState);
    $('#update\\.student-post-office').val(studentInfo.studentBasicInfo.permanentPostOffice);
    $('#update\\.student-police-station').val(studentInfo.studentBasicInfo.permanentPoliceStation);
    $('#update\\.student-zipcode').val(studentInfo.studentBasicInfo.permanentZipcode);
    $('#update\\.student-aadhar-number').val(studentInfo.studentBasicInfo.aadharNumber);
    setFormattedDate(studentInfo.studentBasicInfo.registrationDate, "#update\\.student-registration-date");
    setFormattedDate(studentInfo.studentBasicInfo.admissionDate, "#update\\.student-admission-date");
    $('#update\\.student-primary-contact-number').val(studentInfo.studentBasicInfo.primaryContactNumber);
    $('#update\\.student-primary-email').val(studentInfo.studentBasicInfo.primaryEmail);
    $('#update\\.student-whatsapp-number').val(studentInfo.studentBasicInfo.whatsappNumber);
    $("#update\\.student-is-sponsored").prop("checked", studentInfo.studentBasicInfo.sponsored);
    $("#update\\.student-new-admission").prop("checked", studentInfo.newAdmission);
    $("#update\\.student-hosteller").prop("checked", studentInfo.studentBasicInfo.hosteller);
    $('#update\\.student-nationality').val(studentInfo.studentBasicInfo.nationality);

    // if(studentInfo.studentImage != null){
    //     $('#update-student-photo-label').html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>");
    // }else{
    //     $('#update-student-photo-label').html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>");
    // }

    $('#update\\.student-house').val(studentInfo.studentBasicInfo.instituteHouse == null ? "" : studentInfo.studentBasicInfo.instituteHouse.houseId);
    $('#update\\.student-admission-in-class').val(studentInfo.studentBasicInfo.admissionInClass);
    $('#update\\.student-name-as-per-aadhar').val(studentInfo.studentBasicInfo.studentNameAsPerAadhar);
    var childCategoryCriteria = "NA";
    if(!(studentInfo.studentBasicInfo.childCategoryCriteria === "" || studentInfo.studentBasicInfo.childCategoryCriteria === null || studentInfo.studentBasicInfo.childCategoryCriteria === undefined)) {
      childCategoryCriteria = studentInfo.studentBasicInfo.childCategoryCriteria;
    }
    $('#update\\.child-category-criteria').val();
    $('#update\\.specially-abled-type').val(studentInfo.studentBasicInfo.speciallyAbledType);

    $('#student-mother-name').val(studentInfo.studentFamilyInfo.mothersName);
    $('#student-father-name').val(studentInfo.studentFamilyInfo.fathersName);
    $('#student-mother-qualification').val(studentInfo.studentFamilyInfo.mothersQualification);
    $('#student-father-qualification').val(studentInfo.studentFamilyInfo.fathersQualification);
    $('#mother-contact-number').val(studentInfo.studentFamilyInfo.mothersContactNumber);
    $('#father-contact-number').val(studentInfo.studentFamilyInfo.fathersContactNumber);
    $('#mother-occupation').val(studentInfo.studentFamilyInfo.mothersOccupation);
    $('#mother-annual-income').val(studentInfo.studentFamilyInfo.mothersAnnualIncome);
    $('#father-occupation').val(studentInfo.studentFamilyInfo.fathersOccupation);
    $('#father-annual-income').val(studentInfo.studentFamilyInfo.fathersAnnualIncome);
    $('#mother-aadhar-number').val(studentInfo.studentFamilyInfo.mothersAadharNumber);
    $('#father-aadhar-number').val(studentInfo.studentFamilyInfo.fathersAadharNumber);
    $('#mother-pan-card-details').val(studentInfo.studentFamilyInfo.mothersPanCardDetails);
    $('#father-pan-card-details').val(studentInfo.studentFamilyInfo.fathersPanCardDetails);
    $('#approx-family-income').val(studentInfo.studentFamilyInfo.approxFamilyIncome);

    if(studentInfo.studentGuardianInfoList != null && studentInfo.studentGuardianInfoList.length > 0){
      $('#guardian-name').val(studentInfo.studentGuardianInfoList[0].guardianName);
      $('#guardian-relation').val(studentInfo.studentGuardianInfoList[0].relation);
      $('#guardian-age').val(studentInfo.studentGuardianInfoList[0].age);
      $('#guardian-gender').val(studentInfo.studentGuardianInfoList[0].gender);
      $('#guardian-occupation').val(studentInfo.studentGuardianInfoList[0].occupation);
      $('#guardian-email').val(studentInfo.studentGuardianInfoList[0].email);
      $('#guardian-contact-number').val(studentInfo.studentGuardianInfoList[0].contactNumber);
      $('#guardian-address').val(studentInfo.studentGuardianInfoList[0].address);
      $('#guardian-city').val(studentInfo.studentGuardianInfoList[0].city);
      $('#guardian-state').find(".country-states").val(studentInfo.studentGuardianInfoList[0].state);
      $('#guardian-zipcode').val(studentInfo.studentGuardianInfoList[0].zipcode);
    }
    $('#previous-school-name').val(studentInfo.studentPreviousSchoolInfo.schoolName);
    $('#previous-school-medium').val(studentInfo.studentPreviousSchoolInfo.medium);
    $('#class-passed').val(studentInfo.studentPreviousSchoolInfo.classPassed);
    $('#year-of-passing').val(studentInfo.studentPreviousSchoolInfo.yearOfPassing);
    $('#result').val(studentInfo.studentPreviousSchoolInfo.result);
    $('#percentage').val(studentInfo.studentPreviousSchoolInfo.percentage);
    $("#student-is-admission-tc-based").prop("checked", studentInfo.studentPreviousSchoolInfo.admissionTcBased);
    $('#previous-school-tc-number').val(studentInfo.studentPreviousSchoolInfo.tcNumber);

    $('#blood-group').val(studentInfo.studentMedicalInfo.bloodGroup);
    $('#blood-pressure').val(studentInfo.studentMedicalInfo.bloodPressure);
    $('#pulse').val(studentInfo.studentMedicalInfo.pulse);
    $('#height').val(studentInfo.studentMedicalInfo.height);
    $('#weight').val(studentInfo.studentMedicalInfo.weight);
    setFormattedDate(studentInfo.studentMedicalInfo.dateOfPhysicalExamination, '#date-of-physical-examination');

    var studentDocumentsMap = {};
    if(studentInfo.studentDocuments != null && studentInfo.studentDocuments.length > 0){
      for(var i = 0 ; i < studentInfo.studentDocuments.length; i++){
        var studentDocument = studentInfo.studentDocuments[i];
        studentDocumentsMap[studentDocument.documentType] = studentDocument;
      }
    }

    fillStudentDocumentsInformation(studentDocumentsMap);

}

function fillStudentDocumentsInformation(studentDocumentsMap) {
  var container = $("#student-document-information-table");
  var html = "";
  for(var i = 0 ; i < STUDENT_DOCUMENT_TYPE.length; i++){
    var documentType = STUDENT_DOCUMENT_TYPE[i];
    var studentDocument = studentDocumentsMap[documentType];

    html += "<tr class=\"student-document-row\" id=" + documentType + "><td>" + documentType + "</td>";

    var documentUploadedText = "No";
    var documentCollected = false;
    var checked = "";
    var disabled = "";
    if(studentDocument != null && studentDocument != undefined) {
      documentCollected = true;
      checked = "checked";
      disabled = studentDocument.documentNotUploaded ? "" : "disabled";
      documentUploadedText = studentDocument.documentNotUploaded ? "No" : "Yes";
    }
    html += "<td><div class=\"custom-control custom-switch\"><input type=\"checkbox\" class=\"custom-control-input student-document-collected-checkbox\" id=\"student-document-checkbox-" + i + "\"" + checked + " " + disabled + "><label class=\"custom-control-label\" for=\"student-document-checkbox-" + i + "\"></label></div></td><td>" + documentUploadedText + "</td>";
    html += "</tr>";
  }

  $(container).html(html);
}

function studentDocumentWarningPopup() {
  $("#student-document-warning-popup").modal('toggle');
}



var admissionReports = {

    dataCache : {
    },

    loadMainScreen: function() {
      ajaxClient.get("/admission/reports", function(data) {
          $("#main-content").html(data);
          // $('.selectpicker').selectpicker();
          initSelect2();
          initDateInput();
          commonUtils.bindCardHoverEvent();
          commonUtils.bindReportCardClickEvent();
          reportUtils.bindSelectClassCheckboxEvent();
          admissionReports.bindGenerateReportEvent();
          admissionReports.loadOnChangeEventsFilter();
          admissionReports.checkboxEvents();
          admissionReports.dataCache.filterationCriteria = new Object();
      });
    },

    loadOnChangeEventsFilter : function () {
      $('select.filter-gender').on('change', function(){
          admissionReports.dataCache.filterationCriteria.gender = $(this).val();
      });
      $('select.filter-religion').on('change', function(){
          admissionReports.dataCache.filterationCriteria.religion = $(this).val();
      });
      $('select.filter-category').on('change', function(){
          admissionReports.dataCache.filterationCriteria.category = $(this).val();
      });
      $('select.filter-area-type').on('change', function(){
          admissionReports.dataCache.filterationCriteria.areaType = $(this).val();
      });
      $('select.filter-specially-abled').on('change', function(){
          admissionReports.dataCache.filterationCriteria.speciallyAbled = $(this).val();
      });
      $('select.filter-rte').on('change', function(){
          admissionReports.dataCache.filterationCriteria.rte = $(this).val();
      });
      $('select.filter-bpl').on('change', function(){
          admissionReports.dataCache.filterationCriteria.bpl = $(this).val();
      });
      $('select.filter-states').on('change', function(){
          admissionReports.dataCache.filterationCriteria.states = $(this).val();
      });
      $('select.reports-student-class').on('change', function(){
          admissionReports.dataCache.filterationCriteria.standards = $(this).val();
      });
      $('select.filter-house').on('change', function(){
          admissionReports.dataCache.filterationCriteria.instituteHouse = $(this).val();
      });
    },

    bindGenerateReportEvent: function() {
      $('.report-academic-session').on('change', function(){
        var ref = this;
        var academicSessionId = $(ref).val();
        ajaxClient.get("/admission/report-session-change/" + academicSessionId , function(data) {
            $(ref).parent().parent().parent().find(".class-attendance-type-div").html(data);
            initSelect2();
            admissionReports.loadOnChangeEventsFilter();
        });
      });

      // $(".generate-student-report").on('click', function() {
      //   $(this).closest('div.modal').modal('toggle');
      //   var containerElement = $(this).closest('div.report-field-container');
      //   var academicSession = 0;
      //   if($(containerElement).find(".report-academic-session option:selected").length != 0){
      //     academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      //   };
      //   var reportType = $(containerElement).find('p.report-type').text().trim();
      //   var downloadFormat = "EXCEL";
      //   var hasDownloadFormatClass = $(containerElement).find('.download-format').length > 0;
      //   if (hasDownloadFormatClass){
      //     downloadFormat = $(containerElement).find('.download-format').val();
      //   }
      //
      //   window.open(baseURL+"/admission/generate-student-report?academicSession="+academicSession+"&reportType="+reportType+"&downloadFormat="+downloadFormat, '_blank')
      //
      // });

      // $(".admission-generate-report").on('click', function() {
      //
      //   var gender = "";
      //   if(admissionReports.dataCache.filterationCriteria.gender != undefined && admissionReports.dataCache.filterationCriteria.gender != null) {
      //     gender = admissionReports.dataCache.filterationCriteria.gender.join(",");
      //   }
      //
      //   var religion = "";
      //   if(admissionReports.dataCache.filterationCriteria.religion != undefined && admissionReports.dataCache.filterationCriteria.religion != null) {
      //     religion = admissionReports.dataCache.filterationCriteria.religion.join(",");
      //   }
      //
      //   var category = "";
      //   if(admissionReports.dataCache.filterationCriteria.category != undefined && admissionReports.dataCache.filterationCriteria.category != null) {
      //     category = admissionReports.dataCache.filterationCriteria.category.join(",");
      //   }
      //
      //   var areaType = "";
      //   if(admissionReports.dataCache.filterationCriteria.areaType != undefined && admissionReports.dataCache.filterationCriteria.areaType != null) {
      //     areaType = admissionReports.dataCache.filterationCriteria.areaType.join(",");
      //   }
      //
      //   var speciallyAbled = "";
      //   if(admissionReports.dataCache.filterationCriteria.speciallyAbled != undefined && admissionReports.dataCache.filterationCriteria.speciallyAbled  != null) {
      //     speciallyAbled = admissionReports.dataCache.filterationCriteria.speciallyAbled;
      //   }
      //
      //   var rte = "";
      //   if(admissionReports.dataCache.filterationCriteria.rte != undefined && admissionReports.dataCache.filterationCriteria.rte != null) {
      //     rte = admissionReports.dataCache.filterationCriteria.rte;
      //   }
      //
      //   var bpl = "";
      //   if(admissionReports.dataCache.filterationCriteria.bpl != undefined && admissionReports.dataCache.filterationCriteria.bpl != null) {
      //     bpl = admissionReports.dataCache.filterationCriteria.bpl;
      //   }
      //
      //   var states = "";
      //   if(admissionReports.dataCache.filterationCriteria.states != undefined && admissionReports.dataCache.filterationCriteria.states != null) {
      //     states = admissionReports.dataCache.filterationCriteria.states.join(",");
      //   }
      //
      //   var requiredStandards = "";
      //   if(admissionReports.dataCache.filterationCriteria.standards != undefined && admissionReports.dataCache.filterationCriteria.standards != null) {
      //     requiredStandards = admissionReports.dataCache.filterationCriteria.standards.join(",");
      //   }
      //
      //   var house = "";
      //   if(admissionReports.dataCache.filterationCriteria.instituteHouse != undefined && admissionReports.dataCache.filterationCriteria.instituteHouse != null) {
      //       house = admissionReports.dataCache.filterationCriteria.instituteHouse.join(",");
      //   }
      //
      //   $(this).closest('div.modal').modal('toggle');
      //   var containerElement = $(this).closest('div.report-field-container');
      //
      //   var reportType = $(containerElement).find('p.report-type').text().trim();
      //
      //   var studentStatus = "";
      //   if($(containerElement).find(".student-status").length > 0){
      //         studentStatus = $(containerElement).find(".student-status").val().join();
      //   }
      //
      //   var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
      //   var academicSession = 0;
      //   if($(containerElement).find(".report-academic-session option:selected").length != 0){
      //     academicSession = $(containerElement).find(".report-academic-session option:selected").val();
      //   };
      //
      //   admissionReports.dataCache.filterationCriteria = new Object();
      //   var downloadFormat = $(containerElement).find('.download-format').val();
      //   if(downloadFormat === undefined) {
      //     downloadFormat = "";
      //   } else {
      //     downloadFormat = downloadFormat.trim();
      //   }
      //
      //   var newAdmission = false;
      //   if($(containerElement).find("input.new-admission").is(":checked")){
      //     newAdmission = true;
      //   }
      //
      //   if(downloadFormat == "PDF" && requiredHeadersCSV.split(",").length > 20){
      //     showErrorDialogBox("Please select atleast 20 columns for the Student Details PDF report and try again.")
      //     return;
      //   }
      //
      //   window.open(baseURL+"/admission/generate-report?reportType="+reportType+"&academicSession="+academicSession+
      //   "&requiredHeaders="+requiredHeadersCSV+"&requiredStandards="+requiredStandards+"&rte="+
      //   rte+"&category="+category+"&gender="+gender+"&religion="+religion+"&area_type="+areaType+
      //   "&specially_abled="+speciallyAbled+"&bpl="+bpl+"&state="+states+"&instituteHouseId="+house+"&downloadFormat="+downloadFormat+"&studentStatus="+studentStatus+"&newAdmission="+newAdmission, '_blank');
      // });

      $(".generate-report").on('click', function() {
        var gender = "";
        if(admissionReports.dataCache.filterationCriteria.gender != undefined && admissionReports.dataCache.filterationCriteria.gender != null) {
          gender = admissionReports.dataCache.filterationCriteria.gender.join(",");
        }

        var religion = "";
        if(admissionReports.dataCache.filterationCriteria.religion != undefined && admissionReports.dataCache.filterationCriteria.religion != null) {
          religion = admissionReports.dataCache.filterationCriteria.religion.join(",");
        }

        var category = "";
        if(admissionReports.dataCache.filterationCriteria.category != undefined && admissionReports.dataCache.filterationCriteria.category != null) {
          category = admissionReports.dataCache.filterationCriteria.category.join(",");
        }

        var areaType = "";
        if(admissionReports.dataCache.filterationCriteria.areaType != undefined && admissionReports.dataCache.filterationCriteria.areaType != null) {
          areaType = admissionReports.dataCache.filterationCriteria.areaType.join(",");
        }

        var speciallyAbled = "";
        if(admissionReports.dataCache.filterationCriteria.speciallyAbled != undefined && admissionReports.dataCache.filterationCriteria.speciallyAbled  != null) {
          speciallyAbled = admissionReports.dataCache.filterationCriteria.speciallyAbled;
        }

        var rte = "";
        if(admissionReports.dataCache.filterationCriteria.rte != undefined && admissionReports.dataCache.filterationCriteria.rte != null) {
          rte = admissionReports.dataCache.filterationCriteria.rte;
        }

        var bpl = "";
        if(admissionReports.dataCache.filterationCriteria.bpl != undefined && admissionReports.dataCache.filterationCriteria.bpl != null) {
          bpl = admissionReports.dataCache.filterationCriteria.bpl;
        }

        var states = "";
        if(admissionReports.dataCache.filterationCriteria.states != undefined && admissionReports.dataCache.filterationCriteria.states != null) {
          states = admissionReports.dataCache.filterationCriteria.states.join(",");
        }

        var house = "";
        if(admissionReports.dataCache.filterationCriteria.instituteHouse != undefined && admissionReports.dataCache.filterationCriteria.instituteHouse != null) {
            house = admissionReports.dataCache.filterationCriteria.instituteHouse.join(",");
        }

        $(this).closest('div.modal').modal('toggle');
        var containerElement = $(this).closest('div.report-field-container');
        var reportType = $(containerElement).find('p.report-type').text().trim();

        var requiredHeadersCSV = reportUtils.getReportHeadersCSV(containerElement);
        admissionReports.dataCache.filterationCriteria = new Object();
        var downloadFormat = $(containerElement).find('.download-format').val();
        if(downloadFormat === undefined) {
          downloadFormat = "";
        } else {
          downloadFormat = downloadFormat.trim();
        }

        var newAdmission = false;
        if($(containerElement).find("input.new-admission").is(":checked")){
          newAdmission = true;
        }

        if(downloadFormat == "PDF" && requiredHeadersCSV.split(",").length > 20){
          showErrorDialogBox("Please select less than 20 columns for the Student Details PDF report and try again.")
          return;
        }

        var academicSession = 0;
        if($(containerElement).find(".report-academic-session option:selected").length != 0) {
          academicSession = $(containerElement).find(".report-academic-session option:selected").val();
        };

        var requiredStandards = "";
        if($(containerElement).find(".reports-student-class").length > 0) {
          requiredStandards = $(containerElement).find(".reports-student-class").val().join();
        }

        var studentStatus = "";
        if($(containerElement).find(".student-status").length > 0) {
              studentStatus = $(containerElement).find(".student-status").val().join();
        }

        var finalStudentStatus = "";
        if($(containerElement).find(".final-student-status").length > 0) {
          finalStudentStatus = $(containerElement).find(".final-student-status").val().join();
        }

        var documentTypes = "";
        if($(containerElement).find(".document-types").length > 0) {
            documentTypes = $(containerElement).find(".document-types").val().join();
        }

        var documentStatus = "";
        if($(containerElement).find(".document-status option:selected").length != 0) {
          documentStatus = $(containerElement).find(".document-status option:selected").val();
        };

        window.open(baseURL+"/admission/generate-report/" + academicSession + "?requiredStandards="+requiredStandards+"&studentStatus="+studentStatus+"&finalStudentStatus="+finalStudentStatus+"&documentTypes="+documentTypes+"&documentStatus="+documentStatus + "&reportType="+reportType + "&requiredHeaders="+requiredHeadersCSV+"&rte="+
        rte+"&category="+category+"&gender="+gender+"&religion="+religion+"&area_type="+areaType+
        "&specially_abled="+speciallyAbled+"&bpl="+bpl+"&state="+states+"&instituteHouseId="+house+"&newAdmission="+newAdmission, '_blank');

      });

    },

    resetPopup : function () {
      // Did not find the way to deselect all
      // $(".filter-gender").select2("val", "").trigger();
      // $(".filter-religion").selectpicker('deselectAll');
      // $(".filter-category").selectpicker('deselectAll');
      // $(".filter-area-type").selectpicker('deselectAll');
      // $(".filter-specially-abled").selectpicker('val', '');
      // $(".filter-rte").selectpicker('val', '');
      // $(".filter-bpl").selectpicker('val', '');
      // $(".filter-states").selectpicker('deselectAll');
      admissionReports.resetCheckBoxes();
    },

    resetCheckBoxes : function () {
      $( ".parent" ).prop( "checked", false );
      $( ".child" ).prop( "checked", false );
      $( ".basic" ).prop( "checked", true );
      $( ".basic-report-column" ).prop( "checked", true );

    },

    checkboxEvents : function () {
      $('.child').on('change', function(){
        $(this).parent().parent().parent().find($(".parent")).prop( "checked", false );
      });
      $('.parent').on('change', function(){
        if($(this).prop("checked")){
          $(this).parent().parent().find($(".child")).prop( "checked", true );
        }
        else if(!($(this).prop("checked"))){
          $(this).parent().parent().find($(".child")).each(function() {
            if (!$(this).is(':disabled')) {
                $(this).prop( "checked", false );
            }
          });
        }
      });
    }
}

var transferCertificate = {

  removeTCStudentCourses : function(ref) {
    $(ref).parent().remove();
  },

  addTCStudentCourses : function() {
    var textFieldHTML = "<div class=\"input-group mb-3 col-md-4\"><input type=\"text\" class=\"form-control tc-student-courses\" placeholder=\"Subject...\"><div class=\"input-group-prepend\" style=\"cursor:pointer;\" onclick=\"transferCertificate.removeTCStudentCourses(this);\"><span class=\"input-group-text\" aria-hidden=\"true\">x</span></div></div>";
    $("#tc-student-courses-div").append(textFieldHTML);
  },

  getTCSubject : function() {
    var course_list = [];
    $(".tc-student-courses").each(function() {
      course_list.push($(this).val());
    });
    return course_list.join(",")
  },

  isValidData : function (data) {
      return data != null && data != undefined && data != "";
  },

  getTcStudentSessionDetails : function() {
  var tcPreviousSessionDetailSummaryList = [];
   $(".tc-previous-session-detail-summary-list").each(function() {

        var previousAdmissionClass = $(this).find("#tc-previous-admission-class").val();
        var previousAdmissionDate = "";

         var admissionDateVal =  getDate($(this).find("#tc-previous-admission-date").val());
       if (transferCertificate.isValidData(admissionDateVal)) {
           previousAdmissionDate = admissionDateVal.getTime() / 1000;
       }

        var admissionPromotionClass = $(this).find("#tc-admission-promotion-class").val();
        var admissionPromotionDate = "";
        var admissionPromotionDateVal =  getDate($(this).find("#tc-admission-promotion-date").val());
       if (transferCertificate.isValidData(admissionPromotionDateVal)) {
           admissionPromotionDate = admissionPromotionDateVal.getTime() / 1000;
       }
        var dateOfPassingFromSchool = "";
        var dateOfPassingFromSchoolVal = getDate($(this).find("#tc-date-of-passing-from-school").val());
        if (transferCertificate.isValidData(dateOfPassingFromSchoolVal)) {
                     dateOfPassingFromSchool = dateOfPassingFromSchoolVal.getTime()/1000;
        }
        var noOfMeeting = $(this).find("#tc-no-of-meeting").val();
        var noOfPresent = $(this).find("#tc-no-of-present").val();
        var rankInClass = $(this).find("#tc-rank-in-class").val();
        var resultDivisionFinalExam = $(this).find("#tc-result-division-final-exam").val();
        var medium = $(this).find("#tc-medium").val();
        var subjectsTaken = $(this).find("#tc-subjects-taken").val();
        var conductAndWorkInSession = $(this).find("#tc-conduct-and-work-in-session").val();
        var duration = $(this).find("#tc-duration-in-session").val();


       tcPreviousSessionDetailSummaryList.push({'previousAdmissionClass' : previousAdmissionClass, 'previousAdmissionDate' : previousAdmissionDate,'admissionPromotionClass': admissionPromotionClass, 'admissionPromotionDate': admissionPromotionDate, 'dateOfPassingFromSchool': dateOfPassingFromSchool, 'noOfMeeting': noOfMeeting, 'noOfPresent': noOfPresent,
        'rankInClass': rankInClass, 'resultDivisionFinalExam': resultDivisionFinalExam,  'medium': medium, 'subjectsTaken': subjectsTaken,
       'conductAndWorkInSession': conductAndWorkInSession, 'duration' : duration
       });
      });

      return tcPreviousSessionDetailSummaryList ;
  },


  generateStaticTC : function(studentId) {
    window.open(baseURL + "/admission/generate-pdf-tc/" + studentId, '_blank');
    returnToMainScreen();
  },

addTCStudentPrevSessionDetails: function() {
    var relievingVariablesHTML =
    "<div class=\"col-md-12 mb-3 tc-previous-session-detail-summary-list\">" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Previous Admission Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Previous AdmissionClass...\" id=\"tc-previous-admission-class\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Previous Admission Date</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Ad./Promo. Date...\" id=\"tc-previous-admission-date\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Promotion Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Promotion Class...\" id=\"tc-admission-promotion-class\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Promotion Date</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Promotion Date...\" id=\"tc-admission-promotion-date\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Date of Passing from School</label>" +
                "<input type=\"text\" class=\"form-control select-date\" placeholder=\"Date of Passing from School...\" id=\"tc-date-of-passing-from-school\">" +
            "</div>" +
        "</div>" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>No. of Meeting</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"No. of Meeting...\" id=\"tc-no-of-meeting\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>No of Present</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"No. of Present...\" id=\"tc-no-of-present\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Result and Division in Final Exam</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Result and Division in Final Exam...\" id=\"tc-result-division-final-exam\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Subjects taken</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Subjects taken...\" id=\"tc-subjects-taken\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Rank in Class</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Rank in Class...\" id=\"tc-rank-in-class\">" +
            "</div>" +
        "</div>" +
        "<div class=\"row\">" +
            "<div class=\"mb-3 col-md-2\">" +
                "<label>Medium</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Medium...\" id=\"tc-medium\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-4\">" +
                "<label>Duration: Admission date to the end of this session</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Admission date to the end of this session...\" id=\"tc-duration-in-session\">" +
            "</div>" +
            "<div class=\"mb-3 col-md-3\">" +
                "<label>Conduct and Work in session</label>" +
                "<input type=\"text\" class=\"form-control\" placeholder=\"Conduct and Work in Session...\" id=\"tc-conduct-and-work-in-session\">" +
            "</div>" +
        "</div>" +
        "<div class=\"col-md-12\" style=\"cursor:pointer;\" onclick=\"transferCertificate.removeTCStudentCourses(this);\">" +
            "<p style=\"color: #FF0000; text-align: right;\"><u>Delete Session Detail</u></p>" +
        "</div>" +
    "</div>";

    $("#tc-previous-session-detail-summary-list-div").append(relievingVariablesHTML);
    initDateWithYearRange("-20:+5", false);
},

  generateTC : function(studentId){
    var book_number = $("#tc-book-no").val().trim();
    var tc_number = $("#tc-serial-no").val().trim();
    var is_tc_number_fixed = $("#tc-generation-is-tc-number-fixed").text().trim();
    var tc_generation_date = 0;
    var tc_generation_date_val = getDate($("#tc-generation-date").val());
    if(tc_generation_date_val != null && tc_generation_date_val != undefined && tc_generation_date_val != "") {
      tc_generation_date = tc_generation_date_val.getTime()/1000;
    }
    var affiliation_number = $("#tc-affiliation-no").val().trim();
    var dise_code = $("#tc-dise-code").val().trim();
    var school_code = $("#tc-school-code").val().trim();
    var primary_education_affiliation_number = $("#tc-pri-edu-affli-no").val().trim();
    var apaar_id_number = $("#tc-apaar-id-no").val().trim();
    var rte_affiliation_number = $("#tc-rte-affli-no").val().trim();

    var admission_number = $("#tc-admission-no").val().trim();
    var student_name = $("#tc-student-name").val().trim();
    var admission_date = 0;
    var admission_date_val = getDate($("#tc-admission-date").val());
    if(admission_date_val != null && admission_date_val != undefined && admission_date_val != "") {
      admission_date = admission_date_val.getTime()/1000;
    }
    var admission_class = $("#tc-admission-class").val().trim();
    var father_guardian_name = $("#tc-father-name").val().trim();
    var mother_name = $("#tc-mother-name").val().trim();
    var proofOfDoBAtTheTimeOfAdmission = $("#tc-proof-of-dob-at-the-time-of-admission").val().trim();
    var living_time_in_currect_city = $("#tc-living-time-in-currect-city").val().trim();
    var dob = 0;
    var dob_val = getDate($("#tc-dob").val());
    if(dob_val != null && dob_val != undefined && dob_val != "") {
      dob = dob_val.getTime()/1000;
    }
    var category = $("#tc-category").val().trim();
    var nationality = $("#tc-nationality").val().trim();

    var last_active_session_class = $("#tc-last-session-class").val().trim();
    var lastActiveSessionClassInWords = $("#tc-last-session-class-name-in-words").val().trim();
    var lastActiveSessionClassInFigures = $("#tc-last-session-class-name-in-figures").val().trim();
    var last_exam_taken_with_result = $("#tc-last-exam").val().trim();
    var number_of_time_exam_failed = $("#tc-last-exam-failed").val().trim();
    var scholastic_courses_last_active_session_str = transferCertificate.getTCSubject();
    var promotion_to_higher_class = $("#tc-getting-promotion").val().trim();
    var promoting_class_name = $("#tc-promoting-class").val().trim();
    var promotingClassNameInWords = $("#tc-promoting-class-name-in-words").val().trim();
    var promotingClassNameInFigures = $("#tc-promoting-class-name-in-figures").val().trim();
    var last_fees_paid = $("#tc-last-fees-paid").val().trim();
    var discount_with_nature = $("#tc-discount").val().trim();
    var total_working_days = $("#tc-total-working-days").val().trim();
    var total_attended_days = $("#tc-total-attended-days").val().trim();

    var ncc_cadet_boy_scout_girl_guide_with_details = $("#tc-ncc-cadet").val().trim();
    var co_curricular_activities = $("#tc-cocurricular-activities").val().trim();

    var code_of_conduct = $("#tc-code-of-conduct").val();
    var relieve_date = 0;
    var relieve_date_val = getDate($("#tc-relieve-date").val());
    if(relieve_date_val != null && relieve_date_val != undefined && relieve_date_val != "") {
      relieve_date = relieve_date_val.getTime()/1000;
      if(relieve_date > tc_generation_date) {
        showErrorDialogBox("Relieved Date cannot be greater than TC Generation Date.");
        return;
      }
    }
    var relieve_reason = $("#tc-relieve-reason").val().trim();
    var remarks = $("#tc-remarks").val().trim();
    var dateOfApplicationOfCertificate = 0;
    var date_of_application_of_certificate = getDate($("#tc-date-of-application-of-certificate").val());
    if(date_of_application_of_certificate != null && date_of_application_of_certificate != undefined && date_of_application_of_certificate != "") {
      dateOfApplicationOfCertificate = date_of_application_of_certificate.getTime()/1000;
    }
    var datePupilsNameStuckOffTheRolls = 0;
    var date_pupils_name_stuck_off_the_rolls = getDate($("#tc-date-pupils-name-stuck-off-the-rolls").val());
    if(date_pupils_name_stuck_off_the_rolls != null && date_pupils_name_stuck_off_the_rolls != undefined && date_pupils_name_stuck_off_the_rolls != "") {
      datePupilsNameStuckOffTheRolls = date_pupils_name_stuck_off_the_rolls.getTime()/1000;
    }

    var prev_session_details_list = JSON.stringify(transferCertificate.getTcStudentSessionDetails());
    var queryParamsStr = "?book_number="+book_number+"&tc_number="+tc_number+"&is_tc_number_fixed="+is_tc_number_fixed+"&tc_generation_date="+tc_generation_date+"&affiliation_number="+affiliation_number+"&dise_code="+dise_code+"&school_code="+school_code+"&primary_education_affiliation_number="+primary_education_affiliation_number+"&apaar_id_number="+apaar_id_number+"&rte_affiliation_number="+rte_affiliation_number+"&admission_number="+admission_number+"&student_name="+student_name+"&admission_date="+admission_date+"&admission_class="+admission_class+"&father_guardian_name="+father_guardian_name+"&mother_name="+mother_name+"&dob="+dob+"&category="+category+"&nationality="+nationality+"&last_active_session_class="+last_active_session_class+"&last_exam_taken_with_result="+last_exam_taken_with_result+"&number_of_time_exam_failed="+number_of_time_exam_failed+"&scholastic_courses_last_active_session_str="+scholastic_courses_last_active_session_str+"&promotion_to_higher_class="+promotion_to_higher_class+"&promoting_class_name="+promoting_class_name+"&last_fees_paid="+last_fees_paid+"&discount_with_nature="+discount_with_nature+"&total_working_days="+total_working_days+"&total_attended_days="+total_attended_days+"&ncc_cadet_boy_scout_girl_guide_with_details="+ncc_cadet_boy_scout_girl_guide_with_details+"&co_curricular_activities="+co_curricular_activities+"&code_of_conduct="+code_of_conduct+"&relieve_date="+relieve_date+"&relieve_reason="+relieve_reason+"&remarks="+remarks+"&proof_of_dob_at_the_time_of_admission="+proofOfDoBAtTheTimeOfAdmission+"&last_active_session_class_in_figures="+lastActiveSessionClassInFigures+"&last_active_session_class_in_words="+lastActiveSessionClassInWords+"&promoting_class_name_in_figures="+promotingClassNameInFigures+"&promoting_class_name_in_words="+promotingClassNameInWords+"&date_of_application_of_certificate="+dateOfApplicationOfCertificate+"&date_pupils_name_stuck_off_the_rolls="+datePupilsNameStuckOffTheRolls+"&prev_session_details_list="+prev_session_details_list+"&living_time_in_currect_city="+living_time_in_currect_city;
    console.log(queryParamsStr)
      window.open(baseURL + "/admission/pdf-tc/" + studentId + queryParamsStr, '_blank');
      returnToMainScreen();
  },

  clearTCVariables : function () {
    var isTCCounterEnabled = $("#tc-counter-enable").text();
    $("#tc-affiliation-no").val("");
    if(isTCCounterEnabled === 'false') {
      $("#tc-serial-no").val("");
    }
    $("#tc-book-no").val("");
    $("#tc-generation-date").val("");
    $("#tc-affiliation-no").val("");
    $("#tc-dise-code").val("");
    $("#tc-school-code").val("");
    $("#tc-pri-edu-affli-no").val("");
    $("#tc-apaar-id-no").val("");
    $("#tc-living-time-in-currect-city").val("");
    $("#tc-rte-affli-no").val("");
    $("#tc-admission-no").val("");
    $("#tc-student-name").val("");
    $("#tc-admission-date").val("");
    $("#tc-admission-class").val("");
    $("#tc-father-name").val("");
    $("#tc-mother-name").val("");
    $("#tc-proof-of-dob-at-the-time-of-admission").val("");
    $("#tc-dob").val("");
    $("#tc-category").val("");
    $("#tc-nationality").val("");
    $("#tc-last-session-class").val("");
    $("#tc-last-exam").val("");
    $("#tc-last-exam-failed").val("");
    $(".tc-student-courses").val("");
    $("#tc-getting-promotion").val("");
    $("#tc-promoting-class").val("");
    $("#tc-last-fees-paid").val("");
    $("#tc-discount").val("");
    $("#tc-total-working-days").val("");
    $("#tc-total-attended-days").val("");
    $("#tc-ncc-cadet").val("");
    $("#tc-cocurricular-activities").val("");
    $("#tc-code-of-conduct").val("");
    $("#tc-relieve-date").val("");
    $("#tc-relieve-reason").val("");
    $("#tc-remarks").val("");
  },

  resetTCVariables : function (studentId) {
    ajaxClient.get("/admission/student-tc-field-details/"+studentId, function(data) {
      $('#tc-outer-div').html(data);
      $("#tc-generation-student-id").text(studentId);
      initDateWithYearRange("-20:+5", false);
      initPastDateById("tc-generation-date", 2000);
      initPastDateById("tc-relieve-date", 2000);
    });
  },

};


var certificate = {

  loadStudentPaymentDetails : function (studentId) {
    var sessionId = academicSessionHandler.getSelectedSessionId();
    window.open(baseURL + "/admission/generate-study-certificate/" + sessionId + "/" + studentId, '_blank');
  }
}

function advanceSearch () {
  $("#advance-search-option-div").toggle();
}

/**
 * Calculates in percent, the change between 2 numbers.
 * e.g from 1000 to 500 = 50%
 *
 * @param oldNumber The initial value
 * @param newNumber The value that changed
 */
function getPercentageChange(oldNumber, newNumber){
  return parseFloat((((newNumber - oldNumber) * 100) / oldNumber).toFixed(1));
}

function getPercentage(number, total){
  return parseFloat(((number / total) * 100).toFixed(1));
}
function isObjectEmpty(object) {
  return Object.keys(object).length === 0 && object.constructor === Object;
}

var admissionEnquiry = {

    dataCache : {
       },

    loadMainScreen : function(actionType) {
        ajaxClient.get("/admission/enquiry-home", function(data) {
           $("#main-content").html(data);
           admissionEnquiry.viewEnquiryDetails(true, "SUBMITTED");
           enquiryPagination.dataCache['status'] = "SUBMITTED";
           academicSessionHandler.bindSessionChangeEvent(admissionEnquiry.loadEnquiryDetailsForSession);
           admissionEnquiry.bindEnquiryClickEvents();
           admissionEnquiry.dataCache = {};
           initDate(5 * 365);
        });
    },

    loadEnquiryDetailsForSession : function() {
         admissionEnquiry.viewEnquiryDetails(true, "SUBMITTED");
    },

    backButton: function(status, offset, itemsPerPage){
       $(".student-details-screen").css("display", "block");
       var sessionId = academicSessionHandler.getSelectedSessionId();

       itemsPerPage = 0;
       ajaxClient.get("/admission/enquiry-details/session/" + sessionId + "/" + status + "/" + offset + "/" + itemsPerPage, function(data) {
             $("#enquiryDetailsResult").html(data);
             var screenHeight = $("#sidebar").height() - $("#enquiryDetailsResult").position().top - 300;
             $('#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper').attr("style", "height:" + screenHeight + "px;  overflow-y: scroll; cursor: pointer;");
            //  var response = JSON.parse($("#pagination-info").text().trim());
            //  $('#items-per-page').val(response.itemsPerPage);
            //  $('.page-item').removeClass('active');
            //  var pageNumber = (response.offset / response.itemsPerPage) + 1;
            //  $('#page-number-' + 1).addClass('active');

             admissionEnquiry.highlightActiveTab(status);
             admissionEnquiry.bindEnquiryClickEvents();
             admissionEnquiry.dataCache = {};
             enquiryPagination.initPagination();

             console.log("." + status.toLowerCase() + "-datatables-reponsive");
            $("." + status.toLowerCase() + "-datatables-reponsive").DataTable({
              searching: true,
              columnDefs: [
                { orderable: false, targets: "no-sort" },
            ],
          });
       });
    },

    viewEnquiryDetails : function(freshSearch, status) {
        enquiryPagination.dataCache['status'] = status;
        var filterDate = getDate($("#latest-followup-date").val());
        if(filterDate != null){
          filterDate = filterDate.getTime()/1000;
        } else {
          filterDate = 0;
        }
        if(status != "ACCEPTED"){
          filterDate = 0;
        }
        var page_number = $('.page-item.active').find('.page-number').text().trim();
        if (freshSearch) {
          page_number = 1;
        }
        var itemsPerPage = $('#items-per-page').val();
        itemsPerPage = 0;
        if (!page_number) {
          page_number = 1;
        }
        var offset = (page_number - 1) * itemsPerPage;
        var sessionId = academicSessionHandler.getSelectedSessionId();
        $("#enquiryDetailsResult").html("");

        ajaxClient.get("/admission/enquiry-details/session/" + sessionId + "/" + status + "/" + offset + "/" + itemsPerPage + "?filter_date=" + filterDate, function(data) {
          $("#enquiryDetailsResult").html(data);
          var screenHeight = $("#sidebar").height() - $("#enquiryDetailsResult").position().top - 300;
          $('#fee-payment-transactions-fixed-height-lmain_screen_container.htmlist-wrapper').attr("style", "height:" + screenHeight + "px;  overflow-y: scroll; cursor: pointer;");
          // var response = JSON.parse($("#pagination-info").text().trim());
          // $('#items-per-page').val(response.itemsPerPage);
          // $('.page-item').removeClass('active');
          // var pageNumber = (response.offset / response.itemsPerPage) + 1;
          // $('#page-number-' + page_number).addClass('active');

          admissionEnquiry.highlightActiveTab(status);
          admissionEnquiry.bindEnquiryClickEvents();
          admissionEnquiry.dataCache = {};
          enquiryPagination.initPagination();
          initDateWithYearRange("-2:+2", false);
          if(filterDate !=0){
            $("#latest-followup-date").val(getFormattedDate(filterDate))
          }

          console.log("." + status.toLowerCase() + "-datatables-reponsive");
           $("." + status.toLowerCase() + "-datatables-reponsive").DataTable({
              searching: true,
              columnDefs: [
                { orderable: false, targets: "no-sort" },
            ],
          });
          
        });
      },


      highlightActiveTab: function(status) {
          $("#nav-submitted-enquiry-tab, #nav-accepted-enquiry-tab, #nav-rejected-enquiry-tab, #nav-closed-enquiry-tab").removeClass('active');
          $("#submitted-enquiry-content, #accepted-enquiry-content, #rejected-enquiry-content, #closed-enquiry-content").removeClass('active');
          switch (status) {
            case 'SUBMITTED':
              $("#nav-submitted-enquiry-tab").addClass('active');
              $("#submitted-enquiry-content").addClass('active');
              break;
            case 'ACCEPTED':
              $("#nav-accepted-enquiry-tab").addClass('active');
              $("#accepted-enquiry-content").addClass('active');
              break;
            case 'REJECTED':
              $("#nav-rejected-enquiry-tab").addClass('active');
              $("#rejected-enquiry-content").addClass('active');
              break;
            case 'CLOSED':
              $("#nav-closed-enquiry-tab").addClass('active');
              $("#closed-enquiry-content").addClass('active');
              break;
          }
      },


      bindEnquiryClickEvents: function() {
          $(".view-enquiry-details").on('click', function() {
            var enquiryId = $(this).attr("id").trim();
            admissionEnquiry.showEnquiryDetails(enquiryId);
          });
      },

      showEnquiryDetails: function(enquiryId) {
          var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
          var enquiry = JSON.parse(enquiryDetails);
          admissionEnquiry.populateEnquiryDetails(enquiry);
          $("#view-enquiry-details-modal").modal('toggle');
      },

      populateEnquiryDetails: function(enquiry) {
          admissionEnquiry.resetFields();
          $('#tracking-id').text(enquiry.trackingId);
          $('#parent-name').text(enquiry.guardianName);
          $('#child-name').text(enquiry.childName);
          $('#standard').text(enquiry.standardName);
          $('#phone-number').text(enquiry.guardianContactInfo);
          $('#email-id').text(enquiry.guardianEmailId);
          $('#message').text(enquiry.message);
          $('#successful').text(enquiry.successful);
          if(enquiry.studentDetailedRow != null){
             $('#student').text(enquiry.studentDetailedRow.studentBasicInfo.name)
             $('#student-adm-no').text(enquiry.studentDetailedRow.studentBasicInfo.admissionNumber)
          }
          if(enquiry.enquiryStatus !== "CLOSED"){
            $('#successful').closest('tr').css('display', 'none');
            $('#student').closest('tr').css('display', 'none');
            $('#student-adm-no').closest('tr').css('display', 'none');

          }
      },

      resetFields: function(){
         $('#tracking-id').text('');
         $('#parent-name').text('');
         $('#child-name').text('');
         $('#standard').text('');
         $('#phone-number').text('');
         $('#email-id').text('');
         $('#message').text('');
         $('#successful').text('');
         $('#student').text('')
         $('#student-adm-no').text('')
         $('#successful').closest('tr').css('display', '');
         $('#student').closest('tr').css('display', '');
         $('#student-adm-no').closest('tr').css('display', '');

      },

       showEnquiry: function(actionType, buttonElement) {
         var enquiryId = $(buttonElement).attr("id").trim();
         var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
         var enquiry = JSON.parse(enquiryDetails);
         $('#accept-action-button').hide();
         $('#reject-action-button').hide();
         $('#complete-action-button').hide();
         $('#enquiry-id-action-div').text(enquiry.enquiryId);

         if (actionType === 'ACCEPTED') {
           $('#accept-action-button').show();
         } else if (actionType === 'REJECTED') {
           $('#reject-action-button').show();
         } else if (actionType === 'COMPLETED') {
           $('#complete-action-button').show();
         }
         $('#reason-outcome').val('');
         $("#enquiry-details-action-modal").modal('toggle');
       },

      updateEnquiryStatus: function(status, toggleElement, reason, enquiryId) {
         var reasonValue = $("#" + reason).val();
         var enquiry_id = $('#' + enquiryId).text();
         admissionEnquiry.dataCache = {
           reason: reasonValue,
           status: status,
           enquiryId: enquiry_id
         };

         $("#" + toggleElement).modal('toggle');
         admissionEnquiry.updateActionModalContent(status);
         $("#enquiry-action-modal").modal('toggle');
       },

      updateActionModalContent: function(status) {
          var title = "";
          var html = "";

          if (status === 'REJECTED') {
            title = "Reject Enquiry";
            html = "Do you wish to reject the enquiry?";
          } else if (status === 'CLOSED') {
            title = "Close Enquiry";
            html = "Do you wish to close the enquiry?";
          } else {
            title = "Accept Enquiry";
            html = "Do you wish to accept the enquiry?";
          }

          $("#enquiry-title").text(title);
          $("#enquiry-details-manage-id").html(html);
      },

      confirmAction: function() {
          var reason = admissionEnquiry.dataCache.reason;
          var status = admissionEnquiry.dataCache.status;
          var enquiry_id = admissionEnquiry.dataCache.enquiryId;
          var isSuccessful = false;
          var studentId = ""
          if(admissionEnquiry.dataCache.status == "CLOSED"){
          isSuccessful = admissionEnquiry.dataCache.isSuccessful
          if(admissionEnquiry.dataCache.studentId != undefined || admissionEnquiry.dataCache.studentId === ""){
          studentId = admissionEnquiry.dataCache.studentId}
          }
          var academicSessionId = academicSessionHandler.getSelectedSessionId();

          $("#enquiry-action-modal").modal('toggle');
          var enquiryDetailsPayload = { 'academicSessionId' : academicSessionId ,'enquiryId' : enquiry_id, 'studentId' : studentId, 'message': reason, 'enquiryStatus' : status, 'successful' : isSuccessful};

          ajaxClient.post("/admission/update-enquiry-status", {'enquiryDetailsPayload' : JSON.stringify(enquiryDetailsPayload)}, function(data) {
            $("#enquiry-status-modal-container").html(data);
            $("#update-enquiry-status-modal").modal({ backdrop: 'static', keyboard: false });
            admissionEnquiry.viewEnquiryDetails(true, "SUBMITTED")
          });
      },

     showDetails : function(enquiryId){
        var sessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/admission/enquiry-detail/session/" + sessionId + "/" + enquiryId, function(data) {
                  $("#enquiryDetailsResult").html(data);
                  $(".student-details-screen").css("display", "none");
                  admissionEnquiry.bindEnquiryClickEvents();
                  admissionEnquiry.dataCache = {};
                  enquiryPagination.initPagination();
        });

     },

     editFollowUpDetails : function(value){
          initDateWithYearRange("-10:+10", true);
          initPastDateById("enquiry-follow-up-date", 4000);
          var followUpPayload = JSON.parse($(value).parent().find("#follow-up-json").text().trim());
          $("#update-transaction-entity-name").html("<option>"+followUpPayload.entityName+"</option>");
          $("#update-enquiry-follow-up-date").val(getFormattedDate(followUpPayload.followUpDate));
          $("#update-transaction-follow-up-mode").html("<option>"+followUpPayload.followUpMode+"</option>");
          $("#update-transaction-contact-person-name").val(followUpPayload.contactPersonName);
          $("#update-transaction-follow-up-type").html("<option>"+followUpPayload.followUpType+"</option>");
          $("#update-transaction-conversation").val(followUpPayload.conversation);

          if(followUpPayload.nextFollowUpDate != null){
            $("#update-next-enquiry-follow-up-date").val(getFormattedDate(followUpPayload.nextFollowUpDate));
          }
          else{
             $("#update-next-enquiry-follow-up-date").val("");
          }

          $("#update-transaction-amount").val(followUpPayload.amount);
          $("#update-enquiry-follow-up-id").text(followUpPayload.followUpId);
          $("#student-id").text(followUpPayload.entityId);
          $("#update-enquiry-follow-up-modal").modal('toggle');
     },

     closeEnquiry : function(buttonElement){
           var enquiryId = $(buttonElement).attr("id").trim();
           var enquiryDetails = $('#' + enquiryId).closest('tr').find('.enquiry-details-info').text().trim();
           var enquiry = JSON.parse(enquiryDetails);
           $('#enquiry-outcome-id-action-div').text(enquiry.enquiryId);
           $('#outcome').val('');
           $('#is-successful').prop('checked', false);
           $(".enquiry_selected_student_row").remove();
           $('#enquiry-students-search').prop('disabled', true);
           $("#close-details-action-modal").modal('toggle');
           var academicSessionId = academicSessionHandler.getSelectedSessionId();
           admissionEnquiry.bindSearchStudentEvent();
     },

     bindSearchStudentEvent : function () {
           var resultArea = "#enquiry-students-search-result";
           $("#enquiry-students-search").on('keyup', function (e) {
             if (e.keyCode == 13) {
                 admissionEnquiry.doneStudentSearchTyping(resultArea);
             }
           });
           liveSearchHandler.bindEvent('#enquiry-students-search', resultArea, admissionEnquiry.doneStudentSearchTyping);
         },

     doneStudentSearchTyping: function(resultArea) {
           var searchText = $('#enquiry-students-search').val().trim();
           var status = "ENROLLED";
           admissionEnquiry.studentLiveSearchEvent(searchText, resultArea, admissionEnquiry.loadStudentDetails, status);
     },

     studentLiveSearchEvent : function (searchText, resultArea, triggerMethod, status){
           var academicSessionId =  academicSessionHandler.getSelectedSessionId();
           ajaxClient.get("/admission/enquiry-student-live-search/" + academicSessionId + "?searchText=" + searchText + "&status=" + status, function(data) {
                 $(resultArea).html(data);
                 admissionEnquiry.loadStudentDetails();
           });
     },

     loadStudentDetails: function () {
           $("#live-search-student-results .student-result-row .student-details").on("click", function () {
                var studentDetails = JSON.parse($(this).closest(".student-result-row").find(".student-info-div").text().trim());
                admissionEnquiry.addSelectedStudentRow(studentDetails);
           });
     },


     addSelectedStudentRow : function (studentDetails) {
            var name = studentDetails.name;
            var admissionNumber = studentDetails.admissionNumber;
            var fatherName = siblingDetails.checkIfStringEmpty(studentDetails.fathersName) ? "-" : studentDetails.fathersName;
            var standardName = studentDetails.studentSessionData.standardNameWithSection;
            var studentId = studentDetails.studentId;
             closeButtonHTML = " <button type=\"button\" class=\"close delete-student-row \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";

            $(".enquiry_selected_student_row").remove();
            var divHTML = "<div class=\"enquiry_selected_student_row " +
                            "\" id=\"" + studentId + "\" style=\"display: flex; justify-content: space-between; align-items: center; padding: 10px;\">" +
                            "<div class=\"student-name-div\" style=\"; \">Student: " +
                            name + " (" + admissionNumber + ", "+ standardName +", " + fatherName + ")" +
                            "</div>" +
                            "<div class=\"close-button-div\" style=\"margin-right: 300px; cursor: pointer;\">" +
                            closeButtonHTML +
                            "</div></div>";

            $("#enquiry-students-search-row").after(divHTML);
            $("#enquiry-students-search-result").html("");
            $("#enquiry-students-search").val("");
            siblingDetails.deletePurchaseItemEntry();
     },


     toggleStudentSelect : function() {
            const isSuccessful = $('#is-successful').is(':checked');
            $('#enquiry-students-search').prop('disabled', !isSuccessful);
            if(!isSuccessful){
                 $(".enquiry_selected_student_row").remove();
            }
     },

    closeEnquiryDetail : function(){
            var outcome = $("#outcome").val();
            var enquiry_id = $('#enquiry-outcome-id-action-div').text();
            var isSuccessful = $('#is-successful').is(':checked');
            if (isSuccessful) {
                        var studentId = $(".enquiry_selected_student_row").attr('id');
            }
            admissionEnquiry.dataCache = {
                  reason: outcome,
                  status: "CLOSED",
                  enquiryId: enquiry_id,
                  isSuccessful: isSuccessful,
                  studentId: studentId
              };

             $("#close-details-action-modal").modal('toggle');
             admissionEnquiry.updateActionModalContent("CLOSED");
             $("#enquiry-action-modal").modal('toggle');
    }
};

var enquiryPagination = {
  dataCache : {},

  initPagination: function () {
    pagination.bindEvents(
      function() {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(false, status);
      },
      function () {
        var status = enquiryPagination.dataCache['status'];
        admissionEnquiry.viewEnquiryDetails(true, status);
      }
    );
  },
};

function loadAllStudentsPage() {
    ajaxClient.get("/admission/relieve-student-screen", function(data) {
      $("#main-content").html(data);
      initiateAllStudentPage();
    });
}

function reloadAllStudentsPage(){
  ajaxClient.get("/admission/relieve-student-screen-reload", function(data) {
    $("#search-students-without-academic-session-results").html(data);
    returnToRelieveStudentMainScreen();
    initiateAllStudentPage();
  });
}

function initiateAllStudentPage() {
  registerStudentWithoutAcademicSessionSearchCallback(bindEnrolledStudentStatusChangeButtonCallBack);
  studentSearchWithoutAcademicSession.search("",bindEnrolledStudentStatusChangeButtonCallBack);
}

function returnToMainScreenFromRelieveStudentPage(status) {
    // var parentScreenId = $("#relieve-status-incoming-page-id").text().trim();
    var parentScreenId = $("#student-status").text().trim();
    if(parentScreenId === "ENROLMENT_PENDING"){
      reloadPendingEnrolementStudentDetailsPage();
    }else if(parentScreenId == "ENROLLED"){
      reloadStudentDetailsPage();
    }else if(parentScreenId == "RELIEVED"){
      reloadRelievedStudentDetailsPage();
    }else if(parentScreenId == "NSO"){
      reloadNSOStudentDetailsPage();
    } else {
      reloadAllStudentsPage();
    }
}

function bindEnrolledStudentStatusChangeButtonCallBack(data) {
    bindStudentStatusChangeButtonCallBack(ALL_ENROLLED_STUDENT_SCREEN_ID);
}

function bindStudentStatusChangeButtonCallBack(incomingScreenId) {
  $(".relieve-student-button").on('click', function () {
    var studentId = $(this).attr("id");
    loadStudentStatusChangeDetails(studentId,"RELIEVED", incomingScreenId);
  });
  $(".mark-nso-student-button").on('click', function () {
    var studentId = $(this).attr("id");
    loadStudentStatusChangeDetails(studentId,"NSO", incomingScreenId);
  });
  $(".enroll-student-button").on('click', function () {
    var studentId = $(this).attr("id");
    $('#enroll-student-details-modal').modal('toggle');
    $("#enroll-student-id").text(studentId);
    // loadStudentStatusChangeDetails(studentId,"ENROLLED", incomingScreenId);
  });
  // console.log(  $("#datatables-reponsive").length);
  // $("#datatables-reponsive").DataTable({
  //   responsive: true
  // });
}

function loadStudentStatusChangeDetails(studentId, statusChangeType, incomingScreenId) {
  $(".relieve-student-screen").attr('style','display:none');
  $(".student-details-screen").attr('style','display:none');
  ajaxClient.get("/admission/relieve-student-status/"+studentId+"/"+statusChangeType, function(data) {
    $("#relieve-student-status-screen").html(data);
    $("#relieve-status-incoming-page-id").text(incomingScreenId);
    $("#relieve-student-status-screen").attr('style','display:block');
    initDateInput();
    bindRelieveReason();
  });
}

function bindRelieveReason() {
  $('.relieve-reason').on('change', function() {
    var reasonValue = $(this).find(':selected').val().trim();
    if(reasonValue == "other"){
      $("#other-relieve-reason-container").attr("style","display:block");
    }else{
      $("#other-relieve-reason-container").attr("style","display:none");
    }
  });
}
function returnToRelieveStudentMainScreen() {
    $(".relieve-student-screen").attr('style','display:none');
    $("#relieve-student-main-screen").attr('style','display:block');
    $(".student-details-screen").attr('style','display:block');
    $('#student-list\\.update-student-screen').attr('style','display:none');
    $('#student-list\\.upload-document-screen').attr('style','display:none');
}

function updateRelieveDetails(ref) {
  var studentId = $("#p-student-id").text();
  $("#relieve-student-confirmation-modal").modal("toggle");
  relieveStudent(ref, studentId, "RELIEVED", true);
}

function relieveStudent(ref, studentId, statusChangeType, updating) {
  $("#relieve-nso-student-confirmation-modal").modal("toggle");
  if(updating === undefined || updating === null || updating === "") {
    updating = false;
  } else {
    $("#modal-footer-yes-button").text("");
  }
  var relieveDate = getDate($(ref).parent().parent().find('#relieve-date').val());
  if(relieveDate == null){
    showErrorDialogBox("Please fill date.");
    return;
  }

  var reason = "";
  var lastActiveSession = null;
  var lastActiveSessionCompleted = null;
  if(statusChangeType == "RELIEVED"){
    var relieveReasonValue = $(ref).parent().parent().find(".relieve-reason").find(':selected').val().trim();
    if(relieveReasonValue == "other"){
      reason = $(ref).parent().parent().find("#other-relieve-reason").val().trim();
    }else{
      reason = $(ref).parent().parent().find(".relieve-reason").find(':selected').text().trim();
    }

    if(reason == ''){
      showErrorDialogBox("Please select the proper relieve reason.");
      return;
    }

    lastActiveSession = $(ref).parent().parent().find("#last-active-session").val();
    if(lastActiveSession === undefined || lastActiveSession === null || lastActiveSession === "") {
      showErrorDialogBox("Please select a last active session to relieve student.");
      return;
    }

    lastActiveSessionCompleted = $(ref).parent().parent().find("#is-last-session-completed").val();
    if(lastActiveSessionCompleted === undefined || lastActiveSessionCompleted === null || lastActiveSessionCompleted === "") {
      showErrorDialogBox("Please select if last active session was completed or not.");
      return;
    }

  }
  var codeOfConduct = $(ref).parent().parent().find("#code-of-conduct").find(':selected').text().trim();
  var tcVariables = {"TRANSFER_REASON" : reason, "CODE_OF_CONDUCT" : codeOfConduct}

  var relievedMetadata = {"LAST_ACTIVE_SESSION" : lastActiveSession, "LAST_ACTIVE_SESSION_COMPLETED" : lastActiveSessionCompleted};

  var relievePayload = {"relieveDate" : relieveDate.getTime()/1000, "relieveReason" : reason, "tcVariables" : tcVariables, "relievedMetadata" : relievedMetadata}
  ajaxClient.post("/admission/relieve-student/"+studentId+"/"+statusChangeType+"/"+updating, {'relievePayload':JSON.stringify(relievePayload)}, function(data) {
    $("#relieve-student-confirmation-modal-container").html(data);
    $("#relieve-student-success-status-modal").modal({backdrop: 'static', keyboard: false});
    $("#relieve-student-status-screen").attr('style','display:none;');
    $("#student-details").attr('style','display:block;');
    $("#relieve-student-main-screen").attr('style','display:block;');
  });
}

function enrollNSOStudent() {
  $("#enroll-student-details-modal").modal("toggle");
  var studentId = $("#enroll-student-id").text();
  var statusChangeType = "ENROLLED";
  ajaxClient.post("/admission/enroll-student/"+studentId+"/"+statusChangeType, {}, function(data) {
    $("#enroll-student-confirmation-modal-container").html(data);
    $("#relieve-student-success-status-modal").modal({backdrop: 'static', keyboard: false});
  });
}

function registerUpdateStudentDetailsCallBack(){
  $('.update-student-info').on('click', function () {
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    fillStudentInformation(studentInfo);
    bindAdmissionClassSectionEvent();
    enforceConstraints();
    permanentAddressUpdate("update-permanent-same-as-present", "update\\.student-permanent-address", "update\\.student-city", "update\\.student-state", "update\\.student-post-office", "update\\.student-police-station",
        "update\\.student-zipcode", "update\\.student-present-address", "update\\.student-present-city", "update\\.student-present-state", "update\\.student-present-post-office", "update\\.student-present-police-station", "update\\.student-present-zipcode");
    // previousTabSwtichingEvent();
    // nextTabSwtichingEvent();
    $('.student-details-screen').attr('style','display:none');
    $('#student-list\\.update-student-screen').attr('style','display:block');
    $('#screen-name').text('STUDENT-LIST');
  });
}

var OTHER_DOCUMENT_TYPE = "OTHER";
var VIEW_SIBLINGS = "VIEW_SIBLINGS";
var ADD_SIBLINGS = "ADD_SIBLINGS";
var DELETE_SIBLINGS = "DELETE_SIBLINGS";

function loadStudentDetailsPage() {
    ajaxClient.get("/admission/student-details", function(data) {
      $("#main-content").html(data);
      initiateStudentPage();
    });
}

function reloadStudentDetailsPage(){
  ajaxClient.get("/admission/student-details-reload", function(data) {
    $("#student-main-screen").html(data);
    initiateStudentPage();
  });
}

function initiateStudentPage() {
  $("#student-status").text("ENROLLED");
  academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession);
  searchStudents(true);
  $('.selectpicker').selectpicker();
  registerStudentSearchCallback();
  // registerViewStudentDetailsCallBack();
  bindStudentListPageEvents();
  initDate(365*100);
  $(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip();
  });
}

function bindStudentListPageEvents() {
  previousTabSwtichingEvent();
  nextTabSwtichingEvent();
  bindRemoveErrorDisplayEvent();
  studentList.initPagination();
}

function loadRelievedStudentDetailsPage(){
  ajaxClient.get("/admission/relieved-student-details", function(data) {
    $("#main-content").html(data);
    initiateRelievedStudentPage();
  });
}

function reloadRelievedStudentDetailsPage(){
  ajaxClient.get("/admission/relieved-student-details-reload", function(data) {
    $("#relieve-student-main-screen").html(data);
    initiateRelievedStudentPage();
  });
}

function initiateRelievedStudentPage() {
  $("#student-status").text("RELIEVED");
  academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession);
  searchStudents(true);
  $('.selectpicker').selectpicker();
  registerStudentSearchCallback();
  registerViewStudentDetailsCallBack();
  bindStudentListPageEvents();
}

function loadNSOStudentDetailsPage(){
  ajaxClient.get("/admission/relieved-student-details", function(data) {
    $("#main-content").html(data);
    initiateNSOStudentPage();
  });
}

function reloadNSOStudentDetailsPage(){
  ajaxClient.get("/admission/relieved-student-details-reload", function(data) {
    $("#relieve-student-main-screen").html(data);
    initiateNSOStudentPage();
  });
}

function initiateNSOStudentPage() {
  $("#student-status").text("NSO");
  academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession);
  searchStudents(true);
  $('.selectpicker').selectpicker();
  registerStudentSearchCallback();
  registerViewStudentDetailsCallBack();
  bindStudentListPageEvents();
  bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID);
}

function loadPendingEnrolmentsPage(){
  ajaxClient.get("/admission/pending-enrolments-details", function(data) {
    $("#main-content").html(data);
    initiatePendingStudentPage();
  });
}

function reloadPendingEnrolementStudentDetailsPage(){
  ajaxClient.get("/admission/pending-enrolments-details-reload", function(data) {
    $("#student-main-screen").html(data);
    initiatePendingStudentPage();
  });
}

function initiatePendingStudentPage() {
  $("#student-status").text("ENROLMENT_PENDING");
  academicSessionHandler.bindSessionChangeEvent(admissionStudentDetails.changeSession);
  searchStudents(true);
  $('.selectpicker').selectpicker();
  registerStudentSearchCallback();
  registerViewStudentDetailsCallBack();
  registerEnrollStudentDetailsCallBack();
  bindStudentListPageEvents();
  initDate(365*100);
  $(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip();
  });
}

var studentRegistration = {
  dataCache : {},

  loadHomePage : function () {
    ajaxClient.get("/admission/student-registration-home", function(data) {
      $("#main-content").html(data);
      studentRegistration.dataCache = {};
      academicSessionHandler.bindSessionChangeEvent(studentRegistration.changeSession);
      studentRegistration.bindStatusChangeEvent();
      studentRegistration.searchRegisteredStudents(true);
      studentRegistration.registerStudentSearchCallback();
    });
  },

  changeSession: function() {
    var sessionDisplay = academicSessionHandler.getSelectedSessionDisplayName();
    $("#academic-year-display").text("Student Registrations : "+sessionDisplay)
    studentRegistration.searchRegisteredStudents(true);
  },

  initPagination: function () {
    pagination.bindEvents(
      function() {
        studentRegistration.searchRegisteredStudents(false);
      },
      function () {
        studentRegistration.searchRegisteredStudents(false);
      },
      function () {
        studentRegistration.searchRegisteredStudents(false);
      },
      function () {
        studentRegistration.searchRegisteredStudents(true);
      }
    );
  },

  registerStudentSearchCallback: function(){
    $('#search-users').on('click', function () {
           studentRegistration.searchRegisteredStudents(true);
    });
    $("#search-user-input").on('keyup', function (e) {
      if (e.keyCode == 13) {
           studentRegistration.searchRegisteredStudents(true);
      }
    });
  },

  searchRegisteredStudents : function (freshSearch) {
    var page_number = $('.page-item.active').find('.page-number').text().trim();
    if(freshSearch){
        page_number = 1;
    }
    var itemsPerPage = $('#items-per-page').val();
    if(page_number == null || page_number == ''){
      page_number = 1;
    }
    var offset = (page_number - 1)*itemsPerPage;
    var search_text = $("#search-user-input").val().trim();
    var academic_session_id = academicSessionHandler.getSelectedSessionId();
    var status = studentRegistration.getStatus();
    ajaxClient.get("/admission/student-registration/search/"+academic_session_id+"?search_text="+search_text+"&offset="+offset+"&itemsPerPage="+itemsPerPage+"&status="+status, function(data) {
        $("#search-user-result").html(data);
        pagination.updatePaginationDetails("search-user-result","student-registration-fixed-height-list-wrapper");
        studentRegistration.initPagination();
        studentRegistration.bindActionEvents();
    });
  },

  getStatus : function () {
    var elementId = studentRegistration.dataCache['pageId'];
    if(elementId == "student-registration-status-applied"){
      return "APPLIED";
    }else if(elementId == "student-registration-status-approved"){
      return "APPROVED";
    }else if(elementId == "student-registration-status-rejected"){
      return "REJECTED";
    }
    return "APPLIED";
  },

  bindStatusChangeEvent : function () {
      $('.student-registration-status-options').click(function() {
          studentRegistration.dataCache['pageId'] = $(this).attr("id").trim();
          studentRegistration.searchRegisteredStudents(true);
      });
  },

  bindActionEvents : function () {
      $('.preview-student-info').click(function() {
        var status = studentRegistration.getStatus();
        if(status == "APPLIED"){
            $('#apply-registration-action-buttons').attr("style","display:block");
        }else{
            $('#apply-registration-action-buttons').attr("style","display:none");
        }
        $('#preview-student-registration-details-modal').modal('toggle');
        var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
        var studentInfo = JSON.parse(studentInfoJson);
        studentRegistration.dataCache['selectedStudent'] = studentInfo;
        studentRegistration.fillStudentPreviewDetails(studentInfo);
      });
  },

  applyRegistrationActionConfirmation : function (action) {
    $('#preview-student-registration-details-modal').modal('toggle');
    var selectedStudent = studentRegistration.dataCache['selectedStudent'];
    var confirmationText = "";
    var actionButtons = "";
    if(action == "approve"){
        confirmationText = "You are <strong>approving</strong>  the registration reqest of <strong>" +selectedStudent.studentBasicInfo.name + " (Registration# "+ selectedStudent.studentBasicInfo.onlineRegistrationNumber +")</strong> . Do you want to proceed?";
        actionButtons = "<button type=\"button\" class=\"btn btn-success\" onclick=\"studentRegistration.loadRegistrationApprovalForm()\">Yes, Approve</button> <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Close</button>"
    }else if(action == "reject"){
      confirmationText = "You are <strong> declining</strong>  the registration reqest of <strong>" +selectedStudent.studentBasicInfo.name + " (Registration# "+ selectedStudent.studentBasicInfo.onlineRegistrationNumber +")</strong> . Do you want to proceed?";
      actionButtons = "<button type=\"button\" class=\"btn btn-danger\" onclick=\"studentRegistration.rejectStudentRegistration()\">Yes, Reject</button> <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Close</button>"
    }
    $("#student-registration-confirmation-text").html(confirmationText);
    $("#student-registration-action-confirm-modal").find('.modal-footer').html(actionButtons);
    $('#student-registration-action-confirm-modal').modal({backdrop: 'static', keyboard: false});
  },

  loadRegistrationApprovalForm : function () {
    $('#student-registration-action-confirm-modal').modal('toggle');
    var selectedStudent = studentRegistration.dataCache['selectedStudent'];
    ajaxClient.get("/admission/registration-enroll-metadata/"+selectedStudent['academicSession']['academicSessionId']+"/"+selectedStudent['standard']['standardId'], function(data) {
      $("#register-student-form-modal-container").html(data);
      $('#register-student-form-modal').modal('toggle');
    });
  },

  rejectStudentRegistration : function () {
    $('#student-registration-action-confirm-modal').modal('toggle');
    var selectedStudent = studentRegistration.dataCache['selectedStudent'];
    ajaxClient.post("/admission/student-registration/"+selectedStudent['studentRegistrationId']+"/apply-action/REJECTED", {}, function(data) {
        $("#student-registration-status-modal-container").html(data);
        $('#student-registration-status-modal').modal('toggle');
        studentRegistration.searchRegisteredStudents(true);
    });
  },

  approveStudentRegistration : function () {
    $('#register-student-form-modal').modal('toggle');
    var selectedStudent = studentRegistration.dataCache['selectedStudent'];

    var registrationNumber = $("#student-registration-number").val();
    var applicableFeeStructures = [];

    $("#applicable-enrollment-fees").find("input.applicable-fee-structure-assign-checkbox").each(function() {
        if(!$(this).is(":checked")) {
            return;
        }
        var feeStructureId = $(this).attr("id");
        applicableFeeStructures.push(feeStructureId);
    });

    var registerStudentPayload = {'registrationNumber' : registrationNumber, 'feeStructureIds' : applicableFeeStructures};
    ajaxClient.post("/admission/student-registration/"+selectedStudent['studentRegistrationId']+"/apply-action/APPROVED",{'registrationDetails':JSON.stringify(registerStudentPayload)}, function(data) {
        $("#student-registration-status-modal-container").html(data);
        $('#student-registration-status-modal').modal('toggle');
        studentRegistration.searchRegisteredStudents(true);
      });
  },

  fillStudentPreviewDetails : function(studentInfo){

    var paymentStatus = studentInfo.paymentStatus;
    if(paymentStatus == 'NOT_APPLICABLE'){
      $('.student-payment-information').attr("style","display:none;");
    } else {
      $('.student-payment-information').attr("style","display:block;");
      $('#preview-payment-status').text(studentInfo.paymentStatusDisplayName);
      $('#preview-assigned-amount').text(studentInfo.paymentData.assignedAmount);
      $('#preview-paid-amount').text(studentInfo.paymentData.paidAmount);
      $('#preview-transaction-mode').text(studentInfo.paymentData.transactionModeDisplayName);
      $('#preview-transaction-date').text(getFormattedDate(studentInfo.paymentData.transactionDate));
      $('#preview-transaction-reference').text(studentInfo.paymentData.transactionReference);
    }

    $('#preview\\.student-name').text(studentInfo.studentBasicInfo.name);
    $('#preview\\.registration-number').text(studentInfo.studentBasicInfo.onlineRegistrationNumber);
    $('#preview\\.standard').text(studentInfo.standard.displayName);
    $('#preview\\.gender').text(studentInfo.studentBasicInfo.gender);
    if(studentInfo.studentBasicInfo.dateOfBirth != null && studentInfo.studentBasicInfo.dateOfBirth > 0){
      $('#preview\\.dob').text(getFormattedDate(studentInfo.studentBasicInfo.dateOfBirth));
    }else{
      $('#preview\\.dob').text("");
    }
    $('#preview\\.birth-place').text(studentInfo.studentBasicInfo.birthPlace);
    $('#preview\\.category').text(studentInfo.studentBasicInfo.userCategory);
    $('#preview\\.religion').text(studentInfo.studentBasicInfo.religion);
    $('#preview\\.caste').text(studentInfo.studentBasicInfo.caste);
    $('#preview\\.rte').text(studentInfo.studentBasicInfo.rteDisplay);
    $("#preview\\.mother-tongue").text(studentInfo.studentBasicInfo.motherTongue);
    $("#preview\\.area-type").text(studentInfo.studentBasicInfo.areaType);
    $("#preview\\.specially-abled").text(studentInfo.studentBasicInfo.speciallyAbledDisplay);
    $("#preview\\.bpl").text(studentInfo.studentBasicInfo.bplDisplay);
    $('#preview\\.present-address').text(getStudentPresentAddress(studentInfo.studentBasicInfo));
    $('#preview\\.address').text(getStudentPermanentAddress(studentInfo.studentBasicInfo));
    $('#preview\\.aadhar-number').text(studentInfo.studentBasicInfo.aadharNumber);
    $('#preview\\.registration-date').text(getFormattedDate(studentInfo.studentBasicInfo.registrationDate));
    $('#preview\\.admission-date').text(getFormattedDate(studentInfo.studentBasicInfo.admissionDate));
    $('#preview\\.primary-contact-number').text(studentInfo.studentBasicInfo.primaryContactNumber);
    $('#preview\\.primary-email').text(studentInfo.studentBasicInfo.primaryEmail);
    $('#preview\\.whatsapp-number').text(studentInfo.studentBasicInfo.whatsappNumber);
    $("#preview\\.is-sponsored").text(studentInfo.studentBasicInfo.sponsoredDisplay);
    $('#preview\\.nationality').text(studentInfo.studentBasicInfo.nationality);
    $("#preview\\.student-new-admission").text(studentInfo.studentBasicInfo.newAdmissionDisplay);
    $("#preview\\.student-hosteller").text(studentInfo.studentBasicInfo.hostellerDisplay);

    if(studentInfo.studentImage != null){
      $('#preview\\.student-image').text("Uploaded");
    }else{
      $('#preview\\.student-image').text("Not present");
    }
    $('#preview\\.student-house').text(studentInfo.studentBasicInfo.instituteHouse == null ? "" : studentInfo.studentBasicInfo.instituteHouse.houseName);
    $('#preview\\.student-admission-in-class').text(studentInfo.studentBasicInfo.admissionInClass);
    $('#preview\\.student-name-as-per-aadhar').text(studentInfo.studentBasicInfo.studentNameAsPerAadhar);
    $('#preview\\.child-category-criteria').text(studentInfo.studentBasicInfo.childCategoryCriteria);
    $('#preview\\.specially-abled-type').text(studentInfo.studentBasicInfo.speciallyAbledType);

    $('#preview\\.student-mother-name').text(studentInfo.studentFamilyInfo.mothersName);
    $('#preview\\.student-father-name').text(studentInfo.studentFamilyInfo.fathersName);
    $('#preview\\.student-mother-qualification').text(studentInfo.studentFamilyInfo.mothersQualification);
    $('#preview\\.student-father-qualification').text(studentInfo.studentFamilyInfo.fathersQualification);
    $('#preview\\.mother-contact-number').text(studentInfo.studentFamilyInfo.mothersContactNumber);
    $('#preview\\.father-contact-number').text(studentInfo.studentFamilyInfo.fathersContactNumber);
    $('#preview\\.mother-occupation').text(studentInfo.studentFamilyInfo.mothersOccupation);
    $('#preview\\.father-occupation').text(studentInfo.studentFamilyInfo.fathersOccupation);
    $('#preview\\.mother-aadhar-number').text(studentInfo.studentFamilyInfo.mothersAadharNumber);
    $('#preview\\.father-aadhar-number').text(studentInfo.studentFamilyInfo.fathersAadharNumber);
    $('#preview\\.mother-pan-card-details').text(studentInfo.studentFamilyInfo.mothersPanCardDetails);
    $('#preview\\.father-pan-card-details').text(studentInfo.studentFamilyInfo.fathersPanCardDetails);
    $('#preview\\.annual-family-income').text(studentInfo.studentFamilyInfo.approxFamilyIncome);

    if(studentInfo.studentGuardianInfoList != null && studentInfo.studentGuardianInfoList.length > 0){
      $('#preview\\.guardian-name').text(studentInfo.studentGuardianInfoList[0].guardianName);
      $('#preview\\.guardian-relation').text(studentInfo.studentGuardianInfoList[0].relation);
      $('#preview\\.guardian-age').text(studentInfo.studentGuardianInfoList[0].age);
      $('#preview\\.guardian-gender').text(studentInfo.studentGuardianInfoList[0].gender);
      $('#preview\\.guardian-occupation').text(studentInfo.studentGuardianInfoList[0].occupation);
      $('#preview\\.guardian-email').text(studentInfo.studentGuardianInfoList[0].email);
      $('#preview\\.guardian-contact-number').text(studentInfo.studentGuardianInfoList[0].contactNumber);
      $('#preview\\.guardian-address').text(getGuardianAddress(studentInfo.studentGuardianInfoList[0]));
    }else{
      $('#preview\\.guardian-name').text("");
      $('#preview\\.guardian-relation').text("");
      $('#preview\\.guardian-age').text("");
      $('#preview\\.guardian-gender').text("");
      $('#preview\\.guardian-occupation').text("");
      $('#preview\\.guardian-email').text("");
      $('#preview\\.guardian-contact-number').text("");
      $('#preview\\.guardian-address').text("");
    }

    $('#preview\\.previous-school-name').text(studentInfo.studentPreviousSchoolInfo.schoolName);
    $('#preview\\.medium').text(studentInfo.studentPreviousSchoolInfo.medium);
    $('#preview\\.class-passed').text(studentInfo.studentPreviousSchoolInfo.classPassed);
    $('#preview\\.year-of-passing').text(studentInfo.studentPreviousSchoolInfo.yearOfPassing);
    $('#preview\\.result').text(studentInfo.studentPreviousSchoolInfo.result);
    $('#preview\\.percentage-grade').text(studentInfo.studentPreviousSchoolInfo.percentage);
    $('#preview\\.student-is-admission-tc-based').text(studentInfo.studentPreviousSchoolInfo.admissionTcBasedDisplay);
    $('#preview\\.previous-school-tc-number').text(studentInfo.studentPreviousSchoolInfo.tcNumber);

    $('#preview\\.blood-group').text(studentInfo.studentMedicalInfo.bloodGroup);
    $('#preview\\.blood-pressure').text(studentInfo.studentMedicalInfo.bloodPressure);
    $('#preview\\.pulse').text(studentInfo.studentMedicalInfo.pulse);
    $('#preview\\.height').text(studentInfo.studentMedicalInfo.height);
    $('#preview\\.weight').text(studentInfo.studentMedicalInfo.weight);
    $('#preview\\.date-of-physical-examination').text(getFormattedDate(studentInfo.studentMedicalInfo.dateOfPhysicalExamination));
  }

}

var admissionStudentDetails = {
    changeSession: function() {
      searchStudents(true);
    }
};
function registerStudentSearchCallback(){
    $('#searchStudents').on('click', function () {
         searchStudents(true);
    });
    $("#searchStudentsInput").on('keyup', function (e) {
      if (e.keyCode == 13) {
          searchStudents(true);
      }
    });
}

function searchStudents(freshSearch){
      var status = $("#student-status").text().trim();
      var search_text = $("#searchStudentsInput").val();
      var academic_session_id = academicSessionHandler.getSelectedSessionId();
      var page_number = $('.page-item.active').find('.page-number').text().trim();
      if(freshSearch){
          page_number = 1;
      }
      var itemsPerPage = $('#items-per-page').val();
      if(page_number == null || page_number == ''){
        page_number = 1;
      }
      var offset = (page_number - 1)*itemsPerPage;
      $("#searchStudentsResult").html("");

      var requiredStandards = "";
      if($('#advance-search-option-div').css('display') != 'none') {
        requiredStandards = $('select.filter-student-classes').val();
        if(requiredStandards != undefined && requiredStandards != null) {
          requiredStandards = requiredStandards.join(",");
        }
      }
      if(status === "RELIEVED") {
        ajaxClient.get("/admission/relieved-student-search/"+academic_session_id+"/"+offset+"/"+itemsPerPage+"?text="+search_text+"&requiredStandards="+requiredStandards, function(data) {
            $("#searchStudentsResult").html(data);
            var screenHeight = $("#sidebar").height() - $("#searchStudentsResult").position().top - 50;
            $('#student-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
            var response = JSON.parse($("#pagination-info").text().trim());
            $('#items-per-page').val(response.itemsPerPage);
            $('.page-item').removeClass('active');
            var pageNumber = (response.offset/response.itemsPerPage) + 1;
            $('#page-number-'+page_number).addClass('active');
            registerViewStudentDetailsCallBack();
            registerEnrollStudentDetailsCallBack();
            registerUpdateStudentDetailsCallBack();
            registerUploadStudentDocumentCallBack();
            registerUpdateRelieveDetailsCallBack();
            registerTransferCertificateCallBack();
            studentList.initPagination();
            // Not best way. Adding additional bind events required for NSO student load page.
            //Since single html is being used for display, binding all the event here.
            //Arguent in this function tells where to go back if clicked on relieve student back button
            bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID);
            // $("#datatables-reponsive").DataTable({
            //   responsive: true
            // });
        });
      } else {
        ajaxClient.get("/admission/student-search/"+academic_session_id+"/"+status+"/"+offset+"/"+itemsPerPage+"?text="+search_text+"&requiredStandards="+requiredStandards, function(data) {
            $("#searchStudentsResult").html(data);
            var screenHeight = $("#sidebar").height() - $("#searchStudentsResult").position().top - 50;
            $('#student-fixed-height-list-wrapper').attr("style","height:"+screenHeight+"px;  overflow-y: scroll; cursor: pointer;");
            var response = JSON.parse($("#pagination-info").text().trim());
            $('#items-per-page').val(response.itemsPerPage);
            $('.page-item').removeClass('active');
            var pageNumber = (response.offset/response.itemsPerPage) + 1;
            $('#page-number-'+page_number).addClass('active');
            registerViewStudentDetailsCallBack();
            registerEnrollStudentDetailsCallBack();
            registerUpdateStudentDetailsCallBack();
            registerUploadStudentDocumentCallBack();
            registerUpdateRelieveDetailsCallBack();
            registerTransferCertificateCallBack();
            studentList.initPagination();
            // Not best way. Adding additional bind events required for NSO student load page.
            //Since single html is being used for display, binding all the event here.
            //Arguent in this function tells where to go back if clicked on relieve student back button
            bindStudentStatusChangeButtonCallBack(NSO_STUDENTS_LIST_SCREEN_ID);
            // $("#datatables-reponsive").DataTable({
            //   responsive: true
            // });
        });
      }
}

function registerViewStudentDetailsCallBack(){
  $('.view-student-info').on('click', function () {
    $('#view-student-details-modal').modal('toggle');
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    studentDetails.fillStudentViewDetails(studentInfo);
  });
}

function registerEnrollStudentDetailsCallBack(){
  $('.enroll-student-info').on('click', function () {
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    var studentId = studentInfo.studentId;
    var name = studentInfo.studentBasicInfo.name;
    var registrationDate = studentInfo.studentBasicInfo.registrationDate;
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    var response = $(".restrict-enrollment-payment-pending").text().trim();
    if(response == "True"){
      ajaxClient.get("/admission/verify-metadata/"+academicSessionId+"/"+studentId, function(data) {
        $("#relieve-student-confirmation-modal-container").html(data);
        var response = $("#success").text().trim();
        if(response == "False"){
          $("#enroll-status-modal").modal('toggle');
          return;
        }
        registerEnrollStudentDetails(studentInfoJson, registrationDate, academicSessionId, name, studentId);
      });
    }
    else{
      registerEnrollStudentDetails(studentInfoJson, registrationDate, academicSessionId, name, studentId);
    }
  });
}

function registerEnrollStudentDetails(studentInfoJson, registrationDate, academicSessionId, name, studentId){
  ajaxClient.get("/admission/enroll-metadata/"+academicSessionId+"/"+studentId, function(data) {
    $("#student-main-screen").html(data);
    $("#enroll-student-id").text(studentId);
    $("#enroll-student-name").text(name);
    $("#academic-session-id").text(academicSessionId);
    $("#enroll-student-registration-date").text(registrationDate);
    $("#student-details-json").text(studentInfoJson);
    $('.datatables-reponsive').DataTable( {
      paging:   true,
      searching: true,
    });
    assignTransportFees.transportBindEvents();
    assignTransportFees.resetTransportForm();
    discountStructure.initDataCache();
    initDateWithYearRange("-5:+5", true);
    bindSameAsRegistrationDateCheckbox();
    $('[data-toggle="tooltip"]').tooltip();
    bindSearchEnrollStudentEvent();
  });
}

function autoSuggestHouse(clearCurrentHouse, screen){
  var studentInfoJson = $('#student-details-json').text().trim();
  var studentInfo = JSON.parse(studentInfoJson);
  var studentId = studentInfo.studentId;
  var name = studentInfo.studentBasicInfo.name;
  var studentStandardId = studentInfo.studentAcademicSessionInfoResponse.standard.standardId;
  var gender = studentInfo.studentBasicInfo.gender;
  var academicSessionId = $("#academic-session-id").text();
  var siblingStudentId = $("#selected-sibling-student-id").text();
  if(siblingStudentId === "") {
    siblingStudentId = null;
  }
  var siblingGroupId = studentInfo.studentBasicInfo.siblingGroupId;
  var clearCurrentHouse = clearCurrentHouse;
  if(screen === "UPDATE") {
    gender = $("#update\\.student-gender").val();
  }
  if(gender === "" || gender === undefined) {
    gender = null;
  }
  $('#update\\.student-house').val("");
  $('#add-student-house').val("");
  var autoSuggestHousePayload = {'studentId' : studentId, 'studentStandardId' : studentStandardId, 'gender' : gender, 'siblingStudentId' : siblingStudentId, 'siblingGroupId' : siblingGroupId, 'clearCurrentHouse' : clearCurrentHouse};
  console.log(autoSuggestHousePayload);
    ajaxClient.post("/admission/auto-suggest-house/"+academicSessionId, {'autoSuggestHousePayload' : JSON.stringify(autoSuggestHousePayload)}, function(data) {
      $("#auto-suggest-house-span").html(data);
    });
}

function bindSearchEnrollStudentEvent() {
  var resultArea = "#student-payment-search-result";
  $('#student-payment-search').on('click', function () {
       // loadStudentPaymentDetails();
       doneStudentSearchTyping(resultArea);
  });
  $("#student-payment-search-text").on('keyup', function (e) {
    if (e.keyCode == 13) {
        // loadStudentPaymentDetails();
        doneStudentSearchTyping(resultArea);
    }
  });
  liveSearchHandler.bindEvent('#student-payment-search-text',resultArea,doneStudentSearchTyping);
}

function doneStudentSearchTyping (resultArea) {
  var searchText = $('#student-payment-search-text').val().trim();
  var sessionId = $("#academic-session-id").text().trim();
  var status = "ENROLLED";
  studentLiveSearchEvent(sessionId, searchText, resultArea, loadSiblingStudentDetails, status);
}

function studentLiveSearchEvent(sessionId, searchText, resultArea, triggerMethod, status){
  ajaxClient.get("/admission/student-live-with-session-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
      $(resultArea).html(data);
      studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
  });
}

function loadSiblingStudentDetails (studentId) {
  var academicSessionId = $("#academic-session-id").text().trim();
  ajaxClient.get( "/admission/student-sibling-details/"+academicSessionId + "/" + studentId, function(data) {
      $("#sibling-listing-div").html(data);
  });
}

function bindSameAsRegistrationDateCheckbox() {
  $('#same-as-registration-date').change(function() {
    var sameAsRegistrationDate = $("#same-as-registration-date").is(":checked");
    var todayDate = new Date();
    var registrationDate = getDate(todayDate).getTime()/1000;
    if(sameAsRegistrationDate) {
      registrationDate = $("#enroll-student-registration-date").text();
    }
    setFormattedDate(registrationDate, '#enroll\\.student-admission-date');
  });
}

function removeEnrollSiblingDetails() {
  $("#sibling-listing-div").html("");
}

var assignTransportFees = {
    dataCache : {},

    transportBindEvents : function () {
      assignTransportFees.initDataCache();
      assignTransportFees.bindDivideEquallyCheck();
      assignTransportFees.bindRouteSelectEvent();
      assignTransportFees.bindFullSessionTransportCheck();
    },

    bindDivideEquallyCheck: function(){
      $('#divide-equally-check').change(function() {
           if($(this).is(":checked")) {
             $('.transport-fees-proportion-input').prop("readonly", true);
             var counter = 1;
             $('.transport-fee-checkbox').each(function(i, obj) {
                if(!$(this).is(":checked")){
                   return;
                }
                counter += 1;
             });
             if(counter > 1){
                 var proportion = 100/(counter-1);
                 $(".transport-fees-proportion-input").val(Math.round(proportion * 100) / 100);
             }
           }
           else{
              $('.transport-fees-proportion-input').prop("readonly", false);
           }
       });
    },

    initDataCache : function () {
      var routes = readJson("p.trasport-service-route-json");
      var feeConfigs = readJson("p.trasport-fee-config-json");
      assignTransportFees.dataCache['routes'] = routes;
      assignTransportFees.dataCache['feeConfigs'] = feeConfigs;
      assignTransportFees.dataCache.transportAssignmentPayload = null;
    },

    createRouteOptionList : function () {
      var routes = assignTransportFees.dataCache["routes"];
      var routeOptionList = "<option value=\"\">--select--</option>";
      for(var i =0; i < routes.length; i++){
        routeOptionList += "<option value=\""+routes[i].serviceRouteId+"\">"+routes[i].serviceRouteName+"</option>";
      }
      return routeOptionList;
    },

    resetTransportForm : function () {
      $("#trasnport-assign-footer").attr("style","");
      $("#transport-assignment-area").val("");
      $("#transport-assignment-area").attr("disabled", false);

      $("#pickup-assignment-service-route").html("<option value=\"\">--select--</option>");
      $("#drop-assignment-service-route").html("<option value=\"\">--select--</option>");
      $("#pickup-assignment-service-route").attr("disabled", false);
      $("#drop-assignment-service-route").attr("disabled", false);
      assignTransportFees.bindRouteAreaSelectEvent();


      $('#complete-session-transport').prop('checked', true);
      $('#complete-session-transport').prop('disabled', false);
      $("#transport-assign-dates").attr("style","display:none;");
      $('.transport-fees-amount-input').prop("readonly", true);

      $("#transport-start-date").val("");
      $("#transport-end-date").val("");
      $('#transport-start-date').prop("readonly", false);
      $('#transport-end-date').prop("readonly", false);
      $("#transport-start-date").css('pointer-events', '');
      $("#transport-end-date").css('pointer-events', '');

      $("input.transport-fees-amount-input").each(function() {
        $(this).val(0);
      });
    },

    bindRouteSelectEvent: function() {
      $("#pickup-assignment-service-route").change(function() {
          assignTransportFees.computeTransportFeeAmount();
      });
      $("#drop-assignment-service-route").change(function() {
          assignTransportFees.computeTransportFeeAmount();
      });
    },

    getAreaOptions: function(routes, routeId) {
      var areaOptionList = "<option value=\"\">--select--</option>";
      for(var i =0; i < routes.length; i++){
        if(routes[i].serviceRouteId == routeId){
          var route = routes[i];
          for(var j = 0; j < route.stoppagesList.length; j++){
              areaOptionList += "<option value=\""+route.stoppagesList[j].transportArea.areaId+"\">"+route.stoppagesList[j].transportArea.area+"</option>";
          }
          break;
        }
      }
      return areaOptionList;
    },

    getRouteOptions: function(routes, routeType, areaId) {
      var routeOptionList = "<option value=\"\">--select--</option>";
      for(var i =0; i < routes.length; i++){
        var route = routes[i];
        if(route.transportServiceRouteMetadata.routeType != routeType){
          continue;
        }
        for(var j = 0; j < route.stoppagesList.length; j++){
          var stoppage = route.stoppagesList[j];
          if(stoppage.transportServiceRouteStoppageDetails.transportArea.areaId.toString() == areaId){
            routeOptionList += "<option value=\""+route.transportServiceRouteMetadata.serviceRouteId+"\">"+route.transportServiceRouteMetadata.serviceRouteName+"</option>";
            break;
          }
        }
      }
      return routeOptionList;
    },

    bindRouteAreaSelectEvent: function() {
        $("#transport-assignment-area").change(function() {
            var areaId = $(this).find(':selected').val().trim();
            assignTransportFees.populateServiceRoutesForArea(areaId);

        });
    },

    populateServiceRoutesForArea : function (areaId) {
      var routes = assignTransportFees.dataCache["routes"];
      var pickupRouteOptionList = assignTransportFees.getRouteOptions(routes, "PICK_UP", areaId);
      var dropRouteOptionList = assignTransportFees.getRouteOptions(routes, "DROP_OFF", areaId);

      $("#pickup-assignment-service-route").html(pickupRouteOptionList);
      $("#drop-assignment-service-route").html(dropRouteOptionList);

      assignTransportFees.bindRouteSelectEvent();
    },

    computeTransportFeeAmount: function(routeId, areaId) {
      var pickupRouteId = $("#pickup-assignment-service-route").find(':selected').val().trim();
      var dropRouteId = $("#drop-assignment-service-route").find(':selected').val().trim();
      var areaId = parseInt($("#transport-assignment-area").find(':selected').val().trim());

      var routes = assignTransportFees.dataCache["routes"];
      var totalAmount = 0;
      for(var i = 0; i < routes.length; i++){
        var route = routes[i];
        if(route.transportServiceRouteMetadata.routeType == "PICK_UP" && pickupRouteId == route.transportServiceRouteMetadata.serviceRouteId){
          for(var j = 0; j < route.stoppagesList.length; j++){
            var stoppage = route.stoppagesList[j];
            if(stoppage.transportServiceRouteStoppageDetails.transportArea.areaId == areaId && stoppage.amount != null && stoppage.amount > 0){
                totalAmount += stoppage.amount;
                break;
            }
          }
        }

        if(route.transportServiceRouteMetadata.routeType == "DROP_OFF" && dropRouteId == route.transportServiceRouteMetadata.serviceRouteId){
          for(var j = 0; j < route.stoppagesList.length; j++){
            var stoppage = route.stoppagesList[j];
            if(stoppage.transportServiceRouteStoppageDetails.transportArea.areaId == areaId && stoppage.amount != null && stoppage.amount > 0){
                totalAmount += stoppage.amount;
                break;
            }
          }
        }
      }

      var transportFeeConfig = assignTransportFees.dataCache["feeConfigs"];
      var fees = transportFeeConfig.transportConfiguredFeeDatas;
      for(var i = 0; i < fees.length; i++){
        var feeAmount = totalAmount*fees[i].moduleFeeProportion.feeProportion;
        $("#"+fees[i].feeConfigurationResponse.feeConfigurationBasicInfo.feeId).val(round(feeAmount,2));
      }
    },

    bindFullSessionTransportCheck: function(){
      $('#complete-session-transport').change(function() {
           if($(this).is(":checked")) {
             $("#transport-assign-dates").attr("style","display:none");
             $('.transport-fees-amount-input').prop("readonly", true);
             assignTransportFees.computeTransportFeeAmount();
           }
           else{
              $("#transport-assign-dates").attr("style","");
              $('.transport-fees-amount-input').prop("readonly", false);
           }
       });
    },

    addTransportService: function() {
      var pickupServiceRouteId = $("#pickup-assignment-service-route").find(':selected').val().trim();
      var dropServiceRouteId = $("#drop-assignment-service-route").find(':selected').val().trim();
      var areaId = $("#transport-assignment-area").find(':selected').val().trim();
      var studentId = $("#enroll-student-id").text().trim();
      var academicSessionId = $("#academic-session-id").text().trim();

      if(areaId === "") {
        return true;
      } else {
        if(pickupServiceRouteId === "" && dropServiceRouteId === "") {
          alert("Please select either a pickup route or drop route to assign transport!");
          return false;
        }
      }
      var completeSessionTransport = $("#complete-session-transport").is(":checked");
      var startDate = 0;
      var endDate = 0;
      if(!completeSessionTransport){
         var startDateTime = $('#transport-start-date').val();
         var endDateTime = $('#transport-end-date').val();

         if(startDateTime == null || endDateTime == null || startDateTime == undefined || endDateTime == undefined || startDateTime === "" || endDateTime === "") {
           alert("Service start and end date are mandatory");
           return false;
         }

         startDate = getDate(startDateTime).getTime()/1000;
         endDate = getDate(endDateTime).getTime()/1000;
      }
      var transportHistoryFeeIdAmountList = [];
      $("input.transport-fees-amount-input").each(function() {
         var feeId = $(this).attr("id");
         var amount = parseFloat($(this).val());
         transportHistoryFeeIdAmountList.push({'feeId' : feeId, 'amount' : amount});
      });
      var transportAssignmentPayload = {'academicSessionId' : academicSessionId, 'studentId' : studentId, 'pickupServiceRouteId': pickupServiceRouteId, 'dropServiceRouteId' : dropServiceRouteId, 'areaId' :  areaId , 'startDate' : startDate , 'endDate': endDate, 'completeSession' :  completeSessionTransport, 'transportHistoryFeeIdAmountList' : transportHistoryFeeIdAmountList};
      assignTransportFees.dataCache.transportAssignmentPayload = transportAssignmentPayload;
      return true;
    },
}

function registerUpdateStudentDetailsCallBack(){
  $('.update-student-info').on('click', function () {
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    fillStudentInformation(studentInfo);
    bindAdmissionClassSectionEvent();
    enforceConstraints();
    permanentAddressUpdate("update-permanent-same-as-present", "update\\.student-permanent-address", "update\\.student-city", "update\\.student-state", "update\\.student-post-office", "update\\.student-police-station",
        "update\\.student-zipcode", "update\\.student-present-address", "update\\.student-present-city", "update\\.student-present-state", "update\\.student-present-post-office", "update\\.student-present-police-station", "update\\.student-present-zipcode");
    $("#student-details-json").text(studentInfoJson);
    $("#academic-session-id").text(academicSessionId);
    $("#auto-suggest-house-span").text("");
    $('.student-details-screen').attr('style','display:none');
    $('#student-list\\.update-student-screen').attr('style','display:block');
    $('#screen-name').text('STUDENT-LIST');
  });
}

function registerUpdateRelieveDetailsCallBack() {
  $('.update-student-relieve-details').on('click', function () {
    $("#relieve-student-confirmation-modal").modal("toggle");
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    var studentId = studentInfo.studentId;
    $("#p-student-id").text(studentId);
    initDateInput();
    bindRelieveReason();
    setFormattedDate(studentInfo.studentBasicInfo.relieveDate, "#relieve-date");
    $(".relieve-reason").val(studentInfo.studentBasicInfo.relieveReason);
    $("#code-of-conduct").val(studentInfo.tcVariables == null ? "" : studentInfo.tcVariables['CODE_OF_CONDUCT']);
    $("#last-active-session").val(studentInfo.studentBasicInfo.relievedMetadata == null ? "" : studentInfo.studentBasicInfo.relievedMetadata['LAST_ACTIVE_SESSION']);
    $("#is-last-session-completed").val(studentInfo.studentBasicInfo.relievedMetadata == null ? "" : studentInfo.studentBasicInfo.relievedMetadata['LAST_ACTIVE_SESSION_COMPLETED']);
  });
}

function registerTransferCertificateCallBack() {
  $('.generate-student-tc-form').on('click', function () {
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    var studentId = studentInfo.studentId;
    var isDetailedTcEnabled = $("#is-detailed-tc-enabled").text();
    if(isDetailedTcEnabled === "true") {
      ajaxClient.get("/admission/student-tc-details/"+studentId, function(data) {
        $('#student-list\\.generate-tc-document-screen').html(data);
        $('.student-details-screen').attr('style','display:none');
        $('#student-list\\.generate-tc-document-screen').attr('style','display:block');
        $("#tc-generation-student-id").text(studentInfo.studentId);
        $("#tc-generation-student-name").text(studentInfo.studentBasicInfo.name);
        initDateWithYearRange("-20:+5", false);
        initPastDateById("tc-generation-date", 2000);
        initPastDateById("tc-relieve-date", 2000);
      });
    } else {
      transferCertificate.generateStaticTC(studentId);
    }
  });
}

function registerUploadStudentDocumentCallBack(){
  $('.upload-student-document').on('click', function () {
    var studentInfoJson = $(this).parent().parent().find('.student-info-json').text().trim();
    var studentInfo = JSON.parse(studentInfoJson);
    $("#upload-document-student-id").text(studentInfo.studentId);
    $('.student-details-screen').attr('style','display:none');
    $('#student-list\\.upload-document-screen').attr('style','display:block');
    $("#upload-document-student-name").text(studentInfo.studentBasicInfo.name);
    populateUploadedDocuments(studentInfo.studentDocuments);
  });

  $("#upload-student-document-type").change(function() {
      var documentType = $(this).find(':selected').val().trim();
      if(documentType == OTHER_DOCUMENT_TYPE){
        $("#upload-student-document-name").parent().attr("style","display:block");
      }else{
        $("#upload-student-document-name").parent().attr("style","display:none");
      }
  });
  registerUploadFileCallback();
}

function bindStudentDocumentActions() {
  $('.download-student-document').on('click', function () {
      var studentId = $("#upload-document-student-id").text().trim();
      var documentId = $(this).parent().find('p.view-document-id').text().trim();
      window.open(baseURL+"/admission/document-download/"+studentId+"/"+documentId, '_blank');
  });

  $('.delete-student-document').on('click', function () {
      var documentId = $(this).parent().find('p.view-document-id').text().trim();
      $("#student-document-delete-confirm-button").attr("onclick","deleteStudentDocument('"+documentId+"')");
      $("#student-document-delete-confirm-modal").modal({backdrop: 'static', keyboard: false});
  });
}

function deleteStudentDocument(documentId) {
    var studentId = $("#upload-document-student-id").text().trim();
    ajaxClient.post("/admission/document-delete/"+studentId+"/"+documentId,{},function(data){
      $("#student-document-status-modal-container").html(data);
      $("#student-document-delete-status-modal").modal({backdrop: 'static', keyboard: false});
      var studentDocumentsJson = $("#success-document-delete-response").text().trim();
      var studentDocuments = JSON.parse(studentDocumentsJson);
      populateUploadedDocuments(studentDocuments);
    });
}

function populateUploadedDocuments(studentDocuments) {
    if(studentDocuments == null || studentDocuments.length == 0){
      $("#student-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
      return;
    }
    var documentsList = "<br>";
    var itemsPerRow = 3;
    var numberOfDocument = 0;
    for(var i = 0 ; i < studentDocuments.length; i++){
      var studentDocument = studentDocuments[i];
      if(studentDocument.documentType === "STUDENT_PROFILE_IMAGE_THUMBNAIL") {
        continue;
      }
      if(studentDocument.documentId == null) {
        continue;
      }
      if(numberOfDocument % 3 == 0){
          if(numberOfDocument != 0){
              documentsList += "</div>";
          }
          documentsList += "<div class=\"row\">";
      }
      numberOfDocument++;
      var uplaodTimeText = "Uploaded on : " + getFormattedDate(studentDocument.uploadTime);
      documentsList += "<div class=\"col-sm-4\"> <div class=\"card bg-light text-center\"> <div class=\"card-header\"> <h5> <strong> "+ studentDocument.documentName + " </strong></h5> </div> <div class=\"card-body\"> <p style=\"display:none;\" class=\"view-document-id\"> "+ studentDocument.documentId + " </p> <p class=\"card-text\"> Category : "+ studentDocument.documentTypeDisplayName+" </p> <a href=\"#\" class=\"btn btn-outline-info download-student-document\">Download </a> <a href=\"#\" class=\"btn btn-outline-danger delete-student-document\">Delete </a> </div> <div class=\"card-footer text-muted\"> "+ uplaodTimeText + " </div> </div> </div>"
    }
    documentsList += "</div> <br>";
    $("#student-uploaded-documents").html(documentsList);
    bindStudentDocumentActions();
}

function resetNewDocumentUploadPopup () {
    $("#upload-student-document-type").val("");
    $("#upload-document-file").val("");
    $("#upload-document-file-label").text("");
    $("#upload-student-document-name").val("");
    $("#upload-student-document-name").parent().attr("style","display:none");
}

async function uploadStudentDocument() {
    var studentId = $("#upload-document-student-id").text();
    var documentType = $("#upload-student-document-type option:selected").val().trim();
    if(documentType == ""){
      showErrorDialogBox("Document type field is mandatory please fill it then proceed.");
      return;
    }
    var file ;
    var ONE_KB = 1024;
    var FILE_SIZE_LIMIT = 500;
    if (($("#upload-document-file"))[0].files.length > 0) {
        var uncompressedFile = ($("#upload-document-file"))[0].files[0];
        console.log(uncompressedFile);
        file = await compressFileUtils.compress(uncompressedFile);
        console.log(file);
        console.log("final file size : " + file.size);
        if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
          showErrorDialogBoxWithExistingModalDetails("File size exceeds " + FILE_SIZE_LIMIT + " KB after compression. Please reduce the file size and try uploading again.", "#upload-new-document-modal");
          return;
        }
    } else {
        showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload", "#upload-new-document-modal");
        return;
    }
    var documentName = "";
    if(documentType == OTHER_DOCUMENT_TYPE){
      documentName = $("#upload-student-document-name").val();
      if(documentName == "") {
        showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed", "#upload-new-document-modal");
        return;
      }
    }
    var formData = new FormData();
    formData.append('document', file);
    formData.append('documentType', documentType);
    formData.append('documentName', documentName);
    formData.append('studentId', studentId);
    $("#upload-new-document-modal").modal("toggle");
    ajaxClient.uploadFile("/admission/document-upload", formData, function(data){
        $("#student-document-status-modal-container").html(data);
        $("#student-document-upload-status-modal").modal({backdrop: 'static', keyboard: false});
        var studentDocumentsJson = $("#success-document-upload-response").text().trim();
        var studentDocuments = JSON.parse(studentDocumentsJson);
        populateUploadedDocuments(studentDocuments);
    });
}

function admitStudent(){
  var studentId = $("#enroll-student-id").text().trim();

  var admissionNumber = $("#enroll\\.student-admission-number").val().trim();
  if(admissionNumber === "") {
    showErrorDialogBox("Admission Number cannot be empty");
    return;
  }

  var admissionDate = getDate($("#enroll\\.student-admission-date").val());
  var admissionDateInt = null;
  if(admissionDate != null){
    admissionDateInt = admissionDate.getTime()/1000;
  }

  var siblingStudentId = $("#selected-sibling-student-id").text();
  if(siblingStudentId == undefined || siblingStudentId === "" || siblingStudentId === null) {
    siblingStudentId = null;
  }
  var houseId = $("#add-student-house").val();
  if(houseId == undefined || houseId === "" || houseId === null) {
    houseId = null;
  }

  var applicableFeeStructures = [];
  $("tr.fee-structure-row").each(function() {
    if(!$(this).find('.custom-control-input:checkbox:checked').length > 0) {
        return;
    }
    var feeStructureId = $(this).attr('id');
    applicableFeeStructures.push(feeStructureId);
  });
  var holidayTemplateId = $("#holiday-student-template").find(':selected').val();
  if(holidayTemplateId == "" || holidayTemplateId === undefined || holidayTemplateId === null){
    holidayTemplateId = null;
  }
  else{
    holidayTemplateId = holidayTemplateId.trim();
  }
  var courseIds = [];
  $("tr.course-row").each(function() {
    if(!$(this).find('.custom-control-input:checkbox:checked').length > 0) {
        return;
    }
    var courseId = $(this).attr('id');
    courseIds.push(courseId);
  });
  var sendNotification = $("#send-admission-notification").is(":checked");

  var sendEmail = $("#send-admission-email-notification").is(":checked");

  var discountStructureIds = [];
  $("tr.discount-structure-row").each(function() {
    if(!$(this).find('.custom-control-input:checkbox:checked').length > 0) {
        return;
    }
    var discountStructureId = $(this).attr('id');
    discountStructureIds.push(discountStructureId);
  });



  var entityDiscountStructurePayload = discountStructure.dataCache.discountAssignmentStructurePayload;
  if(!assignTransportFees.addTransportService()) {
    return;
  }
  var transportAssignmentPayload = assignTransportFees.dataCache.transportAssignmentPayload;
  var enrollStudentPayload = {'studentId' : studentId, 'admissionNumber' : admissionNumber,
      'admissionDate' : admissionDateInt, 'houseId' : houseId, 'siblingStudentId' : siblingStudentId,
      'feeStructureIds' : applicableFeeStructures, 'optionalCourseIds' : courseIds, 'sendNotification' : sendNotification,
      'sendEmail' : sendEmail, 'discountStructureIds' : discountStructureIds, 'entityDiscountStructurePayload' : entityDiscountStructurePayload,
      'transportAssignmentPayload' : transportAssignmentPayload, 'holidayTemplateId' : holidayTemplateId};
  
  ajaxClient.post("/admission/admit-student",{'admissionDetails':JSON.stringify(enrollStudentPayload)}, function(data) {
      $("#relieve-student-confirmation-modal-container").html(data);
      $("#admission-status-modal").modal('toggle');
      var response = JSON.parse($("#student-data-response").text().trim());
      if(response.success){
        reloadPendingEnrolementStudentDetailsPage();
      }
    });
}

var discountStructure = {

  dataCache : {},
  INSTITUTE_ENTITY : "institute",

  initDataCache: function () {
    var feeData = readJson('#fee-data');
    var standardsMap = {};
    var feeMap = {};
    var feeHeadMap = {};
    var feeIdFeeHeadSelectOptionData = {};
    var feeConfigurationBasicInfoList = feeData.feeConfigurationBasicInfoList;
    var feeHeadConfigurationResponseList = feeData.feeHeadConfigurationResponseList;
    for(var i = 0; i < feeConfigurationBasicInfoList.length; i++) {
      var feeConfigurationBasicInfo = feeConfigurationBasicInfoList[i]
      feeMap[feeConfigurationBasicInfo.feeId] = feeConfigurationBasicInfo;
      var feeHeadSelect =  discountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, "");
      feeIdFeeHeadSelectOptionData[feeConfigurationBasicInfo.feeId] = feeHeadSelect;
    }

    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++) {
      var feeHeadConfigurationResponse = feeHeadConfigurationResponseList[i]
      feeHeadMap[feeHeadConfigurationResponse.feeHeadConfiguration.feeHeadId] = feeHeadConfigurationResponse;
    }

    discountStructure.dataCache = {};
    discountStructure.dataCache.feeMap = feeMap;
    discountStructure.dataCache.feeHeadMap = feeHeadMap;
    discountStructure.dataCache.feeHeadConfigurationResponseList = feeHeadConfigurationResponseList;
    discountStructure.dataCache.configureNewStructure = {'institute' : {'selectedFees' : {}}, 'standard' : {'selectedStandards' : {}, selectedFees : {} }};
    discountStructure.dataCache.feeIdFeeHeadSelectOptionData = feeIdFeeHeadSelectOptionData;
    discountStructure.dataCache.discountAssignmentStructurePayload = null;
  },

  createFeeHeadSelectMenu : function (feeHeadConfigurationResponseList , selectedFeeHeadId) {
    var feeHeadSelect = "<select class=\"form-control form-control-sm mandatory-field fee-head\"> <option value=\"\">select</option>";
    for(var i = 0; i < feeHeadConfigurationResponseList.length; i++){
      var feeHeadId = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHeadId;
      var feeHeadName = feeHeadConfigurationResponseList[i].feeHeadConfiguration.feeHead;
      if(selectedFeeHeadId == feeHeadId){
        feeHeadSelect += "<option value=\""+feeHeadId+"\" selected>"+feeHeadName+"</option>";
      }else{
        feeHeadSelect += "<option value=\""+feeHeadId+"\">"+feeHeadName+"</option>";
      }
    }
    feeHeadSelect += "</select>"
    return feeHeadSelect;
  },

  loadNewFeeStructureConfigureModal : function() {
    $("#discount-structure-config-modal").modal('toggle');
    // Clear old dataType
    discountStructure.resetDiscountStructureModal();
    $("#discount-structure-config-modal").find(".modal-title").html("Assign Instant Discounts");
    $("#submit-fee-structure").html("Assign Instant Discounts");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");
    // Clear the older values
    discountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedStandards = {};
    discountStructure.dataCache.configureNewStructure.standard.selectedFees = {};
    discountStructure.dataCache.selectedStructureId = null;

    discountStructure.populateSelectFeesDropdown(".institute-structure", discountStructure.INSTITUTE_ENTITY, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
  },

  resetDiscountStructureModal : function () {
    $("#institute-fee-structure-container").html("<p class=\"institute-add-structure-hint-text pb-7 pt-7\"> Select fees to add new discount structure</p>");
    $("#discount-structure-name").val("");
    $("#discount-structure-name").attr("disabled",false);
    $("#discount-structure-config-modal").find(".modal-footer").css("display","");

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = discountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    discountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  bindSelectFeesEvent : function (entityParentContainerId, entityId, selectedFees) {
    $(entityParentContainerId).find('.fee-select-option').on('click', function (event) {
      //This is for keeping the fees drop-down open (https://embrate.atlassian.net/browse/PD-2535)
      event.stopPropagation();
      var feeId = $(this).attr("id").split("-select-")[0];
      var entityId = $(this).attr("id").split("-select-")[1];
      discountStructure.insertFeeDetailsCard(entityParentContainerId, entityId, feeId, selectedFees);
    });
  },

  bindStandardSelectEvent : function () {
    $('.class-fee-structure').find('.standard-select-option').on('click', function () {
      var standardId = $(this).attr("id").split("-select")[0];
      discountStructure.insertStandardDetailsCard(standardId);
    });
  },

  insertFeeDetailsCard : function (entityParentContainerId, entityId, feeId, selectedFees) {
    selectedFees[feeId] = true;
    $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:none;");

    var entityContainerId = discountStructure.createEntityContainerId(entityId);
    var feeContainerId = discountStructure.createFeeContainerId(entityId,feeId);
    var feeMap = discountStructure.dataCache.feeMap;
    var feeName = feeMap[feeId].feeName.toUpperCase();
    var feeDetailsCard = "<div class=\"fee-structure\"> <div class=\"card card-border text-center\" id=\""+feeContainerId+"\"> <p class=\"fee-id\" style=\"display:none;\">"+feeId+"</p> <div class=\"card-body\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-fee-structure-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h5 class=\"card-title\"> <strong>"+feeName+"</strong> </h5> <div style=\"padding-left:15%; padding-right:15%;\"> <table class=\"table table-borderless\"> <thead> <tr> <th scope=\"col\" class=\"w-50\">Fee Head</th> <th scope=\"col\">Amount</th> <th scope=\"col\">Percentage</th> <th scope=\"col\"></th> </tr> </thead> <tbody class=\"fee-structure-body\"> </tbody> </table> <button type=\"button\" class=\"btn btn-outline-secondary btn-sm add-fee-head-button\"> + Add More Fee Head</button> </div> </div> </div> </br> <div>";
    $("#"+entityContainerId).append(feeDetailsCard);
    discountStructure.bindFeeContainerEvents(entityParentContainerId, feeContainerId, entityId, feeId, selectedFees);
    discountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);

  },

  insertStandardDetailsCard : function (standardId) {
    discountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId] = true;
    $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:none;");
    var standardMap = discountStructure.dataCache.standardsMap;
    var standardName = standardMap[standardId].standardName.toUpperCase();
    var standardContainerId = discountStructure.getStandardContainerId(standardId);
    var standardDetailsCard = "<div id=\""+standardContainerId+"\" class=\"card card-border standard-fee-structure\"> <p class=\"standard-id\" style=\"display:none;\">"+standardId+"</p> <div class=\"card-header card-header-color\" id=\""+standardId+"-heading\" data-toggle=\"collapse\" data-target=\"#collapse-"+standardId+"\" aria-expanded=\"true\" aria-controls=\"collapse-"+standardId+"\"> <div style=\"float:right;\"> <button type=\"button\" class=\"close delete-standard-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </div> <h2 class=\"mb-0\"> <button class=\"btn btn-link\" type=\"button\"> <strong>"+standardName+"</strong> </button> </h2> </div> <div id=\"collapse-"+standardId+"\" class=\"collapse\" aria-labelledby=\""+standardId+"-heading\" data-parent=\"#class-fee-structure-config-accordion\"> <div class=\"card-body\"> <div id=\""+standardId+"-fee-structure-container\" style=\"text-align:center;\"> <p class=\"institute-add-structure-hint-text black-color\"> Select fees to add new fee structure</p> <!-- Next card here --> </div> <br /> <div class=\"fee-select-dropdown-container\" style=\"float:right;\"> <div class=\"btn-group dropup\" style=\"width:150px;\"> <button type=\"button\" class=\"btn btn-info btn-sm dropdown-toggle\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"> Select Fees </button> <div class=\"dropdown-menu scrollable-dropdown fee-select-dropdown dropdown-menu-right\"> </div> </div> </div> <br/> </div> </div> </div>";
    $("#class-fee-structure-config-accordion").append(standardDetailsCard);
    discountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId] = {};
    discountStructure.populateSelectFeesDropdown("#"+standardContainerId, standardId, discountStructure.dataCache.configureNewStructure.standard.selectedFees[standardId]);
    // discountStructure.bindStandardContainerEvents(standardContainerId, standardId);
  },

  createEntityContainerId : function (entityId) {
    return entityId + "-" + "fee-structure-container";
  },

  createFeeContainerId : function (entityId, feeId) {
    return entityId + "-" + feeId;
  },

  bindFeeContainerEvents : function (entityParentContainerId, feeContainerId, entityId, feeId, selectedFees) {
    $("#"+feeContainerId).find(".add-fee-head-button").on('click', function () {
      discountStructure.insertFeeHeadRow(feeContainerId, feeId, null, 0, "");
    });

    $("#"+feeContainerId).find(".delete-fee-structure-row").on('click', function () {
      $(this).closest(".fee-structure").remove();
      delete selectedFees[feeId];
      if(isEmpty(selectedFees)){
        $(entityParentContainerId).find(".institute-add-structure-hint-text").attr("style","display:block;");
      }
      discountStructure.populateSelectFeesDropdown(entityParentContainerId, entityId, selectedFees);
    });

  },

  populateSelectFeesDropdown : function (entityParentContainerId, entityId, selectedFees) {
    var feeMap = discountStructure.dataCache.feeMap;
    var feeDropdownListContent = "";
    for(feeId in feeMap){
      if(!(feeId in selectedFees)){
          feeDropdownListContent += "<a class=\"dropdown-item fee-select-option\" href=\"#\" id=\"" + feeId +"-select-"+entityId+"\">" + feeMap[feeId].feeName +"</a>";
      }
    }
    $(entityParentContainerId).find(".fee-select-dropdown").html(feeDropdownListContent);
    discountStructure.bindSelectFeesEvent(entityParentContainerId, entityId, selectedFees);
  },

  getStandardContainerId : function (standardId) {
      return standardId + "-container";
  },

  bindStandardContainerEvents : function (standardContainerId, standardId) {

    $("#"+standardContainerId).find(".delete-standard-row").on('click', function () {
      $(this).closest(".standard-fee-structure").remove();
      delete discountStructure.dataCache.configureNewStructure.standard.selectedStandards[standardId];
      if(isEmpty(discountStructure.dataCache.configureNewStructure.standard.selectedStandards)){
          $("#class-fee-structure-config-accordion").find(".standrad-add-structure-hint-text").attr("style","display:block;");
      }
    });

  },

  insertFeeHeadRow  :function (feeContainerId, feeId , selectedFeeHeadId, feeHeadAmount, checked) {
    var feeHeadRow = "";
    if(selectedFeeHeadId == null){
      var feeHeadSelectOptionData = discountStructure.dataCache.feeIdFeeHeadSelectOptionData[feeId];
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"> <span class=\"slider round\"></span> </label> </td><td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }else{
      var feeHeadConfigurationResponseList = discountStructure.dataCache.feeHeadConfigurationResponseList;
      var feeHeadSelectOptionData =  discountStructure.createFeeHeadSelectMenu(feeHeadConfigurationResponseList, selectedFeeHeadId);
      if(checked) {
        checked = "checked";
      }
      else {
        checked = "";
      }
      feeHeadRow = "<tr id=\"\"> <td> "+feeHeadSelectOptionData+" </td> <td> <input type=\"number\" class=\"form-control form-control-sm fee-head-amount\" aria-describedby=\"\" placeholder=\"Enter amount...\" value=\""+feeHeadAmount+"\"> </td> <td> <label class=\"switch\"> <input type=\"checkbox\" class=\"primary toggle-switch is-percentage-switch\"" + checked + " /><span class=\"slider round\"></span> </label> </td> <td> <button type=\"button\" class=\"close delete-fee-head-row\" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button> </td> </tr>";
    }

    $("#"+feeContainerId).find(".fee-structure-body").append(feeHeadRow);
    discountStructure.bindFeeHeadRowEvents(feeContainerId);
  },

  bindFeeHeadRowEvents : function (feeContainerId) {
      var previousSelectedValue = "";
      $("#"+feeContainerId).find(".fee-head").on('focus', function () {
        // Store the current value on focus and on change
        previousSelectedValue = $(this).find(':selected').val().trim();
      })
      .change(function () {
          var feeHeadId = $(this).find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          var presenceCount = 0;
          $(this).closest('tbody.fee-structure-body').find("tr").each(function() {
            var existingFeeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
            if(existingFeeHeadId == ""){
              return;
            }else if(feeHeadId == existingFeeHeadId){
              presenceCount++;
            }
          });
          if(presenceCount > 1){
            showErrorDialogBox("Fee Head " + $(this).find(':selected').text() + " already selected. Please fill amount in that");
            $(this).val(previousSelectedValue);
          }
      });

      $("#"+feeContainerId).find(".delete-fee-head-row").on('click', function () {
        $(this).closest("tr").remove();
      });

      $("#"+feeContainerId).find(".fee-head-amount").change(function() {
        var isPercentage = $(this).parent().parent().find(".is-percentage-switch").is(':checked');
        if(isPercentage) {
          var percentage = $(this).val();
          if(percentage > 100 || percentage <= 0) {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).val("");
            return;
          }
        } else {
          var amount = $(this).val();
          if(amount <= 0 && amount != "") {
            showErrorDialogBox("Amount should be a positive number.");
            $(this).val("");
            return;
          }
        }
      });

      $("#"+feeContainerId).find(".is-percentage-switch").change(function() {
        if($(this).is(':checked')) {
          var percentage = $(this).parent().parent().parent().find(".fee-head-amount").val();
          if((percentage > 100 || percentage <= 0) && percentage != "") {
            showErrorDialogBox("Percentage should be between 1 to 100.");
            $(this).parent().parent().parent().find(".fee-head-amount").val("");
            return;
          }
        }
      });
  },

  submitDiscountStructure : function () {
    var structureName = $("#discount-structure-name").val();
    if(structureName == "") {
      alert("Discount Detail is mandatory!");
      return;
    }
    var academicSessionId = $("#academic-session-id").text().trim();

    var structureId = null;
    if(discountStructure.dataCache.selectedStructureId != null && discountStructure.dataCache.selectedStructureId.trim() != "" ){
      structureId = discountStructure.dataCache.selectedStructureId;
    }

    var instituteFeeIdFeeHeadsList =  discountStructure.getFeeStructureData('#institute-fee-structure-container');

    if(Array.isArray(instituteFeeIdFeeHeadsList) && !instituteFeeIdFeeHeadsList.length) {
      return;
    }
    var instituteFeeAssignmentPayload = {'feeEntity' : 'INSTITUTE', 'feeIdFeeHeadsList' : instituteFeeIdFeeHeadsList};

    var entityFeeAssignmentPayloadList = [];
    entityFeeAssignmentPayloadList.push(instituteFeeAssignmentPayload);

    var metadata = {'title' : structureName};
    var discountAssignmentStructurePayload = {'structureId': structureId, 'discountStructureType' : 'SYSTEM', 'metadata' : metadata, 'entityFeeAssignmentPayloads' : entityFeeAssignmentPayloadList};
    $("#discount-structure-config-modal").modal('toggle');
    discountStructure.dataCache.discountAssignmentStructurePayload = discountAssignmentStructurePayload;
    discountStructure.createDiscountStructureTable();
  },

  createDiscountStructureTable : function() {
    var discountAssignmentStructurePayload = discountStructure.dataCache.discountAssignmentStructurePayload;
    var discountStructureHtml = "<div class=\"card-header\" style=\"padding-bottom:0px;padding-left:0px;\"><br><h5 style=\"padding-bottom:0px;color:#43a2ad;cursor:pointer;\" onclick=\"discountStructure.loadNewFeeStructureConfigureModal();\"><u>Assign Instant Discounts</u></h5></div>";
    if(discountAssignmentStructurePayload != null && discountAssignmentStructurePayload != undefined) {
      var structureName = discountAssignmentStructurePayload.metadata.title;
      var discountStructureHtml = "<br/><div class=\"card-header\" style=\"padding-bottom:0px;padding-left:0px;\"><br><h5>Instant Discount Details</h5></div><table id=\"datatables-reponsive\" class=\"table table-striped datatables-reponsive-table table-bordered\"><thead><tr><th scope=\"col\" class=\"w-50\">Structure Name</th><th scope=\"col\" colspan=\"3\">Action</th></tr></thead><tbody><tr><td id=\"\">" + structureName + "</td><td><button type=\"button\" class=\"btn btn-outline-primary btn-sm view-fee-structure-config-button\" data-target=\"#discount-structure-config-modal\" data-keyboard=\"false\" data-toggle=\"modal\" data-backdrop=\"static\" onclick=\"discountStructure.fillFeeDiscountStructureModal(true)\">View</button></td><td><button type=\"button\" class=\"btn btn-outline-warning btn-sm update-fee-structure-config-button\" data-target=\"#discount-structure-config-modal\" data-keyboard=\"false\" data-toggle=\"modal\" data-backdrop=\"static\" onclick=\"discountStructure.fillFeeDiscountStructureModal(false)\">Update</button></td><td><button type=\"button\" class=\"btn btn-outline-danger btn-sm delete-fee-structure-config-button\" data-target=\"#delete-fee-structure-modal\" data-keyboard=\"false\" data-toggle=\"modal\" data-backdrop=\"static\" onclick=\"discountStructure.populateDeleteStructureModal()\">Delete</button></td></tr></tbody></table>";
    }
    $("#assign-instant-discounts-div").html(discountStructureHtml);
  },

  getFeeStructureData : function (entityFeeStructureContainerId) {
    var feeIdFeeHeadsList = [];
    $(entityFeeStructureContainerId).find(".fee-structure").each( function () {
      var feeId = $(this).find(".fee-id").text().trim();
      var feeHeadAmountList = [];
      var feeHeads = {};
      $(this).find('tbody.fee-structure-body').find('tr').each(function () {
          var feeHeadId = $(this).find(".fee-head").find(':selected').val().trim();
          if(feeHeadId == ""){
            return;
          }
          if(feeHeadId in feeHeads){
            // Need to handle properly to display relevent error
            return;
          }
          var feeHeadAmount = $(this).find(".fee-head-amount").val();
          var isPercentage = $(this).find(".toggle-switch").is(':checked');
          if(feeHeadAmount === "" || feeHeadAmount === undefined) {
            alert("Amount cannot be empty");
            return [];
          }
          feeHeadAmountList.push({'feeHeadId' : feeHeadId, 'amount' : feeHeadAmount, 'isPercentage' : isPercentage});
      });
      if(feeHeadAmountList.length > 0){
        feeIdFeeHeadsList.push({'feeId' : feeId, 'feeHeadAmountList' : feeHeadAmountList});
      }
    });

    if(Array.isArray(feeIdFeeHeadsList) && !feeIdFeeHeadsList.length) {
      alert("Please select atleast one fees to create discount structure");
      return [];
    }

    return feeIdFeeHeadsList;
  },

  fillFeeDiscountStructureModal : function (viewOnly) {

    $("#discount-structure-name").attr("disabled",false);
    var selectedDiscountStructure = discountStructure.dataCache.discountAssignmentStructurePayload;
    discountStructure.resetDiscountStructureModal();
    $("#discount-structure-name").val(selectedDiscountStructure.metadata.title);
    $("#discount-structure-config-modal").find(".modal-title").html("Update Instant Discounts");
    $("#submit-discount-structure").html("Update Instant Discounts");
    $(".standard-select-dropdown-container").css("display","block");
    $(".fee-select-dropdown-container").css("display","block");

    discountStructure.dataCache.configureNewStructure.institute.selectedFees = {};
    var instituteEntitySelectedFees = {};
    var standradEntitySelectedFees = {};
    for(var i = 0; i < selectedDiscountStructure.entityFeeAssignmentPayloads.length; i++){
      var entityFeeAssignment = selectedDiscountStructure.entityFeeAssignmentPayloads[i];
        for(var j = 0; j < entityFeeAssignment.feeIdFeeHeadsList.length; j++){
          var feeIdFeeHeadDetails = entityFeeAssignment.feeIdFeeHeadsList[j];
          var feeId = feeIdFeeHeadDetails.feeId;
          instituteEntitySelectedFees[feeId] = feeIdFeeHeadDetails;
        }
    }


    for(feeId in instituteEntitySelectedFees){
        discountStructure.insertFeeDetailsCard(".institute-structure", discountStructure.INSTITUTE_ENTITY, feeId, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
        var feeContainerId = discountStructure.createFeeContainerId(discountStructure.INSTITUTE_ENTITY,feeId);
        for(var i = 0; i < instituteEntitySelectedFees[feeId].feeHeadAmountList.length; i++){
          var feeHeadId = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].feeHeadId;
          var feeHeadAmount = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].amount;
          var checked = instituteEntitySelectedFees[feeId].feeHeadAmountList[i].isPercentage;
          discountStructure.insertFeeHeadRow(feeContainerId, feeId, feeHeadId, feeHeadAmount, checked);
        }
    }
    discountStructure.populateSelectFeesDropdown(".institute-structure", discountStructure.INSTITUTE_ENTITY, discountStructure.dataCache.configureNewStructure.institute.selectedFees);
    if(viewOnly){
        $(".standard-select-dropdown-container").css("display","none");
        $(".fee-select-dropdown-container").css("display","none");
        $(".add-fee-head-button").css("display","none");
        $(".fee-head").attr("disabled","true");
        $(".fee-head-amount").attr("disabled","true");
        $(".toggle-switch").attr("disabled","true");
        $(".delete-fee-head-row").remove();
        $(".delete-fee-structure-row").remove();
        $(".delete-standard-row").remove();
        $("#discount-structure-config-modal").find(".modal-title").html("View Instant Discounts");
        $("#discount-structure-name").attr("disabled",true);
        $("#fee-structure-types").attr("disabled",true);
        $("#discount-structure-config-modal").find(".modal-footer").css("display","none");
    }
  },

  populateDeleteStructureModal : function (structureId) {
    $("#delete-fee-structure-modal-text").html("Do you want to delete instant discount: "+discountStructure.dataCache.discountAssignmentStructurePayload.metadata.title +"?");
  },

  deleteFeeStructure : function () {
     $("#delete-fee-structure-modal").modal('toggle');
     discountStructure.dataCache.discountAssignmentStructurePayload = null;
     discountStructure.createDiscountStructureTable();
   }

};

var studentList = {
  dataCache : {},

  initPagination: function () {
    pagination.bindEvents(
      function() {
        searchStudents(false);
      },
      function () {
        searchStudents(false);
      },
      function () {
        searchStudents(false);
      },
      function () {
        searchStudents(true);
      }
    );
  }
};


var documentPage =  {
  dataCache : {},

  generateDocument : function () {
    var documentType = $("#generate-student-document-type").find(':selected').val().trim();
    var studentId = $("#upload-document-student-id").text();
    var sessionId = academicSessionHandler.getSelectedSessionId();

    window.open(baseURL + "/admission/generate-document/"+sessionId+"/"+studentId+"/"+documentType, '_blank');
  },

  generateStaticAdmissionForm : function () {

    var documentType = "ADMISSION_FORM";
    var academicSessionId = $("#add-student-academic-session").find(':selected').val().trim();

    window.open(baseURL + "/admission/generate-static-document/"+academicSessionId+"/"+documentType, '_blank');
  }

}



var identityCards = {

  dataCache : {},

  loadMainScreen: function() {
    ajaxClient.get("/admission/identitycard", function(data) {
        $("#main-content").html(data);
        commonUtils.bindCardHoverEvent();
        commonUtils.bindCardClickEvent(".identitycard-item");
        identityCards.bindGenerateIdentityCardEvent();
    });
  },

  bindGenerateIdentityCardEvent : function () {
    $('.generate-identitycard').on('click', function () {
      $(this).closest('div.modal').modal('toggle');
      var containerElement = $(this).closest('div.identitycard-field-container');
      var academicSession = $(containerElement).find(".identitycard-academic-session option:selected").val();
      var standardId = $(containerElement).find(".identitycard-standards option:selected").val();
      if(academicSession == ""  || standardId == ""){
        showErrorDialogBox("Please select all the required fields to generate admit card")
        return
      }
      window.open(baseURL+"/admission/generate-identitycard/" + academicSession + "/"+standardId, '_blank');
    });
  }
}

var siblingDetails = {

  loadHomePage: function() {
    var status = "ENROLMENT_PENDING,ENROLLED,NSO";
    ajaxClient.get("/admission/siblings?status="+status, function(data) {
        $("#main-content").html(data);
        siblingDetails.bindSearchStudentEvent();
        siblingDetails.registerStudentSearchCallback();
        academicSessionHandler.bindSessionChangeEvent(siblingDetails.changeSession);
        initSelect2("None");
    });
  },

  changeSession : function() {
    siblingDetails.loadSiblingList();
  },

  registerStudentSearchCallback : function(){
      $('#searchStudents').on('click', function () {
          siblingDetails.loadSiblingList();
      });
      $("#searchStudentsInput").on('keyup', function (e) {
        if (e.keyCode == 13) {
          siblingDetails.loadSiblingList();
        }
      });
  },

  loadSiblingList: function() {
    var search_text = $("#searchStudentsInput").val();
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    var status = "ENROLMENT_PENDING,ENROLLED,NSO";
    if($(".student-status").val().length > 0){
          status = $(".student-status").val().join();
    } else {
      showErrorDialogBox("Please select atleast one student status to search students!")
      return;
    }
    ajaxClient.get("/admission/siblings-list/" + academicSessionId + "?text="+search_text+"&status="+status, function(data) {
        $("#sibling-list-div").html(data);
    });
  },

  bindSearchStudentEvent : function () {
    var resultArea = "#students-search-result";
    $("#students-search").on('keyup', function (e) {
      if (e.keyCode == 13) {
          siblingDetails.doneStudentSearchTyping(resultArea);
      }
    });
    liveSearchHandler.bindEvent('#students-search', resultArea, siblingDetails.doneStudentSearchTyping);
  },

  doneStudentSearchTyping: function(resultArea) {
    var searchText = $('#students-search').val().trim();
    var status = "ENROLLED";
    siblingDetails.studentLiveSearchEvent(searchText, resultArea, siblingDetails.loadStudentDetails, status);
  },

  studentLiveSearchEvent : function (searchText, resultArea, triggerMethod, status){
    var academicSessionId =  academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/admission/student-live-search/" + academicSessionId + "?searchText=" + searchText + "&status=" + status, function(data) {
        $(resultArea).html(data);
        siblingDetails.loadStudentDetails();
    });
  },

  loadStudentDetails : function () {
    $("#live-search-student-results tbody tr td").on("click", function() {
        var studentDetails = JSON.parse($(this).parent().find("td.student-info-td").text().trim());
        siblingDetails.addSelectedStudentRow(studentDetails, true, false, true);
    });
  },

  addSelectedStudentRow : function (studentDetails, showCrossButton, showCheckboxButton, newElement) {
    var name = studentDetails.name;
    var admissionNumber = studentDetails.admissionNumber;
    var fatherName = siblingDetails.checkIfStringEmpty(studentDetails.fathersName) ? "-" : studentDetails.fathersName;
    var motherName = siblingDetails.checkIfStringEmpty(studentDetails.mothersName) ? "-" : studentDetails.mothersName;
    var gender = siblingDetails.checkIfStringEmpty(studentDetails.gender) ? "-" : studentDetails.gender;
    var studentId = studentDetails.studentId;
    var trRef = $(this).parent();
    var closeButtonHTML = "";
    if(showCrossButton) {
      closeButtonHTML = " <button type=\"button\" class=\"close delete-student-row \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
    }
    var checkboxButtonHTML = "";
    if(showCheckboxButton) {
      checkboxButtonHTML = " <input type=\"checkbox\" class=\"delete-student-checkbox\" name=\"\" value=\"\"> &nbsp; &nbsp;";
    }
    var trHTML = "<tr class=\"selected_student_row " + (newElement ? "add" : "") + "\" id=\""+studentId+"\"><td class=\"student-name-td\" scope=\"row\"> " + checkboxButtonHTML + name + " </td><td class=\"student-admission-number-td\" scope=\"row\"> "+admissionNumber+" </td><td class=\"student-gender-td\" scope=\"row\"> "+gender+" </td><td class=\"student-father-name-td\" scope=\"row\"> "+fatherName+" </td><td class=\"student-mother-name-td\" scope=\"row\"> "+motherName+" </td><td>" + closeButtonHTML + "</td> </tr>";
    $("#students-search-row").before(trHTML);
    $("#students-search-result").html("");
    $("#students-search").val("");
    siblingDetails.deletePurchaseItemEntry();
  },

  deletePurchaseItemEntry : function () {
      $(".delete-student-row").click(function() {
          $(this).parent().parent().remove();
      });
  },

  checkIfStringEmpty : function (str) {
    if(str == null || !str.trim()) {
      return true;
    }
    return false;
  },

  editSiblingDetails : function (deleteSiblingCase, addingSiblingGroup) {
    var studentIdList = [];
    var error = false;
    var isStudentLevelAction = false;
    if(deleteSiblingCase) {
      $(".selected_student_row").find("input.delete-student-checkbox").each(function() {
          if(!$(this).is(":checked")) {
              return;
          }
          var studentId = $(this).parent().parent().attr("id");
          if(studentId != undefined && studentId != null) {
            studentIdList.push(studentId);
          }
      });

      if(error) {
        return;
      }
      if(studentIdList.length <= 0) {
        alert("Please select atleast one student to remove!");
        return;
      }
    } else {
      if(addingSiblingGroup) {
        $(".selected_student_row").each(function() {
          var studentId = $(this).attr("id");
          if(siblingDetails.checkIfStudentAlreadySelected(studentId, studentIdList)) {
            alert("Please remove duplicate entries from student list!");
            error = true;
            return;
          }
          studentIdList.push(studentId);
        });

        if(error) {
          return;
        }
        if(studentIdList.length <= 1) {
          alert("Please select atleast two student for sibling information!");
          return;
        }
      } else {
        $(".selected_student_row.add").each(function() {
          var studentId = $(this).attr("id");
          if(siblingDetails.checkIfStudentAlreadySelected(studentId, studentIdList)) {
            alert("Please remove duplicate entries from student list!");
            error = true;
            return;
          }
          studentIdList.push(studentId);
        });

        if(error) {
          return;
        }
        if(studentIdList.length <= 0) {
          alert("Please select atleast a student to add!");
          return;
        }
      }

    }
    var url = "add-siblings"
    var siblingGroupId = null;
    if(deleteSiblingCase) {
      url = "update-siblings"
    }
    if(deleteSiblingCase || !addingSiblingGroup) {
      siblingGroupId = $("#sibling-group-id").text();
      isStudentLevelAction = true;
    }
    var studentSiblingPayload = {"instituteId" : null, "siblingGroupId" : siblingGroupId, "studentIdList" : studentIdList};
    console.log(studentSiblingPayload);
    $("#sibling-config-modal").modal('toggle');
    ajaxClient.post("/admission/" + url + "/" + isStudentLevelAction,{'studentSiblingPayload':JSON.stringify(studentSiblingPayload)}, function(data) {
        $("#siblings-status-modal-container").html(data);
        $("#admission-status-modal").modal('toggle');
        siblingDetails.loadSiblingList();
      });
  },

  checkIfStudentAlreadySelected : function (studentId, studentIdList) {
    return (studentIdList.indexOf(studentId) > -1);
  },

  viewSiblingDetailsButton : function (ref, action) {
    $("#sibling-config-modal").modal('toggle');
    siblingDetails.fillSiblingInformation(true, ref, action);
  },

  fillSiblingInformation : function (viewOnly, ref, action) {
    siblingDetails.resetSibligDetailsPopup();
    var siblingDetail = JSON.parse($(ref).parent().find(".sibling-details-info").text().trim());
    $("#sibling-group-id").text(siblingDetail.siblingGroupId);
    var studentList = siblingDetail.studentList;
    var showCrossButton = false;
    var showCheckboxButton = (action === DELETE_SIBLINGS);
    $.each(studentList, function(key, value) {
      siblingDetails.addSelectedStudentRow(value, showCrossButton, showCheckboxButton, false);
    });
    if(viewOnly) {
      $("#students-search-row").attr("style", "display:none;");
      $("#sibling-details-modal-footer").html("<button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\" onclick=\"closeModal()\">Close</button>");
    } else {
      if(action === DELETE_SIBLINGS) {
        $("#students-search-row").attr("style", "display:none;");
        $("#sibling-details-modal-footer").html("<button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\" onclick=\"closeModal()\">Cancel</button><button type=\"button\" class=\"btn btn-primary\" onclick=\"siblingDetails.editSiblingDetails(true, false);\">Delete Siblings</button>");
      } else if(action === ADD_SIBLINGS) {
        $("#students-search-row").attr("style", "");
        $("#sibling-details-modal-footer").html("<button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\" onclick=\"closeModal()\">Cancel</button><button type=\"button\" class=\"btn btn-primary\" onclick=\"siblingDetails.editSiblingDetails(false, false);\">Add Siblings</button>");
      }
    }
  },

  updateSiblingDetailsButton : function (ref, action) {
    $("#sibling-config-modal").modal('toggle');
    siblingDetails.fillSiblingInformation(false, ref, action);
  },

  resetSibligDetailsPopup : function () {
    var modalBodyHTML = "<p id=\"sibling-group-id\" style=\"display:none;\"></p><table id=\"datatables-reponsive\" class=\"table datatables-reponsive-table\"><thead><tr><th scope=\"col\">Student Name*</th><th scope=\"col\">Admission Number</th><th scope=\"col\">Gender</th><th scope=\"col\">Father Name</th><th scope=\"col\">Mother Name</th><th scope=\"col\"></th></tr></thead><tbody><tr id=\"students-search-row\"><td width=\"30%\"><input type=\"text\" class=\"form-control\" id=\"students-search\" placeholder=\"Type Student Name to add . . .\"><div id=\"students-search-result\"></div></td><td><p></p></td><td><p></p></td><td><p></p></td><td><p></p></td></tr></tbody></table>";
    $("#sibling-details-modal-body").html(modalBodyHTML);

    var modalFooterHTML = "<button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\" onclick=\"closeModal()\">Cancel</button><button type=\"button\" class=\"btn btn-primary\" onclick=\"siblingDetails.editSiblingDetails(false, true);\">Add Siblings Details</button>";
    $("#sibling-details-modal-footer").html(modalFooterHTML);
    siblingDetails.bindSearchStudentEvent();
  },

  deleteSiblingDetailPopup : function (ref) {
    $("#delete-sibling-modal").modal('toggle');
    var modalBodyHTML = "<p id=\"delete-sibling-group-id\" style=\"display:none;\"></p><span style=\"color:red\" id=\"delete-siblings-modal-text\">Do you want to Delete Sibling Information?</span>";
    $("#delete-sibling-details-modal-body").html(modalBodyHTML);
    var modalFooterHTML = "<button type=\"button\" class=\"btn btn-success\" data-dismiss=\"modal\">Do Not Delete</button><button type=\"button\" class=\"btn btn-danger\" onclick=\"siblingDetails.deleteSiblingDetails();\">Yes, Delete Sibling Information</button>";
    $("#delete-sibling-details-modal-footer").html(modalFooterHTML);
    var siblingDetail = JSON.parse($(ref).parent().find(".sibling-details-info").text().trim());
    $("#delete-sibling-group-id").text(siblingDetail.siblingGroupId);
  },

  deleteSiblingDetails : function () {
    var siblingGroupId = $("#delete-sibling-group-id").text();
    $("#delete-sibling-modal").modal('toggle');
    ajaxClient.post("/admission/delete-sibling-details/"+siblingGroupId,{}, function(data){
      $("#siblings-status-modal-container").html(data);
      $("#admission-status-modal").modal('toggle');
      siblingDetails.loadSiblingList();
    });
  }

}
